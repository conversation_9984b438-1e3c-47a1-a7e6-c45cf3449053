# API Status Monitoring System

## Overview

The API Status Monitoring System provides real-time tracking of API health and availability, displaying status information to users and administrators.

## Features

### 1. Real-Time Status Tracking
- **Automatic Monitoring**: Tracks every API request/response
- **Multi-Version Support**: Monitors API v1, v2, and v3 independently
- **Status States**:
  - 🟢 **Online**: API is functioning normally
  - 🟡 **Degraded**: API experiencing issues (2+ consecutive failures)
  - 🔴 **Offline**: API is unavailable (3+ consecutive failures or 5min timeout)
  - ⚪ **Unknown**: No data available yet

### 2. Metrics Collection
- Total requests (success + failures)
- Success rate percentage
- Average response time
- Consecutive failures/successes
- Time since last success/failure
- Last error message and status code

### 3. User-Facing Features
- **Error Messages**: Show API status when errors occur
- **Catalog Display**: Status shown in error messages when browsing cards
- **Real-Time Updates**: Status updates automatically based on API calls

### 4. Admin Features
- **Status Dashboard**: Detailed view of all API versions
- **Metrics Display**: Request counts, success rates, response times
- **Error History**: Last error message for each API
- **Quick Access**: Available from Admin → APIs → API Status

## Architecture

### Components

#### 1. APIStatusService (`services/api_status_service.py`)
**Singleton service** that tracks API health metrics.

**Key Methods:**
```python
# Record API calls
service.record_success(version, response_time, status_code)
service.record_failure(version, error_message, status_code)

# Get status
status = service.get_status(version)  # Returns APIStatus enum
message = service.get_status_message(version)  # Returns formatted string
details = service.get_detailed_status(version)  # Returns full metrics dict

# Management
service.set_current_version(version)  # Set active API version
service.reset_metrics(version)  # Reset metrics for a version
```

**Status Thresholds:**
- `OFFLINE_THRESHOLD_FAILURES = 3` - Consecutive failures before marking offline
- `DEGRADED_THRESHOLD_FAILURES = 2` - Consecutive failures before marking degraded
- `TIMEOUT_THRESHOLD_SECONDS = 300` - Seconds without success before marking offline

#### 2. CardService Integration (`services/card_service.py`)
Automatically tracks all API calls:

```python
# Success tracking
request_start = time.time()
api_resp = await api_call()
response_time = time.time() - request_start
self.status_service.record_success("v3", response_time, 200)

# Failure tracking
self.status_service.record_failure("v3", error_msg, status_code)

# Get status for display
status_msg = card_service.get_api_status_message()
```

#### 3. Catalog Handlers (`handlers/catalog_handlers.py`)
Display status to users when errors occur:

```python
if not cards_data.get("success", False):
    error_msg = cards_data.get("error", "Unknown error")
    api_status = card_service.get_api_status_message()
    
    await callback.message.edit_text(
        f"❌ Error fetching cards\n\n"
        f"Error: {error_msg}\n\n"
        f"{api_status}"
    )
```

#### 4. Admin Handlers (`handlers/admin_handlers.py`)
Provide detailed status view for administrators:

```python
async def cb_admin_api_status(self, callback: CallbackQuery):
    status_service = get_api_status_service()
    all_statuses = status_service.get_all_statuses()
    
    # Display detailed metrics for all API versions
    for version, details in all_statuses.items():
        # Show emoji, status, metrics, errors
```

## Usage

### For Users

When browsing cards, if an API error occurs, users will see:

```
❌ Error fetching cards

Error: Connection refused

🔴 API V3: Offline

This is demo data from an external API
```

### For Administrators

1. **Access Status Dashboard:**
   - Go to Admin Panel (`/admin`)
   - Click "🔧 APIs"
   - Click "📡 API Status"

2. **View Detailed Metrics:**
   ```
   📡 API Status Monitor

   API V3 🟢 Online
   ├ Requests: 150
   ├ Success Rate: 98.67%
   ├ Avg Response: 1.23s
   ├ Last Success: 5s ago
   └ No recent errors

   API V2 🔴 Offline
   ├ Requests: 10
   ├ Success Rate: 0.0%
   ├ Avg Response: 0.0s
   ├ ⚠️ Consecutive Failures: 5
   └ Last Error: Connection refused...

   API V1 ⚪ Unknown
   ├ Requests: 0
   ├ Success Rate: 0.0%
   ├ Avg Response: 0.0s
   └ No recent errors
   ```

## Implementation Details

### Status Calculation Logic

```python
def _calculate_status(metrics):
    # No data = Unknown
    if metrics.total_requests == 0:
        return APIStatus.UNKNOWN
    
    # 3+ consecutive failures = Offline
    if metrics.consecutive_failures >= 3:
        return APIStatus.OFFLINE
    
    # 2+ consecutive failures = Degraded
    if metrics.consecutive_failures >= 2:
        return APIStatus.DEGRADED
    
    # No success in 5 minutes = Offline
    if time_since_success > 300:
        return APIStatus.OFFLINE
    
    # Success rate < 50% = Degraded
    if success_rate < 50:
        return APIStatus.DEGRADED
    
    # Otherwise = Online
    return APIStatus.ONLINE
```

### Metrics Tracking

**Success Recording:**
```python
metrics.last_success_time = time.time()
metrics.consecutive_successes += 1
metrics.consecutive_failures = 0
metrics.total_requests += 1
metrics.total_successes += 1
metrics.average_response_time = 0.7 * old_avg + 0.3 * new_time  # EMA
```

**Failure Recording:**
```python
metrics.last_failure_time = time.time()
metrics.consecutive_failures += 1
metrics.consecutive_successes = 0
metrics.total_requests += 1
metrics.total_failures += 1
metrics.last_error_message = error_msg
```

### Caching

Status calculations are cached for 10 seconds to reduce overhead:

```python
_cache_ttl = 10.0  # seconds
_status_cache = {version: (status, timestamp)}
```

## Testing

### Run Tests

```bash
# Test status service
python3 tests/test_api_status_service.py

# Test with live API
python3 tests/test_api_v3_end_to_end.py
```

### Test Results

```
✓ Initial status is UNKNOWN
✓ Status is ONLINE after successes
✓ Status message correct
✓ Detailed status correct
✓ Status is DEGRADED after 2 failures
✓ Status is OFFLINE after 3 failures
✓ Status recovered to ONLINE
✓ All statuses retrieved
✓ Metrics reset successfully
✓ CardService integration working
```

## Configuration

No configuration required! The system works automatically once integrated.

**Optional Customization:**

Edit `services/api_status_service.py` to adjust thresholds:

```python
class APIStatusService:
    OFFLINE_THRESHOLD_FAILURES = 3  # Change to 5 for more tolerance
    DEGRADED_THRESHOLD_FAILURES = 2  # Change to 3 for more tolerance
    TIMEOUT_THRESHOLD_SECONDS = 300  # Change to 600 for 10min timeout
```

## Benefits

### For Users
- **Transparency**: Know when API is having issues
- **Better UX**: Clear error messages with status information
- **Reduced Confusion**: Understand if issue is temporary or persistent

### For Administrators
- **Monitoring**: Real-time view of API health
- **Diagnostics**: Quick identification of API issues
- **Metrics**: Track API performance over time
- **Proactive**: Identify issues before users report them

### For Developers
- **Debugging**: Detailed error messages and metrics
- **Testing**: Easy to verify API integration
- **Maintenance**: Track API reliability

## Future Enhancements

Potential improvements:

1. **Persistent Storage**: Save metrics to database for historical analysis
2. **Alerts**: Send notifications when API goes offline
3. **Graphs**: Visual charts of API performance over time
4. **Health Checks**: Periodic background health checks
5. **SLA Tracking**: Track uptime percentage over time
6. **Multi-Endpoint**: Track status per endpoint, not just per version

## Troubleshooting

### Status shows OFFLINE but API works

**Cause**: Cached status or recent failures

**Solution**: 
- Wait 10 seconds for cache to expire
- Make a successful API call to update status
- Reset metrics: `service.reset_metrics("v3")`

### Status not updating

**Cause**: Status service not initialized

**Solution**: Ensure CardService is properly initialized with status service

### Metrics seem incorrect

**Cause**: Metrics persist across bot restarts

**Solution**: Restart bot or reset metrics via admin panel

---

**Status:** ✅ **PRODUCTION READY**

The API Status Monitoring System is fully implemented, tested, and ready for production use!

