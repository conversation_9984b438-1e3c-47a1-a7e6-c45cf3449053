# ✅ API v3 Testing Checklist

## 📋 Pre-Test Checklist

### 1. Tor Browser Setup

- [ ] Tor Browser is installed on your system
- [ ] Tor Browser is **currently running**
- [ ] Tor has successfully connected to the Tor network (check connection status)
- [ ] Tor Browser is running in the **background** (minimize but don't close)

**Verify Tor Connection:**

```powershell
# Check if Tor port is open (should show LISTENING)
netstat -an | findstr "9150"

# Expected output: something like:
# TCP    127.0.0.1:9150         0.0.0.0:0              LISTENING
```

### 2. Environment Configuration

- [ ] `.env` file exists in project root
- [ ] `EXTERNAL_API_VERSION=v3` (changed from v2)
- [ ] `API_V3_BASE_URL` is set correctly
- [ ] `API_V3_USERNAME` is set correctly
- [ ] `API_V3_PASSWORD` is set correctly
- [ ] `USE_SOCKS_PROXY=true` is added
- [ ] `SOCKS_URL=socks5h://127.0.0.1:9150` is added

**Verify Configuration:**

```powershell
# Check .env values
Get-Content .env | Select-String "API_V3|SOCKS|EXTERNAL_API"

# Expected output should show:
# EXTERNAL_API_VERSION=v3
# API_V3_BASE_URL="http://...onion"
# API_V3_USERNAME="..."
# API_V3_PASSWORD="..."
# USE_SOCKS_PROXY=true
# SOCKS_URL=socks5h://127.0.0.1:9150
```

### 3. Python Dependencies

- [ ] Virtual environment is activated
- [ ] `aiohttp-socks` is installed
- [ ] `beautifulsoup4` is installed

**Verify Dependencies:**

```powershell
# Activate venv
& "venv/Scripts/Activate.ps1"

# Check installed packages
pip list | Select-String "aiohttp-socks|beautifulsoup4"

# Should show:
# aiohttp-socks    X.X.X
# beautifulsoup4   X.X.X
```

If missing, install:

```powershell
pip install aiohttp-socks beautifulsoup4
```

### 4. Code Files

- [ ] `api_v3/auth/session_handler.py` exists (new file)
- [ ] `api_v3/auth/__init__.py` exists (new file)
- [ ] `api_v3/http/client.py` exists (new file)
- [ ] `api_v3/http/__init__.py` exists (new file)
- [ ] `api_v3/services/browse_service.py` is updated
- [ ] `test_api_v3_auth.py` exists (new test script)

**Verify Files:**

```powershell
# Check new files exist
Test-Path "api_v3/auth/session_handler.py"  # Should be True
Test-Path "api_v3/http/client.py"            # Should be True
Test-Path "test_api_v3_auth.py"              # Should be True
```

---

## 🧪 Testing Phase

### Test 1: Prerequisites Check

```powershell
python test_api_v3_auth.py
```

**Look for:**

```
PREREQUISITES CHECK
✅ API_V3_BASE_URL: http://...
✅ API_V3_USERNAME: ...
✅ API_V3_PASSWORD: ***
✅ USE_SOCKS_PROXY: True
✅ SOCKS_URL: socks5h://127.0.0.1:9150
✅ aiohttp-socks installed
✅ beautifulsoup4 installed
✅ Port 9150 is open (Tor likely running)
```

- [ ] All items show ✅
- [ ] No ❌ errors in prerequisites

**If any ❌ appears, fix before continuing!**

---

### Test 2: Authentication Test

The test script will automatically test authentication.

**Look for:**

```
TEST 1: Authentication
Base URL: http://...onion
Username: ...
Using SOCKS: True

[INFO] Using SOCKS proxy: socks5h://127.0.0.1:9150
[INFO] Fetching login page: http://.../login
[INFO] Attempting login...
[INFO] ✓ Login successful - session authenticated

✅ Authentication successful!
   - Session authenticated: True
```

- [ ] "Login successful" message appears
- [ ] Session authenticated: True
- [ ] No connection errors
- [ ] No authentication errors

**Common Issues:**

- ❌ "Cannot connect" → Tor not running, start Tor Browser
- ❌ "Authentication failed" → Check username/password
- ❌ "getaddrinfo failed" → SOCKS proxy not configured

---

### Test 3: Browse Service Test

The test script will automatically test browsing.

**Look for:**

```
TEST 2: Browse Service

📋 Fetching cards (no filters)...
[INFO] Starting list_items request
[DEBUG] Making GET request to http://.../shop
[DEBUG] Response 200 (12345 chars)
[INFO] Completed list_items successfully in 1.23s

✅ Browse successful!
   - Cards returned: 50
   - Total count: 100

   First card preview:
   - BIN: 555426
   - Country: UNITED STATES
   - Scheme: MASTERCARD
   - Price: 8.5
```

- [ ] "Browse successful" message appears
- [ ] Cards returned: > 0
- [ ] Card data shows correctly (BIN, Country, etc.)
- [ ] No service errors

**Common Issues:**

- ❌ "Service unavailable" → API might be down
- ❌ "Parse error" → Response format changed
- ❌ "Network error" → Connection issue

---

### Test 4: Filtered Browse Test

The test script will automatically test with filters.

**Look for:**

```
📋 Fetching cards with filters (BIN: 555426)...
✅ Filtered browse successful!
   - Cards returned: 5
```

- [ ] Filtered results returned
- [ ] Card count is reasonable
- [ ] No filter errors

---

### Test 5: Filters Test

The test script will automatically fetch available filters.

**Look for:**

```
🔍 Fetching available filters...
✅ Filters retrieved!
   - Filter count: 6
   - Available: continent[], country[], scheme[], type[], level[]
```

- [ ] Filters retrieved successfully
- [ ] Filter count > 0
- [ ] Filter names look correct

---

## 📊 Test Summary

After all tests complete, you should see:

```
TEST SUMMARY
============================================================
Authentication: ✅ PASS
Browse Service: ✅ PASS
============================================================

🎉 All tests passed! API v3 is working correctly.
```

### Final Checklist:

- [ ] All tests show ✅ PASS
- [ ] No ❌ FAIL messages
- [ ] No critical errors in logs
- [ ] "All tests passed" message appears

---

## 🚀 Production Test (Bot)

If all tests pass, run the actual bot:

```powershell
python run.py
```

### Expected Behavior:

1. **Bot Startup:**

   ```
   [INFO] Bot starting...
   [INFO] Registered API configuration: api_v3
   [INFO] API v3 adapter created successfully
   ```

2. **First API v3 Request:**

   ```
   [INFO] Using SOCKS proxy: socks5h://127.0.0.1:9150
   [INFO] Fetching login page
   [INFO] Attempting login...
   [INFO] ✓ Login successful - session authenticated
   [INFO] Starting list_items request
   [INFO] Completed list_items successfully
   ```

3. **Subsequent Requests:**
   ```
   [INFO] Starting list_items request
   [INFO] Completed list_items successfully in 0.5s
   ```
   (Notice: No login, much faster!)

### Bot Runtime Checklist:

- [ ] Bot starts without errors
- [ ] "Login successful" appears on first request
- [ ] Cards are displayed to users
- [ ] Filters work correctly
- [ ] No repeated login attempts (session is cached)
- [ ] Response time is fast (<1s after first request)

---

## 🚨 Troubleshooting Guide

### Issue: "Cannot connect to host ...onion"

**Diagnosis:**

```powershell
# Check Tor is running
netstat -an | findstr "9150"

# Should show LISTENING
```

**Solution:**

1. Open Tor Browser
2. Wait for "Connected to Tor" message
3. Keep Tor Browser running
4. Try again

---

### Issue: "Authentication failed"

**Diagnosis:**

```powershell
# Check credentials in .env
Get-Content .env | Select-String "API_V3_USERNAME|API_V3_PASSWORD"
```

**Solution:**

1. Verify credentials are correct (no typos)
2. Remove extra quotes if any
3. Test login manually in Tor Browser
4. Update .env if credentials changed

---

### Issue: "No module named 'aiohttp_socks'"

**Diagnosis:**

```powershell
pip list | Select-String "aiohttp-socks"
# Should show aiohttp-socks version
```

**Solution:**

```powershell
pip install aiohttp-socks beautifulsoup4
```

---

### Issue: "getaddrinfo failed" even with Tor running

**Diagnosis:**

- SOCKS proxy not configured correctly
- Wrong port number

**Solution:**

1. Check `.env` has: `USE_SOCKS_PROXY=true`
2. Check `.env` has: `SOCKS_URL=socks5h://127.0.0.1:9150`
3. If using Tor service (not browser), change port to 9050
4. Verify with: `netstat -an | findstr "9150"`

---

### Issue: Tests pass but bot fails

**Diagnosis:**

- Environment difference
- Different API version selected

**Solution:**

1. Check `.env` has: `EXTERNAL_API_VERSION=v3`
2. Restart bot completely
3. Check logs for actual error
4. Ensure Tor is still running

---

## 📝 Completion Checklist

### ✅ All Systems Go:

- [ ] Tor Browser is running
- [ ] All prerequisites checked (green ✅)
- [ ] Test script passed all tests
- [ ] Bot starts without errors
- [ ] First request logs "Login successful"
- [ ] Cards are displayed correctly
- [ ] Subsequent requests are fast
- [ ] No repeated errors in logs

### 🎉 Success Criteria:

```
✅ Authentication works
✅ SOCKS proxy works
✅ Session management works
✅ Cards are fetched successfully
✅ Filters work correctly
✅ Performance is good (<1s after first request)
✅ No errors in production logs
```

---

## 📞 Support

If you're still having issues after following this checklist:

1. **Check Documentation:**

   - `API_V3_AUTH_FIX.md` - Technical details
   - `API_V3_QUICK_START.md` - Quick start guide
   - `API_V3_VISUAL_GUIDE.md` - Visual reference

2. **Check Logs:**

   - Look for ERROR or WARNING messages
   - Note the exact error message
   - Check which step failed

3. **Verify Basics:**
   - Tor is running: `netstat -an | findstr "9150"`
   - Dependencies installed: `pip list`
   - Files exist: `ls api_v3/auth`, `ls api_v3/http`

---

**Last Updated:** 2025-10-03  
**Version:** 1.0  
**Status:** ✅ Ready for Testing
