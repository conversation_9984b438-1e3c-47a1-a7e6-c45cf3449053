# Quick Fix: Tor Connection Issue

## Problem

You're seeing this error:

```
[ERROR] Failed to establish a new connection: [<PERSON><PERSON>no 111] Connection refused
SOCKS proxy connection refused
```

This means **Tor is not running** on your system.

## Solution

You have **3 options** to fix this:

### Option 1: Start Tor Browser (Easiest for Testing)

1. **Download Tor Browser:**
   - Visit: https://www.torproject.org/download/
   - Download and install for your OS

2. **Start Tor Browser:**
   ```bash
   # Just open Tor Browser application
   # Wait for it to connect (green onion icon)
   # Keep it running in the background
   ```

3. **Verify it's running:**
   ```bash
   # Check if port 9150 is open
   nc -zv 127.0.0.1 9150
   # Should show: Connection to 127.0.0.1 9150 port [tcp/*] succeeded!
   ```

4. **Your .env is already configured correctly:**
   ```bash
   SOCKS_URL=socks5h://127.0.0.1:9150  # Tor Browser port
   ```

### Option 2: Install System Tor (Best for Production)

1. **Install Tor:**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install tor
   
   # macOS
   brew install tor
   ```

2. **Start Tor service:**
   ```bash
   sudo systemctl start tor
   sudo systemctl enable tor  # Start on boot
   ```

3. **Verify it's running:**
   ```bash
   sudo systemctl status tor
   # Should show: active (running)
   
   # Check if port 9050 is open
   nc -zv 127.0.0.1 9050
   ```

4. **Update your .env:**
   ```bash
   # Change from 9150 to 9050
   SOCKS_URL=socks5h://127.0.0.1:9050  # System Tor port
   ```

### Option 3: Use Non-.onion URL (If Available)

If the API has a clearnet (non-.onion) URL:

1. **Update .env:**
   ```bash
   # Change from .onion to clearnet URL
   API_V3_BASE_URL=https://clearnet-url.com  # Instead of .onion
   ```

2. **No Tor needed** - will work without SOCKS proxy

## Quick Check

Run this to see if Tor is accessible:

```bash
# Check Tor Browser port
nc -zv 127.0.0.1 9150

# Check System Tor port
nc -zv 127.0.0.1 9050

# Or use our script
python3 scripts/check_tor.py
```

## Current Status

Based on your logs:
- ✗ Tor is **NOT running** (connection refused on port 9150)
- ✓ API v3 is **correctly configured** and trying to connect
- ✓ Your credentials are loaded from .env
- ⚠️ You just need to **start Tor**

## Recommended Action

**For immediate testing:**

1. Open Tor Browser
2. Wait for it to connect
3. Keep it running
4. Restart your bot

```bash
# After starting Tor Browser:
python3 run.py
```

That's it! The bot will automatically connect through Tor.

## Verification

After starting Tor, you should see:

```
[INFO] Using SOCKS proxy: socks5h://127.0.0.1:9150
[INFO] Fetching login page: http://...onion/login
[INFO] Login page fetched successfully
[INFO] ✓ Login completed successfully
```

Instead of:

```
[ERROR] Connection refused
```

## Need Help?

See full troubleshooting guide: `docs/API_V3_TROUBLESHOOTING.md`

