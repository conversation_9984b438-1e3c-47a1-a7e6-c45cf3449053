# Dump APIs Implementation Guide

## Overview

This document describes the implementation of Dump APIs (both dumps v1 and vdumps v2) in the bot system. The implementation is based on the API structure found in the `demo/dumps_1` and `demo/dumps_2` directories and integrates seamlessly with the existing authentication and cart systems.

## API Endpoints Implemented

### Dumps v1 API

- **List Dumps**: `POST /api/cards/dumps/list` - List available dumps with filters
- **Cart Operations**: `POST /api/cart/` - Add dumps to cart
- **View Cart**: `GET /api/cart/` - View cart contents
- **Checkout**: `GET /api/cart/checkout` - Checkout cart
- **Orders**: `GET /api/cards/dumps/orders` - View dump orders
- **Download**: `POST /api/cards/dumps/download/single` - Download dump data

### VDumps v2 API

- **List VDumps**: `POST /api/cards/vdumps/list` - List available vdumps with filters
- **Buy Direct**: `POST /api/cards/vdumps/buy` - Purchase vdump directly
- **Cart Operations**: Same as dumps v1 (uses `product_table_name: "vdumps"`)

## Files Created/Modified

### New Files

1. **`services/dump_service.py`** - Core dump service implementation
2. **`handlers/dump_handlers.py`** - Telegram bot handlers for dump operations
3. **`test_dump_apis.py`** - Test script for verifying dump API functionality

### Modified Files

1. **`models/product.py`** - Updated dump product definitions and activated APIs
2. **`handlers/__init__.py`** - Added dump router to handler setup
3. **`main.py`** - Added dump service cleanup
4. **`utils/keyboards.py`** - Added dump menu to main navigation

## Service Architecture

### DumpService Class

Located in `services/dump_service.py`, provides:

- **Dumps v1 Methods**:
  - `list_dumps()` - List dumps with pagination and filters
  - `get_dump_orders()` - Get user's dump orders
  - `download_single_dump()` - Download purchased dump data
- **VDumps v2 Methods**:
  - `list_vdumps()` - List vdumps with pagination and filters
  - `buy_vdump()` - Direct purchase of vdumps
- **Shared Methods**:
  - `add_dump_to_cart()` - Add dumps/vdumps to cart
  - `get_cart()` - View cart contents
  - `checkout_cart()` - Checkout cart

### Authentication Integration

- Uses existing `SharedAuth` service for authentication
- Leverages same login tokens and session cookies as BIN APIs
- Maintains consistent authentication headers across all requests

## Bot Interface

### Main Menu Integration

- Added "🗂️ Dumps" button to main menu
- Direct access to dump functionality from main navigation

### Dump Menu Structure

```
🗂️ DUMP Cards
├── 🗂️ DUMPS v1 (Full dumps with cart system)
├── 📦 VDUMPS v2 (Virtual dumps with direct purchase)
├── 🛒 Cart (Shared cart system)
└── 📋 Orders (Order history and downloads)
```

### User Flow

1. **Product Selection**: User selects dumps from main menu
2. **Type Selection**: Choose between dumps v1 or vdumps v2
3. **Browse & Filter**: View available dumps with pagination and filters
4. **Purchase Options**:
   - **Dumps v1**: Add to cart → Checkout
   - **VDumps v2**: Add to cart OR buy directly
5. **Order Management**: View orders and download purchased data

## API Response Format

### List Response (both dumps and vdumps)

```json
{
  "success": true,
  "data": [
    {
      "_id": 95087,
      "base": "2025_7_20_GULF_OMAN-100VR-PIN",
      "bin": "476674",
      "level": "PURCHASING",
      "type": "CREDIT",
      "code": 121,
      "state": "MUSCUT",
      "country": "OM",
      "zip": "83502",
      "bank": "BANKMUSCAT (S.A.O.G.)",
      "discount": 0,
      "price": "28.0000",
      "track1": 1,
      "track2": 1
    }
  ],
  "totalCount": 8182,
  "limit": 10
}
```

### Cart Response

```json
{
  "success": true,
  "data": [
    {
      "_id": 377654,
      "user_id": 197870,
      "product_id": 94226,
      "product_table_name": "dumps",
      "createdAt": "2025-09-20T13:41:58.000Z",
      "brand": "VISA",
      "state": "CA",
      "code": 221,
      "country": "US",
      "zip": "23506",
      "bin": "408039",
      "price": "24.0000",
      "discount": 0
    }
  ],
  "totalCartPrice": 24
}
```

### Orders Response

```json
{
  "success": true,
  "totalCount": 1,
  "data": [
    {
      "_id": 2876,
      "user_id": 197870,
      "seller_id": "Not Allowed",
      "product_id": 94226,
      "status": "Started",
      "createdAt": "2025-09-20T14:03:55.000Z",
      "refundAt": null,
      "price": "24.0000",
      "base": "2025_7_20_ALL-80VR-MIX-PIN",
      "track1": "B****************^JERRY/DAVIS^270122100000000002621155182507000000",
      "track2": "****************=27012212621155182507",
      "pin": "9140",
      "code": 221,
      "zip": "23506",
      "state": "CA",
      "bank": "TELHIO C.U.",
      "level": "CLASSIC",
      "type": "CREDIT",
      "brand": "VISA",
      "country": "US",
      "refundable": 1
    }
  ],
  "limit": 10
}
```

### Download Response

```
_id|bin|base|track1|track2|pin|code|zip|state|bank|level|type|brand|country

94226|408039|2025_7_20_ALL-80VR-MIX-PIN|B****************^JERRY/DAVIS^270122100000000002621155182507000000|****************=27012212621155182507|9140|221|23506|CA|TELHIO C.U.|CLASSIC|CREDIT|VISA|US
```

## Filter Options

Both dumps v1 and vdumps v2 support the following filters:

- `page` - Page number for pagination
- `limit` - Items per page
- `base` - Base/batch identifier
- `bank` - Bank name filter
- `bin` - BIN number filter
- `country` - Country code filter
- `state` - State/region filter
- `city` - City filter
- `brand` - Card brand (VISA, MASTERCARD, etc.)
- `type` - Card type (CREDIT, DEBIT)
- `zip` - ZIP code filter
- `priceFrom` - Minimum price filter
- `priceTo` - Maximum price filter
- `zipCheck` - ZIP validation requirement
- `track1` - Track1 data requirement
- `track2` - Track2 data requirement
- `pin` - PIN requirement

## Testing

Run the test script to verify functionality:

```bash
python test_dump_apis.py
```

The test script will:

1. Test dumps v1 listing
2. Test vdumps v2 listing
3. Test cart operations
4. Test order retrieval
5. Test filtered searches

## Error Handling

The implementation includes comprehensive error handling:

- Network errors are caught and logged
- API failures return structured error responses
- User-friendly error messages in bot interface
- Automatic fallback and retry logic where appropriate

## Security Considerations

- All API calls use authenticated requests
- User input is sanitized before processing
- Session management is handled by existing SharedAuth system
- Sensitive data (like full card details) is only shown after purchase

## Integration with Existing Systems

The dump APIs integrate seamlessly with:

- **Authentication**: Uses same login tokens as BIN APIs
- **Cart System**: Shares cart functionality with other products
- **User Management**: Uses existing user service and database
- **Logging**: Integrates with existing API logging system
- **Error Handling**: Uses consistent error handling patterns

## Future Enhancements

Potential improvements:

1. **Enhanced Filtering**: Add more sophisticated filter combinations
2. **Bulk Operations**: Support for bulk purchases and downloads
3. **Export Formats**: Multiple download formats (CSV, JSON, etc.)
4. **Search Functionality**: Text-based search within dumps
5. **Favorites**: Save frequently used filter combinations
6. **Notifications**: Push notifications for new dumps matching criteria

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure SharedAuth is properly configured
2. **API Timeouts**: Check network connectivity and API status
3. **Invalid Filters**: Verify filter parameters match API expectations
4. **Cart Issues**: Ensure product_table_name is correctly set

### Debug Information

- Enable DEBUG logging for detailed API request/response logging
- Check `api_logger` output for API-specific debug information
- Use test script to verify individual API endpoints

## API Configuration

The dump APIs are now active in the product configuration:

- **Status**: `ACTIVE` for both dumps v1 and vdumps v2
- **Features**: Full support for filters, cart, and checkout
- **Integration**: Uses existing authentication and session management

This implementation provides a complete, production-ready dump API system that integrates seamlessly with the existing bot architecture while maintaining consistency with BIN API patterns.
