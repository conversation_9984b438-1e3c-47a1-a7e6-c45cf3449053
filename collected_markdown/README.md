# Demo Wallet Bot v2 - MongoDB Edition

A standalone Telegram bot for demo wallet and card purchases with MongoDB backend and scalable API architecture.

## Features

### Core Functionality

- **Wallet Management**: Virtual wallet with balance tracking and comprehensive transaction history
- **Card Catalog**: Browse and filter demo card offerings with advanced search, pagination, and sorting
- **Local Shopping Cart**: Add/remove items, quantity management, and complete checkout process with local cart functionality
- **Purchase System**: Secure demo card purchases with confirmation flow and receipt generation
- **Settings Management**: User preferences, notification settings, and data export options

### Admin Panel

- **User Management**: List, paginate, search, change roles, activate/deactivate, and export user data
- **Catalog Management**: Add, edit, and organize card inventory with category management
- **API Configuration**: Manage external API configurations with health monitoring (API 1 system with scalable architecture)
- **System Monitoring**: Real-time health checks, analytics dashboard, and performance metrics
- **Settings & Logs**: Application settings management and comprehensive audit logging

### Advanced Features

- **MongoDB Backend**: Scalable database with automatic fallback to in-memory simulation
- **Scalable API Architecture**: Shared system powering API 1 and the new API 2 (BASE 2) with room for additional integrations
- **Unique Naming System**: Clear separation between local and external operations with conflict-free naming
- **Security**: Multi-layer admin authentication, input validation, rate limiting, and audit trails
- **Error Handling**: Comprehensive error handling with user-friendly messages and detailed logging
- **Background Tasks**: Automated health monitoring, data cleanup, and retention management
- **Data Export**: User data export in multiple formats (JSON, CSV, PDF) with privacy controls
- **Compliance**: AML checks, sanctions screening, and regulatory compliance features
- **Metrics**: Prometheus-compatible monitoring with detailed performance analytics

## Requirements

- Python 3.11+
- MongoDB (optional - falls back to in-memory simulation)

## Quick Start

### 1. Automated Setup (Recommended)

```bash
# Navigate to bot_v2 directory
cd bot_v2

# Run the setup script
./setup.sh
```

The setup script will:

- Check Python version (3.11+ required)
- Create a proper virtual environment
- Install all dependencies
- Create .env file from template
- Run verification tests

### 2. Manual Setup (Alternative)

```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Copy example configuration
cp config.example.env .env
```

### 3. Configuration

Edit the `.env` file with your settings:

```bash
# Required: Get your bot token from @BotFather on Telegram
BOT_TOKEN=your_bot_token_here

# Optional: Set admin user IDs (comma-separated)
ADMIN_USER_IDS=123456789,987654321

# Database: Use MongoDB or SQLite simulation
USE_MONGODB=false  # Set to true for MongoDB
MONGODB_URL=mongodb://localhost:27017  # If using MongoDB
```

### 4. Run the Bot

```bash
# Using the run script (recommended)
./run.sh

# Or manually
source venv/bin/activate && python run.py
```

## API v3 Integration

The bot now supports the HTML-based **API v3** workflow (forms, CSRF tokens, Tor proxy).

Update your `.env` with the following keys:

```bash
EXTERNAL_API_VERSION=v3
EXTERNAL_V3_BASE_URL=https://your-onion-or-host
EXTERNAL_V3_USERNAME=api_username
EXTERNAL_V3_PASSWORD=api_password
EXTERNAL_V3_USE_TOR_PROXY=true          # disables if running on clearnet
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9050
EXTERNAL_V3_DEFAULT_BINS=405621          # optional default shop filter
EXTERNAL_V3_SESSION_PATH=storage/api_v3/session_cookies.json
EXTERNAL_V3_TIMEOUT=60
EXTERNAL_V3_VERIFY_TLS=false             # set true for HTTPS endpoints
```

> The client persists session cookies under `storage/api_v3/`. Ensure the path is writable by the bot process. When pointing at an `.onion` host leave `EXTERNAL_V3_USE_TOR_PROXY=true` so requests route through Tor.

## Configuration Options

Edit `.env` file with the following options:

### Required

- `BOT_TOKEN`: Telegram Bot API token from @BotFather

### Optional

- `DEMO_API_BASE`: Demo API base URL (default: https://demo.api.example)
- `DEMO_API_TOKEN`: Demo API authentication token
- `USE_MONGODB`: Enable MongoDB (default: false, uses in-memory simulation)
- `MONGODB_URL`: MongoDB connection URL (default: mongodb://localhost:27017)
- `DATABASE_NAME`: Database name (default: demo_wallet_bot)

### Wallet Settings

- `INITIAL_BALANCE`: Starting balance for new users (default: 100.00)
- `DEFAULT_CURRENCY`: Default currency (default: USD)

### Compliance

- `SANCTIONED_COUNTRIES`: Comma-separated country codes (default: CU,IR,KP,SY,UA-CRIMEA)
- `AML_HOURLY_LIMIT`: Hourly transaction limit (default: 200)
- `DAILY_SPEND_CAP`: Daily spending limit (default: 500)
- `MONTHLY_SPEND_CAP`: Monthly spending limit (default: 2000)

### Rate Limiting

- `PURCHASES_PER_MINUTE`: Purchase rate limit (default: 3)
- `PURCHASES_PER_DAY`: Daily purchase limit (default: 50)
- `SEARCHES_PER_MINUTE`: Search rate limit (default: 10)
- `MESSAGES_PER_MINUTE`: Generic message rate limit (default: 10)
- `CALLBACKS_PER_MINUTE`: Generic callback rate limit (default: 20)

### System

- `LOG_LEVEL`: Logging level (default: INFO)
- `METRICS_ENABLED`: Enable metrics collection (default: true)
- `RETENTION_ENABLED`: Enable data cleanup (default: true)
- `RETENTION_DAYS`: Data retention period (default: 45)

## MongoDB Setup (Optional)

If you want to use MongoDB instead of the in-memory simulation:

1. Install MongoDB locally or use a cloud service
2. Set `USE_MONGODB=true` in your `.env` file
3. Configure `MONGODB_URL` if using a non-default connection

## Admin Setup

To enable admin features, add your Telegram user ID to the `ADMIN_USER_IDS` setting:

```env
ADMIN_USER_IDS=123456789,987654321

Admin commands and callbacks:
- `/admin` to open the panel
- From the panel: Users, Statistics, Logs, Settings
- Users list supports pagination, search, per-user manage, and CSV export
- Settings supports listing, adding, and editing key/value pairs
- Roles management: view roles and edit permissions list
- Optional admin passphrase: set `ADMIN_PASSPHRASE` to require unlock; session lasts 30 minutes

Legacy API admin (callbacks starting with `admin:apis`) has been removed to reduce duplication. Use the built‑in API Configuration section in the admin panel.
```

## Development

### Project Structure

```
bot_v2/
├── config/          # Configuration management
├── database/        # Database connection and management
├── handlers/        # Telegram bot message handlers
├── middleware/      # Bot middleware (rate limiting, error handling)
├── models/          # Data models
├── services/        # Business logic services
├── utils/           # Utility functions
├── tests/           # Test suite
├── docs/            # Documentation
├── main.py          # Application entry point
├── run.py           # Standalone runner script
├── requirements.txt # Python dependencies
└── README.md        # This file
```

### Running Tests

The repository includes async integration scripts for manual validation:

```bash
python test_admin_api_config_integration.py
python test_api_config_system.py
python test_checkout_queue.py
python test_external_cart_integration.py
```

If you prefer pytest, add pytest-asyncio wrappers or run the scripts above directly.

### Useful Bot Commands

- `/start` – Initialize and create wallet
- `/balance` – Show current wallet balance
- `/help` – Show available commands
- `/version` – Show bot and library versions
- `/health` – Run a quick health check (DB ping)
- `/delete_me` – Delete your demo data (confirmation required)

```

## Security Notes

⚠️ **Important Security Information**

- This bot is for **demonstration purposes only**
- All card data is fake/demo data
- **Never use with real financial data or production systems**

### Security Features

- Environment variable-based configuration
- Secure encryption for sensitive API data
- Rate limiting and abuse protection
- Input validation and sanitization
- Comprehensive error handling
- Security logging and monitoring

### Security Best Practices

1. **Environment Variables**: Never commit `.env` files with real credentials
2. **Bot Token**: Keep your Telegram bot token secure and rotate regularly
3. **Database**: Use strong authentication for MongoDB in production
4. **Admin Access**: Limit admin user IDs to trusted users only
5. **Monitoring**: Enable logging and monitor for suspicious activity
6. **Updates**: Keep dependencies updated for security patches

### Production Deployment

For production use:
- Set strong `API_ENCRYPTION_KEY` and `API_ENCRYPTION_SALT`
- Use HTTPS for all external API calls
- Implement proper backup and disaster recovery
- Regular security audits and penetration testing
- Monitor logs for security events

## Support

This is a standalone version of the Demo Wallet Bot v2. All dependencies are contained within this directory.
```
