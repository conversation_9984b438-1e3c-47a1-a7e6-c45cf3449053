# API v3 Integration Guide

## Quick Integration

### 1. Add to existing imports

```python
# In your handlers or services
from api_v3 import get_api_v3_browse_service, APIV3BrowseParams
from api_v3.adapter import get_api_v3_adapter
```

### 2. Configure environment

Add to `.env`:

```env
# Use existing variables
BASE_URL=https://your-api.com
USERNAME=your_username
PASSWORD=your_password

# Or API v3 specific
API_V3_BASE_URL=https://your-api.com
API_V3_USERNAME=your_username
API_V3_PASSWORD=your_password
```

### 3. Use in product service

Add API v3 to product types:

```python
# In services/product_service.py
SUPPORTED_API_TYPES = {
    "api_v1": {...},
    "api_v2": {...},
    "api_v3": {
        "name": "API v3",
        "description": "Form-based authentication with table responses",
        "requires_auth": True,
        "supports_filters": True,
    }
}
```

### 4. Use in card service

Add API v3 browse method:

```python
# In services/card_service.py
async def browse_cards_v3(self, filters, page=1, user_id=None):
    """Browse cards using API v3"""
    from api_v3.adapter import get_api_v3_adapter

    adapter = get_api_v3_adapter()
    return await adapter.browse_cards(
        filters=filters,
        page=page,
        user_id=user_id
    )
```

### 5. Update catalog handlers

Add API v3 support to catalog handlers:

```python
# In handlers/catalog_handlers.py
async def handle_browse_request(callback: CallbackQuery, state: FSMContext):
    # Get user's selected API
    user_api = await get_user_api(callback.from_user.id)

    if user_api == "api_v3":
        # Use API v3
        from api_v3.adapter import get_api_v3_adapter
        adapter = get_api_v3_adapter()
        result = await adapter.browse_cards(
            filters=user_filters,
            page=page,
            user_id=str(callback.from_user.id)
        )
    else:
        # Use existing API v1/v2
        result = await existing_browse_method()
```

## Complete Integration Example

```python
"""
Complete example showing API v3 integration in catalog handlers
"""

from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

from api_v3.adapter import get_api_v3_adapter
from utils.keyboards import create_card_grid_keyboard

router = Router()


@router.callback_query(F.data == "browse_cards_v3")
async def browse_cards_v3_handler(callback: CallbackQuery, state: FSMContext):
    """Browse cards using API v3"""

    # Get adapter
    adapter = get_api_v3_adapter()

    # Get user filters from state
    data = await state.get_data()
    filters = data.get("filters", {})
    page = data.get("page", 1)

    # Browse cards
    result = await adapter.browse_cards(
        filters=filters,
        page=page,
        limit=20,
        user_id=str(callback.from_user.id)
    )

    if result["success"]:
        cards = result["data"]
        total = result["totalCount"]

        # Create keyboard
        keyboard = create_card_grid_keyboard(
            cards=cards,
            page=page,
            total=total
        )

        # Send message
        await callback.message.edit_text(
            f"📋 Found {total} cards (Page {page})\\n\\n"
            f"Select a card to view details:",
            reply_markup=keyboard
        )
    else:
        await callback.message.edit_text(
            f"❌ Error: {result['error']}\\n\\n"
            f"Please try again or contact support."
        )
```

## Filter Integration

```python
"""
Example of filter management with API v3
"""

@router.callback_query(F.data == "get_filters_v3")
async def get_filters_v3_handler(callback: CallbackQuery):
    """Get available filters from API v3"""

    # Get adapter
    adapter = get_api_v3_adapter()

    # Get filters
    result = await adapter.get_filters(
        user_id=str(callback.from_user.id)
    )

    if result["success"]:
        filters = result["filters"]

        # Create filter menu
        keyboard = create_filter_menu_keyboard(filters)

        await callback.message.edit_text(
            "🔍 Available Filters:\\n\\n"
            "Select a filter category:",
            reply_markup=keyboard
        )
    else:
        await callback.message.edit_text(
            f"❌ Error loading filters: {result['error']}"
        )
```

## Testing Integration

```python
"""
Test API v3 integration
"""

import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_api_v3_browse_integration():
    """Test browsing cards with API v3"""

    # Mock adapter
    with patch("api_v3.adapter.get_api_v3_adapter") as mock_adapter:
        adapter = AsyncMock()
        adapter.browse_cards = AsyncMock(return_value={
            "success": True,
            "data": [
                {"id": "1", "bin": "555426", "price": "8.5"},
                {"id": "2", "bin": "411111", "price": "12.5"},
            ],
            "totalCount": 2,
        })
        mock_adapter.return_value = adapter

        # Test browse
        result = await adapter.browse_cards(
            filters={"country": "UNITED STATES"},
            user_id="test_user"
        )

        assert result["success"] is True
        assert len(result["data"]) == 2
```

## Migration Checklist

- [ ] Add API v3 imports to required modules
- [ ] Update `.env` with API v3 credentials
- [ ] Add API v3 to product service types
- [ ] Create API v3 browse method in card service
- [ ] Update catalog handlers with API v3 support
- [ ] Add API v3 filter management
- [ ] Update admin panel with API v3 configuration
- [ ] Add API v3 selection in user settings
- [ ] Test API v3 browsing functionality
- [ ] Test API v3 filter functionality
- [ ] Update documentation
- [ ] Add API v3 examples
- [ ] Run integration tests
- [ ] Deploy to production

## Common Issues

### Issue 1: Import errors

**Solution**: Ensure `api_v3` package is in Python path

```python
import sys
sys.path.append('/path/to/bot_v2')
```

### Issue 2: Configuration not found

**Solution**: Check `.env` file has required variables

```bash
# Check environment
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print(os.getenv('BASE_URL'))"
```

### Issue 3: Authentication fails

**Solution**: Verify credentials and URL

```python
# Test configuration
from api_v3.config import create_api_v3_configuration

config = create_api_v3_configuration()
print(f"Base URL: {config.base_url}")
print(f"Username: {config.username}")
```

### Issue 4: Filters not working

**Solution**: Check filter format conversion

```python
# Debug filter conversion
from api_v3.adapter import get_api_v3_adapter

adapter = get_api_v3_adapter()
params = adapter._convert_filters_to_params({"country": "US"})
print(f"Converted params: {params.to_query_params()}")
```

## Next Steps

1. **Review**: Read `api_v3/README.md` for detailed documentation
2. **Test**: Run `examples/api_v3_quickstart.py` to test functionality
3. **Integrate**: Follow this guide to integrate into your system
4. **Monitor**: Check logs and metrics for API v3 performance
5. **Optimize**: Adjust configuration based on usage patterns

## Support

For issues or questions:

- Check `api_v3/README.md` for documentation
- Review `API_V3_IMPLEMENTATION_SUMMARY.md` for details
- Run `tests/test_api_v3.py` to verify functionality
- Check logs in `logs/` directory
