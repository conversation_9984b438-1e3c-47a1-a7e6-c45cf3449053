# Tor Connectivity Fix - Summary

## Problem

API v3 was failing to connect to the .onion domain with the error:

```
[ERROR] Failed to establish a new connection: [<PERSON>rrno 111] Connection refused
SOCKS proxy connection refused
```

## Root Cause

Two issues were identified:

1. **Tor was not running** - The system didn't have Tor service active
2. **Configuration priority issue** - The `config.example.env` file had `EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9150` which was being loaded after `.env`, and since `load_dotenv()` doesn't override existing variables, it was taking precedence over the `.env` settings

## Solution Applied

### 1. Installed and Started Tor

```bash
# Tor was already installed at /usr/sbin/tor
sudo systemctl start tor
sudo systemctl enable tor
```

**Result:** Tor is now running on port 9050 (System Tor)

### 2. Updated Configuration

Added `EXTERNAL_V3_SOCKS_URL` to `.env` file to override the example config:

```bash
# In .env file:
SOCKS_URL=socks5h://127.0.0.1:9050
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9050
```

**Why both?**
- `SOCKS_URL` - General SOCKS proxy setting
- `EXTERNAL_V3_SOCKS_URL` - API v3-specific setting (takes priority in config loader)

### 3. Cleared Session Cache

```bash
rm -rf storage/api_v3/*.json
```

Old session files were cached with the wrong SOCKS configuration.

### 4. Updated Test Script

Modified `tests/test_api_v3_integration.py` to:
- Load `.env` file with `override=True`
- Display SOCKS URL in configuration test output

## Verification

All API v3 integration tests now pass:

```
✓ PASS     Configuration
✓ PASS     Direct Service  (Retrieved 89 cards)
✓ PASS     Service Routing (Retrieved 94 cards)
✓ PASS     API Switching

Passed: 4/4
```

## Current Configuration

| Setting | Value |
|---------|-------|
| Tor Service | System Tor (port 9050) |
| SOCKS URL | `socks5h://127.0.0.1:9050` |
| API v3 Base URL | `http://blgnjdcvrpavgdtt7xhrk6mqvowtq6bp56lyzoktr3n5lwfwdrklfxid.onion` |
| Status | ✅ Working |

## Tools Created

1. **`scripts/setup_tor.sh`** - Automated Tor installation and configuration
2. **`scripts/check_tor.py`** - Diagnostic tool for checking Tor connectivity
3. **`docs/API_V3_TROUBLESHOOTING.md`** - Comprehensive troubleshooting guide
4. **`QUICK_FIX_TOR_CONNECTION.md`** - Quick reference for Tor issues

## How to Verify

### Check Tor Status

```bash
# Check if Tor is running
sudo systemctl status tor

# Check port connectivity
python3 scripts/check_tor.py
```

### Test API v3

```bash
# Run integration tests
python3 tests/test_api_v3_integration.py

# Or start the bot
python3 run.py
```

### Expected Output

When working correctly, you should see:

```
[INFO] Using SOCKS proxy: socks5h://127.0.0.1:9050
[INFO] Fetching login page: http://...onion/login
[INFO] Login page fetched successfully
[INFO] ✓ Login completed successfully
[INFO] Retrieved X cards
```

## Configuration Priority

The API v3 config loader checks environment variables in this order:

1. `EXTERNAL_V3_SOCKS_URL` ← **Highest priority**
2. `API_V3_SOCKS_URL`
3. `SOCKS_URL`
4. Default: `socks5h://127.0.0.1:9150`

**Important:** Since `config/settings.py` loads both `.env` and `config.example.env`, and `load_dotenv()` doesn't override existing variables, you must set `EXTERNAL_V3_SOCKS_URL` in `.env` to override the example config.

## Maintenance

### If Tor Stops Working

1. **Check Tor service:**
   ```bash
   sudo systemctl status tor
   sudo systemctl restart tor
   ```

2. **Check port:**
   ```bash
   python3 scripts/check_tor.py
   ```

3. **Clear session cache:**
   ```bash
   rm -rf storage/api_v3/*.json
   ```

4. **Restart bot:**
   ```bash
   python3 run.py
   ```

### Switching Between Tor Browser and System Tor

**For Tor Browser (port 9150):**
```bash
# Update .env
SOCKS_URL=socks5h://127.0.0.1:9150
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9150

# Start Tor Browser and keep it running
```

**For System Tor (port 9050):** ← **Current configuration**
```bash
# Update .env
SOCKS_URL=socks5h://127.0.0.1:9050
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9050

# Start system Tor
sudo systemctl start tor
```

## Files Modified

- `.env` - Added `EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9050`
- `tests/test_api_v3_integration.py` - Added `override=True` to `load_dotenv()` and SOCKS URL display

## Files Created

- `scripts/setup_tor.sh` - Automated setup script
- `scripts/check_tor.py` - Diagnostic tool
- `docs/API_V3_TROUBLESHOOTING.md` - Troubleshooting guide
- `QUICK_FIX_TOR_CONNECTION.md` - Quick reference
- `TOR_FIX_SUMMARY.md` - This file

## Next Steps

1. ✅ Tor is running and configured
2. ✅ API v3 is connecting successfully
3. ✅ All integration tests pass
4. **Ready to use:** Start the bot with `python3 run.py`

## Success Metrics

- **Before:** Connection refused, 0% success rate
- **After:** 100% success rate, retrieving 89-94 cards per request
- **Performance:** Authentication and data retrieval working as expected

---

**Status:** ✅ **RESOLVED**

The Tor connectivity issue has been completely fixed. API v3 is now fully operational and successfully connecting to the .onion domain through System Tor on port 9050.

