#!/usr/bin/env python3
"""
Debug script to test CSRF token extraction from cart page.
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(override=True)

from api_v3.services.cart_service import APIV3CartService
from api_v3.config.api_config import get_api_v3_config_from_env

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def debug_csrf_token_extraction():
    """Debug CSRF token extraction from cart page"""
    logger.info("🔍 Debugging CSRF Token Extraction")
    logger.info("=" * 50)
    
    cart_service = None
    try:
        # Get API v3 configuration
        config = get_api_v3_config_from_env()
        if not config:
            logger.error("❌ API v3 configuration not found")
            return False
        
        # Initialize API v3 cart service
        cart_service = APIV3CartService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )
        
        logger.info("📋 Step 1: Get cart page for CSRF token...")
        cart_response = await cart_service.client.get(
            endpoint="cart",
            params={},
            timeout=30,
        )
        
        logger.info(f"Cart response success: {cart_response.get('success')}")
        
        if cart_response.get("success"):
            cart_data = cart_response.get("data", {})
            logger.info(f"Cart data keys: {list(cart_data.keys())}")
            
            # Check payload
            payload = cart_data.get("payload", {})
            logger.info(f"Payload keys: {list(payload.keys())}")
            
            token = payload.get("_token")
            if token:
                logger.info(f"✅ CSRF token found: {token[:20]}...")
            else:
                logger.warning("⚠️ No CSRF token in payload")
                
                # Try direct extraction from HTML
                logger.info("🔍 Attempting direct HTML extraction...")
                
                # Make a raw request to get HTML
                session = cart_service.client.session_manager.get_session()
                url = cart_service.client._build_url("cart")
                response = session.get(url, timeout=30)
                
                if response.status_code == 200:
                    html = response.text
                    logger.info(f"HTML length: {len(html)}")
                    
                    # Check if _token is in HTML
                    if "_token" in html:
                        logger.info("✅ _token found in HTML")
                        
                        # Try manual extraction
                        import re
                        csrf_match = re.search(r'name="_token"[^>]*value="([^"]*)"', html)
                        if csrf_match:
                            manual_token = csrf_match.group(1)
                            logger.info(f"✅ Manual token extraction: {manual_token[:20]}...")
                        else:
                            logger.warning("⚠️ Manual regex extraction failed")
                            
                            # Try BeautifulSoup extraction
                            from bs4 import BeautifulSoup
                            soup = BeautifulSoup(html, "html.parser")
                            token_input = soup.find("input", {"name": "_token"})
                            if token_input and token_input.get("value"):
                                bs_token = token_input.get("value").strip()
                                logger.info(f"✅ BeautifulSoup token extraction: {bs_token[:20]}...")
                            else:
                                logger.warning("⚠️ BeautifulSoup extraction failed")
                                
                                # Show first 1000 chars of HTML for debugging
                                logger.info("📄 HTML preview:")
                                logger.info(html[:1000])
                    else:
                        logger.warning("⚠️ _token not found in HTML")
                        logger.info("📄 HTML preview:")
                        logger.info(html[:1000])
                else:
                    logger.error(f"❌ Failed to get HTML: {response.status_code}")
        else:
            logger.error(f"❌ Failed to get cart page: {cart_response.get('error')}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}", exc_info=True)
        return False
    finally:
        if cart_service:
            await cart_service.close()


async def main():
    """Run CSRF token debugging"""
    logger.info("🚀 Starting CSRF Token Debug")
    logger.info("=" * 60)
    
    success = await debug_csrf_token_extraction()
    
    if success:
        logger.info("✅ Debug completed successfully")
    else:
        logger.error("❌ Debug failed")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
