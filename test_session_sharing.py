"""
Test Session Sharing Performance

Verifies that all services share the same session and authentication is fast.
"""

import asyncio
import logging
import os
import time
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
import sys

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams
from api_v3.services.cart_service import APIV3CartService
from api_v3.services.order_service import APIV3OrderService

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_session_sharing():
    """Test that all services share the same session (fast initialization)"""

    # Load config
    env_file = project_root / ".env"
    if not env_file.exists():
        env_file = project_root / "config.production.env"

    load_dotenv(env_file)

    base_url = os.getenv("API_V3_BASE_URL")
    username = os.getenv("API_V3_USERNAME")
    password = os.getenv("API_V3_PASSWORD")
    use_socks = os.getenv("API_V3_USE_SOCKS_PROXY", "true").lower() == "true"
    socks_url = os.getenv("SOCKS_URL", "socks5h://127.0.0.1:9150")

    logger.info("=" * 80)
    logger.info("🧪 API v3 Session Sharing Performance Test")
    logger.info("=" * 80)
    logger.info(f"Base URL: {base_url}")
    logger.info(f"Username: {username}")
    logger.info(f"SOCKS Proxy: {socks_url if use_socks else 'Disabled'}")
    logger.info("=" * 80)

    # Test 1: Initialize first service (will authenticate)
    logger.info("\n" + "=" * 80)
    logger.info("TEST 1: Initialize Browse Service (First Login)")
    logger.info("=" * 80)

    start_time = time.time()
    browse_service = APIV3BrowseService(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=use_socks,
        socks_url=socks_url,
    )

    # Make a request to trigger authentication
    params = APIV3BrowseParams(page=1)
    result1 = await browse_service.list_items(params)
    time1 = time.time() - start_time

    if result1.success:
        cards = result1.data.get("data", []) if result1.data else []
        logger.info(f"✅ Browse service initialized in {time1:.2f}s")
        logger.info(f"   Found {len(cards)} items")
    else:
        logger.error(f"❌ Browse service failed: {result1.error}")
        return

    # Test 2: Initialize second service (should reuse session - FAST!)
    logger.info("\n" + "=" * 80)
    logger.info("TEST 2: Initialize Cart Service (Should Reuse Session)")
    logger.info("=" * 80)

    start_time = time.time()
    cart_service = APIV3CartService(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=use_socks,
        socks_url=socks_url,
    )

    # Make a request
    result2 = await cart_service.view_cart()
    time2 = time.time() - start_time

    if result2.get("success"):
        logger.info(f"✅ Cart service initialized in {time2:.2f}s")
    else:
        logger.info(f"⚠️  Cart service response: {result2}")

    # Test 3: Initialize third service (should also reuse session - FAST!)
    logger.info("\n" + "=" * 80)
    logger.info("TEST 3: Initialize Order Service (Should Reuse Session)")
    logger.info("=" * 80)

    start_time = time.time()
    order_service = APIV3OrderService(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=use_socks,
        socks_url=socks_url,
    )

    # Just initialize without making a request to test client creation speed
    time3 = time.time() - start_time
    logger.info(f"✅ Order service initialized in {time3:.2f}s")

    # Test 4: Make multiple requests with first service (should be fast)
    logger.info("\n" + "=" * 80)
    logger.info("TEST 4: Make Multiple Browse Requests (Session Reuse)")
    logger.info("=" * 80)

    start_time = time.time()
    for i in range(3):
        params = APIV3BrowseParams(page=1)
        result = await browse_service.list_items(params)
        if result.success:
            cards = result.data.get("data", []) if result.data else []
            logger.info(f"  Request {i+1}: ✅ {len(cards)} items")
        else:
            logger.warning(f"  Request {i+1}: ⚠️ {result.error}")
    time4 = time.time() - start_time
    logger.info(f"✅ 3 requests completed in {time4:.2f}s (avg: {time4/3:.2f}s)")

    # Performance Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 Performance Summary")
    logger.info("=" * 80)
    logger.info(f"First service (with login):  {time1:.2f}s")
    logger.info(f"Second service (reuse):      {time2:.2f}s")
    logger.info(f"Third service (reuse):       {time3:.2f}s")
    logger.info(f"3 subsequent requests:       {time4:.2f}s (avg: {time4/3:.2f}s)")
    logger.info("=" * 80)

    # Check if session reuse is working
    if time2 < time1 / 2 and time3 < 1.0:
        logger.info("✅ SESSION SHARING IS WORKING! Services are fast!")
        logger.info("   Second and third services are significantly faster.")
    else:
        logger.warning("⚠️  Session sharing may not be working optimally.")
        logger.warning(f"   Expected second service < {time1/2:.2f}s, got {time2:.2f}s")

    logger.info("\n" + "=" * 80)
    logger.info("🎉 Test Complete!")
    logger.info("=" * 80)

    # Cleanup
    await browse_service.close()
    await cart_service.close()
    await order_service.close()


if __name__ == "__main__":
    asyncio.run(test_session_sharing())
