#!/usr/bin/env python3
"""
Debug Check Order API Implementation

This script investigates the check_order API v1 implementation issues.
"""

import asyncio
import sys
import os
from typing import Dict, Any
sys.path.insert(0, '.')

async def test_check_order_configuration():
    """Test the check_order configuration and routing"""
    print("🔍 DEBUGGING CHECK ORDER CONFIGURATION")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        from config.settings import get_settings
        
        # Initialize service
        settings = get_settings()
        api_version = getattr(settings, "EXTERNAL_API_VERSION", "v2").lower()
        print(f"📋 Current API Version: {api_version}")
        
        external_api = ExternalAPIService()
        print(f"📋 Service API Version: {external_api.api_version}")
        
        # Get API configuration
        config = await external_api._get_api_config()
        if config:
            print(f"📋 API Config Found:")
            print(f"   • Base URL: {config.base_url}")
            print(f"   • Service Name: {getattr(config, 'service_name', 'Unknown')}")
            
            # Check if check_order endpoint is configured
            endpoints = getattr(config, 'endpoints', {})
            if 'check_order' in endpoints:
                check_endpoint = endpoints['check_order']
                print(f"   • Check Order Endpoint: {check_endpoint}")
            else:
                print(f"   ❌ No check_order endpoint configured")
                print(f"   📋 Available endpoints: {list(endpoints.keys())}")
        else:
            print(f"❌ No API configuration found")
        
        return config is not None
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_check_order_url_construction():
    """Test URL construction for check_order"""
    print(f"\n🔧 TESTING CHECK ORDER URL CONSTRUCTION")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        
        external_api = ExternalAPIService()
        config = await external_api._get_api_config()
        
        if not config:
            print("❌ No configuration available")
            return False
        
        # Test URL construction
        test_order_id = 325461
        expected_url = f"{config.base_url}/cards/hq/check"
        
        print(f"📋 URL Construction Test:")
        print(f"   • Base URL: {config.base_url}")
        print(f"   • Endpoint: /cards/hq/check")
        print(f"   • Full URL: {expected_url}")
        print(f"   • Order ID: {test_order_id}")
        print(f"   • Payload: {{'id': {test_order_id}}}")
        
        # Compare with demo reference
        demo_url = "https://ronaldo-club.to/api/cards/hq/check"
        print(f"\n📋 Demo Reference:")
        print(f"   • Demo URL: {demo_url}")
        print(f"   • Match: {'✅' if expected_url == demo_url else '❌'}")
        
        if expected_url != demo_url:
            print(f"   ⚠️ URL mismatch detected!")
            print(f"   Expected: {demo_url}")
            print(f"   Actual: {expected_url}")
        
        return expected_url == demo_url
        
    except Exception as e:
        print(f"❌ Error testing URL construction: {e}")
        return False

async def test_check_order_headers():
    """Test headers for check_order"""
    print(f"\n📋 TESTING CHECK ORDER HEADERS")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService, APIOperation
        
        external_api = ExternalAPIService()
        config = await external_api._get_api_config()
        
        if not config:
            print("❌ No configuration available")
            return False
        
        # Build headers
        headers = external_api._build_headers(config, APIOperation.CHECK_ORDER)
        
        print(f"📋 Generated Headers:")
        for key, value in headers.items():
            # Don't print sensitive values
            if 'token' in key.lower() or 'auth' in key.lower():
                print(f"   • {key}: [REDACTED]")
            else:
                print(f"   • {key}: {value}")
        
        # Compare with demo reference
        demo_headers = {
            "accept": "application/json, text/plain, */*",
            "content-type": "application/json",
            "referer": "https://ronaldo-club.to/orders/cards/hq",
            "origin": "https://ronaldo-club.to",
        }
        
        print(f"\n📋 Demo Reference Headers:")
        for key, value in demo_headers.items():
            print(f"   • {key}: {value}")
        
        # Check critical headers
        critical_checks = [
            ("content-type", "application/json"),
            ("accept", "application/json"),
        ]
        
        print(f"\n📋 Critical Header Checks:")
        all_good = True
        for header_name, expected_value in critical_checks:
            actual_value = headers.get(header_name, "")
            match = expected_value in actual_value if expected_value else actual_value != ""
            status = "✅" if match else "❌"
            print(f"   {status} {header_name}: {actual_value}")
            if not match:
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error testing headers: {e}")
        return False

async def test_api_version_routing():
    """Test if check_order needs API version routing"""
    print(f"\n🔄 TESTING API VERSION ROUTING")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        
        # Test with different API versions
        api_versions = ["v1", "v2", "v3"]
        
        for version in api_versions:
            print(f"\n📋 Testing API {version}:")
            
            external_api = ExternalAPIService(api_version=version)
            print(f"   • Service initialized with API {external_api.api_version}")
            
            # Check if check_order method has version routing
            import inspect
            check_order_source = inspect.getsource(external_api.check_order)
            
            has_v3_routing = "api_version" in check_order_source and "v3" in check_order_source
            print(f"   • Has API v3 routing: {'✅' if has_v3_routing else '❌'}")
            
            if not has_v3_routing and version == "v3":
                print(f"   ⚠️ API v3 configured but check_order lacks v3 routing!")
        
        # Compare with other methods that have routing
        methods_with_routing = ["list_items", "add_to_cart", "view_cart"]
        print(f"\n📋 Methods with API version routing:")
        
        external_api = ExternalAPIService()
        for method_name in methods_with_routing:
            method = getattr(external_api, method_name)
            method_source = inspect.getsource(method)
            has_routing = "api_version" in method_source and "v3" in method_source
            print(f"   • {method_name}: {'✅' if has_routing else '❌'}")
        
        print(f"\n🎯 CONCLUSION: check_order method lacks API version routing like other methods")
        return True
        
    except Exception as e:
        print(f"❌ Error testing API version routing: {e}")
        return False

async def test_demo_comparison():
    """Compare current implementation with demo"""
    print(f"\n📊 DEMO COMPARISON")
    print("=" * 60)
    
    try:
        # Demo API v1 reference
        demo_v1 = {
            "url": "https://ronaldo-club.to/api/cards/hq/check",
            "method": "POST",
            "headers": {
                "content-type": "application/json",
                "referer": "https://ronaldo-club.to/orders/cards/hq",
            },
            "payload": {"id": 299187},
            "response": {"success": True, "data": {"_id": 299187, "status": "Refunded"}}
        }
        
        # Demo API v3 reference
        demo_v3 = {
            "url": "https://ronaldo-club.to/orders/{order_id}/check",
            "method": "GET",
            "params": {"cc_id": "card_id"},
            "headers": {"Referer": "order_details_page"},
        }
        
        print(f"📋 Demo API v1 (demo/check.py):")
        print(f"   • URL: {demo_v1['url']}")
        print(f"   • Method: {demo_v1['method']}")
        print(f"   • Payload: {demo_v1['payload']}")
        print(f"   • Success Response: {demo_v1['response']['success']}")
        
        print(f"\n📋 Demo API v3 (demo/api3_demo/check.py):")
        print(f"   • URL Pattern: {demo_v3['url']}")
        print(f"   • Method: {demo_v3['method']}")
        print(f"   • Params: {demo_v3['params']}")
        
        print(f"\n📋 Current Implementation Analysis:")
        print(f"   • Configured for: API v1")
        print(f"   • URL matches demo v1: ✅")
        print(f"   • Method matches demo v1: ✅")
        print(f"   • Payload format matches demo v1: ✅")
        print(f"   • Missing: API version routing for v3")
        
        print(f"\n🎯 ISSUE IDENTIFIED:")
        print(f"   • Current implementation is correct for API v1")
        print(f"   • But lacks API v3 routing like other methods")
        print(f"   • HTTP 404 suggests endpoint or configuration issue")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in demo comparison: {e}")
        return False

async def main():
    """Run comprehensive debug analysis"""
    print("🔍 CHECK ORDER API DEBUG ANALYSIS")
    print("=" * 60)
    
    tests = [
        ("Configuration Check", test_check_order_configuration),
        ("URL Construction", test_check_order_url_construction),
        ("Headers Analysis", test_check_order_headers),
        ("API Version Routing", test_api_version_routing),
        ("Demo Comparison", test_demo_comparison),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEBUG ANALYSIS SUMMARY")
    print("=" * 60)
    
    passed = [name for name, success in results if success]
    failed = [name for name, success in results if not success]
    
    print(f"✅ COMPLETED TESTS: {len(passed)}/{len(results)}")
    for test_name in passed:
        print(f"   • {test_name}")
    
    if failed:
        print(f"\n❌ FAILED TESTS: {len(failed)}")
        for test_name in failed:
            print(f"   • {test_name}")
    
    print(f"\n🎯 KEY FINDINGS:")
    print(f"1. Current implementation follows API v1 demo pattern correctly")
    print(f"2. check_order method lacks API version routing (unlike other methods)")
    print(f"3. HTTP 404 error suggests endpoint or configuration issue")
    print(f"4. Need to add API v3 routing for completeness")
    
    print(f"\n📋 RECOMMENDED FIXES:")
    print(f"1. Add API version routing to check_order method")
    print(f"2. Implement _check_order_v3 method for API v3")
    print(f"3. Verify API v1 endpoint configuration")
    print(f"4. Test with actual API calls to confirm fix")
    
    return len(passed) >= 3

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
