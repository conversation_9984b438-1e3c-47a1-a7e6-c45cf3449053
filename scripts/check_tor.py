#!/usr/bin/env python3
"""
Check Tor Connectivity

This script checks if Tor is running and accessible on common SOCKS ports.
Useful for troubleshooting API v3 .onion domain connections.
"""

import socket
import sys


def check_port(host: str, port: int, timeout: float = 2.0) -> bool:
    """Check if a port is open and accepting connections"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False


def main():
    """Check Tor connectivity"""
    print("=" * 60)
    print("Tor Connectivity Check")
    print("=" * 60)
    
    host = "127.0.0.1"
    ports = {
        9150: "Tor Browser",
        9050: "System Tor",
    }
    
    found_any = False
    
    for port, name in ports.items():
        print(f"\nChecking {name} (port {port})...", end=" ")
        if check_port(host, port):
            print(f"✓ OPEN")
            print(f"  SOCKS URL: socks5h://{host}:{port}")
            found_any = True
        else:
            print(f"✗ CLOSED")
    
    print("\n" + "=" * 60)
    
    if found_any:
        print("✓ Tor is accessible")
        print("\nTo use with API v3, set:")
        for port, name in ports.items():
            if check_port(host, port):
                print(f"  export SOCKS_URL=socks5h://{host}:{port}  # {name}")
        return 0
    else:
        print("✗ Tor is not accessible")
        print("\nTo fix this:")
        print("\n1. For Tor Browser:")
        print("   - Open Tor Browser")
        print("   - Wait for it to connect")
        print("   - Keep it running in the background")
        print("   - Default SOCKS port: 9150")
        print("\n2. For System Tor:")
        print("   - Install: sudo apt install tor")
        print("   - Start: sudo systemctl start tor")
        print("   - Enable: sudo systemctl enable tor")
        print("   - Default SOCKS port: 9050")
        print("\n3. Then set environment variable:")
        print("   export SOCKS_URL=socks5h://127.0.0.1:9150  # Tor Browser")
        print("   export SOCKS_URL=socks5h://127.0.0.1:9050  # System Tor")
        return 1


if __name__ == "__main__":
    sys.exit(main())

