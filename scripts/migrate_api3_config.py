#!/usr/bin/env python3
"""
Configuration Migration Utility for API v3 Integration

This script helps migrate configuration from api3 standalone scripts
to the bot's integrated api_v3 system.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class API3ConfigMigrator:
    """Migrates api3 configuration to bot settings format"""
    
    def __init__(self, api3_dir: Path, bot_root: Path):
        self.api3_dir = Path(api3_dir)
        self.bot_root = Path(bot_root)
        self.api3_env_file = self.api3_dir / ".env"
        self.bot_env_file = self.bot_root / ".env"
        
    def load_api3_config(self) -> Dict[str, str]:
        """Load configuration from api3 .env file"""
        config = {}
        
        if self.api3_env_file.exists():
            # Load api3 .env file
            load_dotenv(self.api3_env_file)
            
            # Extract api3-specific variables
            api3_vars = [
                "BASE_URL", "USERNAME", "PASSWORD",
                "USE_SOCKS_PROXY", "SOCKS_URL",
                "SESSION_COOKIES_PATH", "LOG_LEVEL"
            ]
            
            for var in api3_vars:
                value = os.getenv(var)
                if value:
                    config[var] = value
                    
            logger.info(f"Loaded {len(config)} configuration variables from api3")
        else:
            logger.warning(f"API3 .env file not found: {self.api3_env_file}")
            
        return config
    
    def migrate_session_cookies(self) -> Optional[str]:
        """Migrate session cookies from api3 to bot storage"""
        api3_cookies_file = self.api3_dir / "session_cookies.json"
        
        if not api3_cookies_file.exists():
            logger.info("No api3 session cookies found to migrate")
            return None
            
        try:
            # Create bot storage directory
            bot_storage_dir = self.bot_root / "storage" / "api_v3"
            bot_storage_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy session cookies
            bot_cookies_file = bot_storage_dir / "session_cookies.json"
            
            with api3_cookies_file.open('r') as src:
                cookies_data = json.load(src)
                
            with bot_cookies_file.open('w') as dst:
                json.dump(cookies_data, dst, indent=2)
                
            logger.info(f"Migrated session cookies to: {bot_cookies_file}")
            return str(bot_cookies_file)
            
        except Exception as e:
            logger.error(f"Failed to migrate session cookies: {e}")
            return None
    
    def generate_bot_env_config(self, api3_config: Dict[str, str]) -> Dict[str, str]:
        """Generate bot .env configuration from api3 config"""
        bot_config = {}
        
        # Map api3 variables to bot variables
        mapping = {
            "BASE_URL": "EXTERNAL_V3_BASE_URL",
            "USERNAME": "EXTERNAL_V3_USERNAME", 
            "PASSWORD": "EXTERNAL_V3_PASSWORD",
            "USE_SOCKS_PROXY": "EXTERNAL_V3_USE_TOR_PROXY",
            "SOCKS_URL": "EXTERNAL_V3_SOCKS_URL",
            "SESSION_COOKIES_PATH": "EXTERNAL_V3_SESSION_PATH",
        }
        
        for api3_key, bot_key in mapping.items():
            if api3_key in api3_config:
                value = api3_config[api3_key]
                
                # Handle boolean conversion
                if api3_key == "USE_SOCKS_PROXY":
                    value = value.lower() in ("true", "1", "yes", "on")
                    
                bot_config[bot_key] = str(value)
        
        # Set additional bot-specific defaults
        bot_config.setdefault("EXTERNAL_API_VERSION", "v3")
        bot_config.setdefault("EXTERNAL_V3_TIMEOUT", "60")
        bot_config.setdefault("EXTERNAL_V3_VERIFY_TLS", "true")
        bot_config.setdefault("EXTERNAL_V3_DEFAULT_BINS", "405621")
        
        return bot_config
    
    def update_bot_env_file(self, new_config: Dict[str, str]) -> bool:
        """Update bot .env file with new configuration"""
        try:
            # Read existing .env file
            existing_lines = []
            existing_vars = set()
            
            if self.bot_env_file.exists():
                with self.bot_env_file.open('r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' in line:
                                var_name = line.split('=', 1)[0]
                                existing_vars.add(var_name)
                        existing_lines.append(line)
            
            # Add new configuration variables
            new_lines = existing_lines.copy()
            
            # Add API v3 section header if needed
            if any(key.startswith('EXTERNAL_V3_') for key in new_config.keys()):
                if not any('API v3' in line for line in existing_lines):
                    new_lines.extend([
                        "",
                        "# API v3 (HTML/Tor) Configuration - Migrated from api3",
                    ])
            
            # Add new variables
            for key, value in new_config.items():
                if key not in existing_vars:
                    new_lines.append(f"{key}={value}")
                    logger.info(f"Added configuration: {key}")
                else:
                    logger.info(f"Configuration already exists: {key}")
            
            # Write updated .env file
            with self.bot_env_file.open('w') as f:
                f.write('\n'.join(new_lines))
                if new_lines and not new_lines[-1].endswith('\n'):
                    f.write('\n')
            
            logger.info(f"Updated bot .env file: {self.bot_env_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update bot .env file: {e}")
            return False
    
    def migrate(self) -> bool:
        """Perform complete migration from api3 to bot configuration"""
        logger.info("Starting API v3 configuration migration...")
        
        # Load api3 configuration
        api3_config = self.load_api3_config()
        if not api3_config:
            logger.warning("No api3 configuration found to migrate")
            return False
        
        # Migrate session cookies
        cookies_path = self.migrate_session_cookies()
        if cookies_path:
            # Update session path in config
            api3_config["SESSION_COOKIES_PATH"] = cookies_path
        
        # Generate bot configuration
        bot_config = self.generate_bot_env_config(api3_config)
        
        # Update bot .env file
        success = self.update_bot_env_file(bot_config)
        
        if success:
            logger.info("✅ API v3 configuration migration completed successfully!")
            logger.info("🔄 Please restart the bot to apply the new configuration")
            return True
        else:
            logger.error("❌ API v3 configuration migration failed")
            return False


def main():
    """Main migration function"""
    import argparse

    parser = argparse.ArgumentParser(description="Migrate api3 configuration to bot")
    parser.add_argument("--api3-dir", default="./api3", help="Path to api3 directory")
    parser.add_argument("--bot-root", default=".", help="Path to bot root directory")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be migrated without making changes")
    parser.add_argument("--validate", action="store_true", help="Validate setup after migration")

    args = parser.parse_args()

    migrator = API3ConfigMigrator(
        api3_dir=Path(args.api3_dir),
        bot_root=Path(args.bot_root)
    )

    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No changes will be made")
        api3_config = migrator.load_api3_config()
        bot_config = migrator.generate_bot_env_config(api3_config)

        print("\n📋 Configuration that would be migrated:")
        for key, value in bot_config.items():
            # Mask sensitive values
            display_value = value
            if any(sensitive in key.lower() for sensitive in ['password', 'token', 'secret']):
                display_value = '*' * len(value) if value else ''
            print(f"  {key}={display_value}")

        return True

    success = migrator.migrate()

    # Run validation if requested
    if success and args.validate:
        print("\n🔍 Running post-migration validation...")
        try:
            import subprocess
            result = subprocess.run([
                sys.executable,
                str(Path(__file__).parent / "validate_api_v3_setup.py")
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ Post-migration validation passed!")
            else:
                print("⚠️  Post-migration validation found issues:")
                print(result.stdout)
                if result.stderr:
                    print("Errors:", result.stderr)
        except Exception as e:
            print(f"⚠️  Could not run validation: {e}")

    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
