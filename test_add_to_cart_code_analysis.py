#!/usr/bin/env python3
"""
Add-to-Cart Code Analysis

This script analyzes the code to identify issues with card selection and add-to-cart functionality
without requiring database connection.
"""

import sys
import os
import re
from typing import List, Dict, Any
sys.path.insert(0, '.')

def analyze_add_to_cart_workflow():
    """Analyze the complete add-to-cart workflow"""
    print("🔍 ANALYZING ADD-TO-CART WORKFLOW")
    print("=" * 60)
    
    issues_found = []
    fixes_needed = []
    
    # Check catalog handlers cb_add_to_cart method
    try:
        with open('handlers/catalog_handlers.py', 'r') as f:
            catalog_content = f.read()
        
        print("📄 Analyzing handlers/catalog_handlers.py...")
        
        # Check if cb_add_to_cart method exists
        if 'async def cb_add_to_cart(' in catalog_content:
            print("   ✅ cb_add_to_cart method exists")
            
            # Extract the method
            method_start = catalog_content.find('async def cb_add_to_cart(')
            method_end = catalog_content.find('\n    async def ', method_start + 1)
            if method_end == -1:
                method_end = catalog_content.find('\n    def ', method_start + 1)
            if method_end == -1:
                method_end = len(catalog_content)
            
            method_content = catalog_content[method_start:method_end]
            
            # Check for proper error handling
            if 'try:' in method_content and 'except' in method_content:
                print("   ✅ Has error handling")
            else:
                issues_found.append("cb_add_to_cart: Missing error handling")
                print("   ❌ Missing error handling")
            
            # Check if card data is retrieved from cache
            if 'user_current_cards.get(user_id, [])' in method_content:
                print("   ✅ Uses cached card data")
            else:
                issues_found.append("cb_add_to_cart: Not using cached card data")
                print("   ❌ Not using cached card data")
            
            # Check if card_data is passed to add_to_cart
            if 'add_to_cart(' in method_content and 'card_data' in method_content:
                print("   ✅ Passes card_data to add_to_cart")
            else:
                issues_found.append("cb_add_to_cart: Not passing card_data to add_to_cart")
                print("   ❌ Not passing card_data to add_to_cart")
            
            # Check for proper user feedback
            if 'Adding to cart...' in method_content:
                print("   ✅ Provides user feedback")
            else:
                issues_found.append("cb_add_to_cart: Missing user feedback")
                print("   ❌ Missing user feedback")
        else:
            issues_found.append("cb_add_to_cart: Method not found")
            print("   ❌ cb_add_to_cart method not found")
    
    except FileNotFoundError:
        issues_found.append("cb_add_to_cart: Could not read catalog_handlers.py")
        print("   ❌ Could not read catalog_handlers.py")
    
    return issues_found, fixes_needed

def analyze_cart_service_add_to_cart():
    """Analyze the cart service add_to_cart method"""
    print(f"\n🛒 ANALYZING CART SERVICE ADD-TO-CART")
    print("=" * 60)
    
    issues_found = []
    fixes_needed = []
    
    try:
        with open('services/cart_service.py', 'r') as f:
            cart_content = f.read()
        
        print("📄 Analyzing services/cart_service.py...")
        
        # Check if add_to_cart method exists
        if 'async def add_to_cart(' in cart_content:
            print("   ✅ add_to_cart method exists")
            
            # Extract the method
            method_start = cart_content.find('async def add_to_cart(')
            method_end = cart_content.find('\n    async def ', method_start + 1)
            if method_end == -1:
                method_end = cart_content.find('\n    def ', method_start + 1)
            if method_end == -1:
                method_end = len(cart_content)
            
            method_content = cart_content[method_start:method_end]
            
            # Check for database transaction
            if 'async with database_transaction():' in method_content:
                print("   ✅ Uses database transaction")
            else:
                issues_found.append("add_to_cart: Missing database transaction")
                print("   ❌ Missing database transaction")
            
            # Check for card_data parameter handling
            if 'card_data: Optional[Dict[str, Any]] = None' in method_content:
                print("   ✅ Accepts card_data parameter")
            else:
                issues_found.append("add_to_cart: Missing card_data parameter")
                print("   ❌ Missing card_data parameter")
            
            # Check for unnecessary API calls
            if 'if card_data is None:' in method_content and '_fetch_card_data' in method_content:
                issues_found.append("add_to_cart: Makes API calls when card_data is None")
                fixes_needed.append("Optimize: Avoid API calls when card_data is provided")
                print("   ⚠️ Makes API calls when card_data is None")
            
            # Check if external cart operations are disabled during browsing
            if 'We no longer add to external cart during browsing' in method_content:
                print("   ✅ External cart operations disabled during browsing")
            else:
                issues_found.append("add_to_cart: May be making external API calls during browsing")
                fixes_needed.append("Disable external cart operations during add-to-cart")
                print("   ❌ May be making external API calls during browsing")
            
            # Check for proper error handling
            if 'try:' in method_content and 'except' in method_content:
                print("   ✅ Has error handling")
            else:
                issues_found.append("add_to_cart: Missing error handling")
                print("   ❌ Missing error handling")
        else:
            issues_found.append("add_to_cart: Method not found")
            print("   ❌ add_to_cart method not found")
    
    except FileNotFoundError:
        issues_found.append("add_to_cart: Could not read cart_service.py")
        print("   ❌ Could not read cart_service.py")
    
    return issues_found, fixes_needed

def analyze_fetch_card_data_method():
    """Analyze the _fetch_card_data method for unnecessary API calls"""
    print(f"\n📡 ANALYZING _FETCH_CARD_DATA METHOD")
    print("=" * 60)
    
    issues_found = []
    fixes_needed = []
    
    try:
        with open('services/cart_service.py', 'r') as f:
            cart_content = f.read()
        
        print("📄 Analyzing _fetch_card_data method...")
        
        # Check if _fetch_card_data method exists
        if 'async def _fetch_card_data(' in cart_content:
            print("   ✅ _fetch_card_data method exists")
            
            # Extract the method
            method_start = cart_content.find('async def _fetch_card_data(')
            method_end = cart_content.find('\n    async def ', method_start + 1)
            if method_end == -1:
                method_end = cart_content.find('\n    def ', method_start + 1)
            if method_end == -1:
                method_end = len(cart_content)
            
            method_content = cart_content[method_start:method_end]
            
            # Check for multiple API calls
            api_call_patterns = [
                'await self.card_service.fetch_cards(',
                'cards_data = await self.card_service.fetch_cards('
            ]
            
            total_api_calls = 0
            for pattern in api_call_patterns:
                total_api_calls += method_content.count(pattern)
            
            if total_api_calls > 1:
                issues_found.append(f"_fetch_card_data: Makes {total_api_calls} API calls")
                fixes_needed.append("Optimize: Reduce API calls in _fetch_card_data")
                print(f"   ⚠️ Makes {total_api_calls} potential API calls")
            else:
                print(f"   ✅ Makes {total_api_calls} API call(s)")
            
            # Check for search strategies
            if 'search_strategies' in method_content:
                strategies_count = method_content.count('{"page":')
                issues_found.append(f"_fetch_card_data: Uses {strategies_count} search strategies")
                fixes_needed.append("Optimize: Reduce search strategies or use better caching")
                print(f"   ⚠️ Uses {strategies_count} search strategies (potential for many API calls)")
            
            # Check for caching
            if '_card_cache' in method_content:
                print("   ✅ Has caching mechanism")
            else:
                issues_found.append("_fetch_card_data: Missing caching mechanism")
                print("   ❌ Missing caching mechanism")
        else:
            print("   ✅ _fetch_card_data method not found (good - no unnecessary API calls)")
    
    except FileNotFoundError:
        issues_found.append("_fetch_card_data: Could not read cart_service.py")
        print("   ❌ Could not read cart_service.py")
    
    return issues_found, fixes_needed

def analyze_external_api_service():
    """Analyze external API service for duplicate methods"""
    print(f"\n🌐 ANALYZING EXTERNAL API SERVICE")
    print("=" * 60)
    
    issues_found = []
    fixes_needed = []
    
    try:
        with open('services/external_api_service.py', 'r') as f:
            api_content = f.read()
        
        print("📄 Analyzing services/external_api_service.py...")
        
        # Check for add_to_cart methods
        add_to_cart_methods = re.findall(r'async def.*add_to_cart.*\(', api_content)
        print(f"   Found {len(add_to_cart_methods)} add_to_cart methods:")
        for method in add_to_cart_methods:
            print(f"     - {method.strip()}")
        
        if len(add_to_cart_methods) > 2:
            issues_found.append(f"external_api_service: {len(add_to_cart_methods)} add_to_cart methods (potential duplicates)")
            fixes_needed.append("Remove duplicate add_to_cart methods")
            print(f"   ⚠️ Multiple add_to_cart methods may indicate duplicates")
        
        # Check for view_cart methods
        view_cart_methods = re.findall(r'async def.*view_cart.*\(', api_content)
        print(f"   Found {len(view_cart_methods)} view_cart methods:")
        for method in view_cart_methods:
            print(f"     - {method.strip()}")
        
        if len(view_cart_methods) > 2:
            issues_found.append(f"external_api_service: {len(view_cart_methods)} view_cart methods (potential duplicates)")
            fixes_needed.append("Remove duplicate view_cart methods")
        
        # Check for clear_cart methods
        clear_cart_methods = re.findall(r'async def.*clear_cart.*\(', api_content)
        print(f"   Found {len(clear_cart_methods)} clear_cart methods:")
        for method in clear_cart_methods:
            print(f"     - {method.strip()}")
        
        if len(clear_cart_methods) > 2:
            issues_found.append(f"external_api_service: {len(clear_cart_methods)} clear_cart methods (potential duplicates)")
            fixes_needed.append("Remove duplicate clear_cart methods")
        
        # Check file size (large files may indicate duplication)
        file_size = len(api_content)
        line_count = api_content.count('\n')
        print(f"   File size: {file_size} characters, {line_count} lines")
        
        if line_count > 2000:
            issues_found.append(f"external_api_service: Large file ({line_count} lines) may indicate code duplication")
            fixes_needed.append("Review and remove duplicate code")
            print(f"   ⚠️ Large file may indicate code duplication")
    
    except FileNotFoundError:
        issues_found.append("external_api_service: Could not read external_api_service.py")
        print("   ❌ Could not read external_api_service.py")
    
    return issues_found, fixes_needed

def main():
    """Run comprehensive add-to-cart code analysis"""
    print("🔍 ADD-TO-CART CODE ANALYSIS")
    print("=" * 60)
    
    all_issues = []
    all_fixes = []
    
    # Analyze each component
    workflow_issues, workflow_fixes = analyze_add_to_cart_workflow()
    all_issues.extend(workflow_issues)
    all_fixes.extend(workflow_fixes)
    
    cart_issues, cart_fixes = analyze_cart_service_add_to_cart()
    all_issues.extend(cart_issues)
    all_fixes.extend(cart_fixes)
    
    fetch_issues, fetch_fixes = analyze_fetch_card_data_method()
    all_issues.extend(fetch_issues)
    all_fixes.extend(fetch_fixes)
    
    api_issues, api_fixes = analyze_external_api_service()
    all_issues.extend(api_issues)
    all_fixes.extend(api_fixes)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CODE ANALYSIS SUMMARY")
    print("=" * 60)
    
    if all_issues:
        print(f"❌ ISSUES FOUND: {len(all_issues)}")
        
        # Group issues by category
        categories = {
            "Workflow Issues": [i for i in all_issues if "cb_add_to_cart" in i],
            "Cart Service Issues": [i for i in all_issues if "add_to_cart:" in i],
            "API Call Issues": [i for i in all_issues if "_fetch_card_data" in i],
            "External API Issues": [i for i in all_issues if "external_api_service" in i]
        }
        
        for category, issues in categories.items():
            if issues:
                print(f"\n🔴 {category}:")
                for issue in issues:
                    print(f"   • {issue}")
    
    if all_fixes:
        print(f"\n🔧 FIXES NEEDED: {len(all_fixes)}")
        for fix in all_fixes:
            print(f"   • {fix}")
    
    if not all_issues:
        print("✅ NO CRITICAL ISSUES FOUND!")
    
    print(f"\n📈 ANALYSIS COMPLETE")
    print("🎯 PRIORITY FIXES:")
    print("1. Optimize _fetch_card_data to avoid unnecessary API calls")
    print("2. Ensure card_data is properly passed from catalog handlers")
    print("3. Remove duplicate methods in external_api_service")
    print("4. Verify database operations are completing successfully")
    
    return len(all_issues) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
