#!/usr/bin/env python3
"""
Test script for API v3-only checkout cart synchronization workflow.

This script tests the complete workflow:
1. Clear external cart using API v3 clear_cart()
2. Verify cart is empty using API v3 view_cart()
3. Populate cart from virtual cart using API v3 add_to_cart()
4. Verify synchronization using API v3 view_cart()
"""

import asyncio
import logging
import os
import sys
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(override=True)

from services.checkout_queue_service import CheckoutQueueService
from services.external_api_service import ExternalAPIService
from database.connection import init_database

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_api_v3_enforcement():
    """Test that API v3 enforcement works correctly"""
    logger.info("🔒 Testing API v3 Enforcement")
    logger.info("=" * 50)
    
    try:
        checkout_service = CheckoutQueueService()
        
        # Check API version
        api_version = getattr(checkout_service.external_api_service, 'api_version', None)
        logger.info(f"External API service version: {api_version}")
        
        # Test API v3 enforcement
        is_v3_only = checkout_service._ensure_api_v3_only()
        
        if is_v3_only:
            logger.info("✅ API v3 enforcement passed")
            return True
        else:
            logger.error("❌ API v3 enforcement failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ API v3 enforcement test failed: {e}", exc_info=True)
        return False


async def test_cart_clearing_v3():
    """Test API v3-only cart clearing"""
    logger.info("\n🧹 Testing API v3 Cart Clearing")
    logger.info("=" * 50)
    
    try:
        checkout_service = CheckoutQueueService()
        
        # Test cart clearing
        clear_success = await checkout_service._clear_external_cart_v3()
        
        if clear_success:
            logger.info("✅ API v3 cart clearing succeeded")
            
            # Verify cart is empty
            empty_verification = await checkout_service._verify_cart_empty_v3()
            
            if empty_verification:
                logger.info("✅ Cart empty verification passed")
                return True
            else:
                logger.error("❌ Cart empty verification failed")
                return False
        else:
            logger.error("❌ API v3 cart clearing failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Cart clearing test failed: {e}", exc_info=True)
        return False


async def test_cart_population_v3():
    """Test API v3-only cart population from virtual cart"""
    logger.info("\n📦 Testing API v3 Cart Population")
    logger.info("=" * 50)
    
    try:
        checkout_service = CheckoutQueueService()
        
        # Create mock virtual cart items
        virtual_cart_items = [
            {
                "card_id": "01ce7b071917211cc99ce4a265faa3c4aff1f589",
                "quantity": 1,
                "card_data": {"name": "Test Card 1"}
            },
            {
                "card_id": "0475ecbb9cd0879361d378c0aad9fa94582fdd12",
                "quantity": 2,
                "card_data": {"name": "Test Card 2"}
            }
        ]
        
        logger.info(f"Virtual cart contains {len(virtual_cart_items)} unique items")
        
        # Clear cart first
        clear_success = await checkout_service._clear_external_cart_v3()
        if not clear_success:
            logger.error("❌ Failed to clear cart before population test")
            return False
        
        # Test cart population
        populate_success = await checkout_service._populate_cart_from_virtual_v3(virtual_cart_items)
        
        if populate_success:
            logger.info("✅ API v3 cart population succeeded")
            return True
        else:
            logger.error("❌ API v3 cart population failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Cart population test failed: {e}", exc_info=True)
        return False


async def test_cart_synchronization_v3():
    """Test API v3-only cart synchronization verification"""
    logger.info("\n🔍 Testing API v3 Cart Synchronization")
    logger.info("=" * 50)
    
    try:
        checkout_service = CheckoutQueueService()
        
        # Create mock virtual cart items
        virtual_cart_items = [
            {
                "card_id": "01ce7b071917211cc99ce4a265faa3c4aff1f589",
                "quantity": 1,
                "card_data": {"name": "Test Card 1"}
            }
        ]
        
        # Clear and populate cart
        clear_success = await checkout_service._clear_external_cart_v3()
        if not clear_success:
            logger.error("❌ Failed to clear cart")
            return False
        
        populate_success = await checkout_service._populate_cart_from_virtual_v3(virtual_cart_items)
        if not populate_success:
            logger.warning("⚠️ Cart population had issues, but continuing with sync test")
        
        # Test synchronization verification
        sync_result = await checkout_service._verify_cart_synchronization_v3(virtual_cart_items)
        
        if sync_result["valid"]:
            logger.info(f"✅ Cart synchronization verification passed: {sync_result['message']}")
            return True
        else:
            logger.error(f"❌ Cart synchronization verification failed: {sync_result['message']}")
            logger.error(f"   Expected: {sync_result.get('expected_items', {})}")
            logger.error(f"   Actual: {sync_result.get('actual_items', {})}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Cart synchronization test failed: {e}", exc_info=True)
        return False


async def test_complete_workflow():
    """Test the complete API v3-only checkout cart synchronization workflow"""
    logger.info("\n🚀 Testing Complete API v3 Workflow")
    logger.info("=" * 50)
    
    try:
        checkout_service = CheckoutQueueService()
        
        # Create mock virtual cart items
        virtual_cart_items = [
            {
                "card_id": "01ce7b071917211cc99ce4a265faa3c4aff1f589",
                "quantity": 1,
                "card_data": {"name": "Test Card 1"}
            },
            {
                "card_id": "0475ecbb9cd0879361d378c0aad9fa94582fdd12",
                "quantity": 1,
                "card_data": {"name": "Test Card 2"}
            }
        ]
        
        logger.info(f"Testing complete workflow with {len(virtual_cart_items)} virtual cart items")
        
        # Test the complete workflow
        workflow_success = await checkout_service._populate_external_cart(virtual_cart_items)
        
        if workflow_success:
            logger.info("✅ Complete API v3 workflow succeeded")
            
            # Additional verification using the validation method
            validation_result = await checkout_service._validate_cart_items(virtual_cart_items)
            
            if validation_result["valid"]:
                logger.info(f"✅ Final validation passed: {validation_result['message']}")
                return True
            else:
                logger.error(f"❌ Final validation failed: {validation_result['message']}")
                return False
        else:
            logger.error("❌ Complete API v3 workflow failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Complete workflow test failed: {e}", exc_info=True)
        return False


async def main():
    """Run all API v3-only checkout cart synchronization tests"""
    logger.info("🚀 Starting API v3-Only Checkout Cart Synchronization Tests")
    logger.info("=" * 80)

    # Initialize database connection
    try:
        logger.info("🔌 Connecting to database...")
        await init_database()
        logger.info("✅ Database connected successfully")
    except Exception as e:
        logger.error(f"❌ Failed to connect to database: {e}")
        return False

    test_results = []
    
    # Test 1: API v3 enforcement
    result1 = await test_api_v3_enforcement()
    test_results.append(("API v3 Enforcement", result1))
    
    # Only continue if API v3 is available
    if not result1:
        logger.error("❌ API v3 not available - skipping remaining tests")
        logger.error("   Please ensure EXTERNAL_API_VERSION=v3 is set")
        return False
    
    # Test 2: Cart clearing
    result2 = await test_cart_clearing_v3()
    test_results.append(("API v3 Cart Clearing", result2))
    
    # Test 3: Cart population
    result3 = await test_cart_population_v3()
    test_results.append(("API v3 Cart Population", result3))
    
    # Test 4: Cart synchronization
    result4 = await test_cart_synchronization_v3()
    test_results.append(("API v3 Cart Synchronization", result4))
    
    # Test 5: Complete workflow
    result5 = await test_complete_workflow()
    test_results.append(("Complete API v3 Workflow", result5))
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 80)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        logger.info("🎉 All API v3-only checkout cart synchronization tests passed!")
        logger.info("   ✅ API v3 enforcement works correctly")
        logger.info("   ✅ Cart clearing uses API v3 clear_cart() only")
        logger.info("   ✅ Cart population uses API v3 add_to_cart() only")
        logger.info("   ✅ Cart verification uses API v3 view_cart() only")
        logger.info("   ✅ Complete workflow enforces API v3-only usage")
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
    
    return passed == len(test_results)


if __name__ == "__main__":
    asyncio.run(main())
