"""
Admin permission middleware to enforce role-based access for admin actions.
"""

from __future__ import annotations

import logging
from typing import Any, Awaitable, Callable, Dict

from aiogram.dispatcher.middlewares.base import BaseMiddleware
from aiogram.types import TelegramObject, CallbackQuery, Message

from config.settings import get_settings
from services.user_service import UserService
from database.connection import get_collection

logger = logging.getLogger(__name__)


class AdminPermissionMiddleware(BaseMiddleware):
    """Middleware that enforces role/permission rules on admin callbacks."""

    def __init__(self):
        self.settings = get_settings()
        # Lazily initialize services/collections to avoid requiring DB at import time
        self.user_service: UserService | None = None
        self.roles = None

        # Map callback prefixes to required permission strings
        self.perm_map: list[tuple[str, str]] = [
            ("admin:users:export", "users:export"),
            ("admin:user:setrole:", "users:edit"),
            ("admin:user:toggle:", "users:edit"),
            ("admin:users:add", "users:edit"),
            ("admin:users:search", "users:view"),
            ("admin:users:page:", "users:view"),
            ("admin:users", "users:view"),
            ("admin:settings:page:", "settings:view"),
            ("admin:setting:edit:", "settings:edit"),
            ("admin:setting:add", "settings:edit"),
            ("admin:settings", "settings:view"),
            ("admin:roles", "roles:view"),
            ("admin:role:edit:", "roles:edit"),
            # API Management permissions
            ("admin:apis:export", "apis:export"),
            ("admin:apis:import", "apis:import"),
            ("admin:apis:add", "apis:create"),
            ("admin:api:delete:", "apis:delete"),
            ("admin:api:edit:", "apis:edit"),
            ("admin:api:toggle:", "apis:edit"),
            ("admin:api:test:", "apis:test"),
            ("admin:apis:search", "apis:view"),
            ("admin:apis:analytics", "apis:view"),
            ("admin:apis:health", "apis:view"),
            ("admin:apis:page:", "apis:view"),
            ("admin:apis:list", "apis:view"),
            ("admin:apis", "apis:view"),
            ("admin:api:view:", "apis:view"),
            ("admin:api:metrics:", "apis:view"),
            ("admin:api:health:", "apis:view"),
            # Modern API management callbacks
            ("api_main", "apis:view"),
            ("api_list", "apis:view"),
            ("api_create", "apis:create"),
            ("api_view:", "apis:view"),
            ("api_edit:", "apis:edit"),
            ("api_test:", "apis:test"),
            ("api_test_endpoint:", "apis:test"),
            ("api_health_check_all", "apis:view"),
            ("edit_auth:", "apis:edit"),
            ("auth_update:", "apis:edit"),
            ("auth_test:", "apis:test"),
            ("auth_clear:", "apis:edit"),
            ("auth_change_type:", "apis:edit"),
            ("auth_type:", "apis:edit"),
            ("headers_manage:", "apis:edit"),
            ("headers_add:", "apis:edit"),
            ("headers_update:", "apis:edit"),
            ("headers_test:", "apis:test"),
            ("headers_clear:", "apis:edit"),
            ("edit_base_url:", "apis:edit"),
            ("edit_endpoints:", "apis:edit"),
            ("skip_endpoints", "apis:edit"),
            ("add_basic_endpoints", "apis:edit"),
        ]

    async def _get_perms_for_role(self, role: str) -> set[str]:
        try:
            # Lazy collection init
            if self.roles is None:
                self.roles = get_collection("roles")
            doc = await self.roles.find_one({"name": role})
            if not doc:
                return set()
            perms = doc.get("permissions") or []
            return set(perms)
        except Exception as e:
            logger.warning(f"Failed loading role perms for {role}: {e}")
            return set()

    async def _allowed(self, tg_user_id: int, required: str) -> bool:
        # Admin IDs are superusers
        if tg_user_id in self.settings.admin_ids:
            return True
        try:
            # Lazy service init
            if self.user_service is None:
                self.user_service = UserService()
            user = await self.user_service.get_user_by_telegram_id(tg_user_id)
            if not user:
                return False
            role = getattr(user, "role", "user")
            perms = await self._get_perms_for_role(role)
            if "*" in perms:
                return True
            return required in perms
        except Exception as e:
            logger.error(f"Permission check failed: {e}")
            return False

    def _required_from_callback(self, data: str) -> str | None:
        for prefix, perm in self.perm_map:
            if data.startswith(prefix):
                return perm
        return None

    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any],
    ) -> Any:
        # Only enforce on admin callback queries for now
        if isinstance(event, CallbackQuery) and event.data:
            required = self._required_from_callback(event.data)
            if required:
                tg_user = event.from_user
                if not tg_user:
                    return await handler(event, data)
                allowed = await self._allowed(tg_user.id, required)
                if not allowed:
                    try:
                        await event.answer("Not permitted", show_alert=True)
                    except Exception:
                        pass
                    return None
        # Allow all other events
        return await handler(event, data)
