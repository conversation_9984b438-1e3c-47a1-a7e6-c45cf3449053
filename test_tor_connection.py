#!/usr/bin/env python3
"""
Test Tor/SOCKS Proxy Connection

This script tests if Tor is running and accessible on the configured SOCKS ports.
"""
import os
import sys
import socket
from dotenv import load_dotenv

# Load environment
load_dotenv()


def test_port(host: str, port: int, timeout: float = 5.0) -> tuple[bool, str]:
    """Test if a port is open and accepting connections"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()

        if result == 0:
            return True, f"✅ Port {port} is OPEN and accepting connections"
        else:
            return (
                False,
                f"❌ Port {port} is CLOSED or not accepting connections (Error: {result})",
            )
    except socket.timeout:
        return False, f"❌ Connection to port {port} timed out"
    except Exception as e:
        return False, f"❌ Error testing port {port}: {str(e)}"


def main():
    print("=" * 60)
    print("🔍 Tor/SOCKS Proxy Connection Test")
    print("=" * 60)

    # Get SOCKS URL from environment
    socks_url = os.getenv("SOCKS_URL", "socks5h://127.0.0.1:9050")
    print(f"\n📋 Configured SOCKS URL: {socks_url}")

    # Extract host and port
    if "://" in socks_url:
        socks_url = socks_url.split("://")[1]

    host, port_str = socks_url.rsplit(":", 1)
    port = int(port_str)

    print(f"🎯 Testing connection to {host}:{port}...")
    print()

    # Test the configured port
    success, message = test_port(host, port)
    print(message)
    print()

    # Test common Tor ports
    print("📊 Testing common Tor ports:")
    print("-" * 60)

    common_ports = {
        9050: "System Tor SOCKS proxy (default)",
        9150: "Tor Browser SOCKS proxy",
        9051: "Tor control port",
    }

    available_ports = []
    for test_port_num, description in common_ports.items():
        success, message = test_port(host, test_port_num)
        print(f"  Port {test_port_num} ({description}):")
        print(f"    {message}")
        if success:
            available_ports.append(test_port_num)

    print()
    print("=" * 60)
    print("📋 Summary:")
    print("=" * 60)

    if available_ports:
        print(
            f"✅ Found {len(available_ports)} accessible Tor port(s): {available_ports}"
        )
        print()
        print("🔧 To use an available port, update your .env file:")
        for port in available_ports:
            print(f"   SOCKS_URL=socks5h://127.0.0.1:{port}")
        print()
    else:
        print("❌ No Tor ports are accessible!")
        print()
        print("🔧 To fix this issue:")
        print()
        print("   Option 1: Start Tor Browser")
        print("      - Download from: https://www.torproject.org/download/")
        print("      - Open Tor Browser (it will start Tor on port 9150)")
        print("      - Update .env: SOCKS_URL=socks5h://127.0.0.1:9150")
        print()
        print("   Option 2: Install and start system Tor")
        print("      Windows:")
        print("        - Download Tor Expert Bundle from torproject.org")
        print("        - Extract and run: tor.exe")
        print("      Linux:")
        print("        - sudo apt install tor")
        print("        - sudo systemctl start tor")
        print("      macOS:")
        print("        - brew install tor")
        print("        - brew services start tor")
        print("      Then update .env: SOCKS_URL=socks5h://127.0.0.1:9050")
        print()
        print("   Option 3: Test without Tor (for clearnet URLs only)")
        print("      - Update .env: USE_SOCKS_PROXY=false")
        print("      - Note: This won't work for .onion domains!")
        print()

    return 0 if available_ports else 1


if __name__ == "__main__":
    sys.exit(main())
