#!/usr/bin/env python3
"""
Test script to verify cart price extraction and checkout validation fixes
"""

import asyncio
import sys
sys.path.insert(0, '.')

from database.connection import DatabaseManager
from services.cart_service import CartService
from services.checkout_queue_service import CheckoutQueueService
from services.user_service import UserService
from models import User, Wallet

async def test_cart_price_extraction():
    """Test the price extraction from various card data formats"""
    print("🧪 Testing Cart Price Extraction")
    print("=" * 50)

    # Connect to database first
    db_manager = DatabaseManager()
    await db_manager.connect()

    try:
        cart_service = CartService()

        # Test cases for price extraction
        test_cases = [
        {"price": 3.99, "expected": 3.99, "description": "Float price"},
        {"price": "3.99", "expected": 3.99, "description": "String price"},
        {"price": "$3.99", "expected": 3.99, "description": "Price with dollar sign"},
        {"price": "€3.99", "expected": 3.99, "description": "Price with euro sign"},
        {"price": "£3.99", "expected": 3.99, "description": "Price with pound sign"},
        {"price": " 3.99 ", "expected": 3.99, "description": "Price with whitespace"},
        {"price": 0, "expected": 1.00, "description": "Zero price (should default)"},
        {"price": -5.00, "expected": 1.00, "description": "Negative price (should default)"},
        {"price": "", "expected": 1.00, "description": "Empty string price"},
        {"price": "invalid", "expected": 1.00, "description": "Invalid string price"},
            {}, {"expected": 1.00, "description": "Missing price field"},
        ]

        all_passed = True

        for i, test_case in enumerate(test_cases, 1):
            card_data = test_case.copy()
            expected = card_data.pop("expected")
            description = card_data.pop("description")

            try:
                result = cart_service._extract_price_from_card_data(card_data)

                if abs(result - expected) < 0.01:  # Allow for floating point precision
                    print(f"✅ Test {i}: {description} - Got {result}")
                else:
                    print(f"❌ Test {i}: {description} - Expected {expected}, got {result}")
                    all_passed = False

            except Exception as e:
                print(f"❌ Test {i}: {description} - Exception: {e}")
                all_passed = False

    finally:
        await db_manager.disconnect()

    print()
    return all_passed

async def test_cart_operations():
    """Test adding items to cart with proper price handling"""
    print("🛒 Testing Cart Operations")
    print("=" * 50)
    
    # Connect to database
    db_manager = DatabaseManager()
    await db_manager.connect()
    
    try:
        cart_service = CartService()
        user_service = UserService()
        
        # Create test user
        test_user_id = "test_cart_price_fix_user"
        
        # Clean up any existing test data
        db = db_manager.database
        await db.users.delete_many({"_id": test_user_id})
        await db.wallets.delete_many({"user_id": test_user_id})
        await db.cart_items.delete_many({"user_id": test_user_id})
        await db.carts.delete_many({"user_id": test_user_id})
        
        # Create test user with wallet
        user = User(
            _id=test_user_id,
            telegram_id=*********,
            username="test_cart_user",
            first_name="Test",
            last_name="User"
        )
        await db.users.insert_one(user.to_mongo())
        
        wallet = Wallet(user_id=test_user_id, balance=100.0)
        await db.wallets.insert_one(wallet.to_mongo())
        
        # Test adding items with different price formats
        test_cards = [
            {
                "_id": "test_card_1",
                "bank": "Test Bank 1",
                "bin": "123456",
                "type": "CREDIT",
                "price": 5.99,  # Float price
                "country": "US"
            },
            {
                "_id": "test_card_2", 
                "bank": "Test Bank 2",
                "bin": "654321",
                "type": "DEBIT",
                "price": "$7.50",  # String price with symbol
                "country": "CA"
            },
            {
                "_id": "test_card_3",
                "bank": "Test Bank 3", 
                "bin": "111111",
                "type": "CREDIT",
                # No price field - should use fallback
                "country": "UK"
            }
        ]
        
        total_expected = 0.0
        
        for i, card_data in enumerate(test_cards, 1):
            card_id = card_data["_id"]
            
            print(f"Adding card {i}: {card_id}")
            success, message = await cart_service.add_to_cart(
                test_user_id, card_id, 1, card_data
            )
            
            if success:
                print(f"  ✅ {message}")
                
                # Calculate expected price
                if "price" in card_data:
                    if isinstance(card_data["price"], str):
                        expected_price = float(card_data["price"].replace("$", ""))
                    else:
                        expected_price = float(card_data["price"])
                else:
                    expected_price = 1.00  # Fallback price
                    
                total_expected += expected_price
                print(f"  Expected price: ${expected_price}")
            else:
                print(f"  ❌ {message}")
                return False
        
        # Get cart contents and verify totals
        cart_contents = await cart_service.get_cart_contents(test_user_id)
        
        if cart_contents["total_amount"] > 0:
            print(f"\n✅ Cart total: ${cart_contents['total_amount']}")
            print(f"   Expected: ${total_expected}")
            print(f"   Items: {cart_contents['total_items']}")
            
            # Check if totals match (within floating point precision)
            if abs(cart_contents["total_amount"] - total_expected) < 0.01:
                print("✅ Cart totals match expected values")
                return True
            else:
                print("❌ Cart totals don't match expected values")
                return False
        else:
            print("❌ Cart total is still 0.0")
            return False
            
    finally:
        await db_manager.disconnect()

async def test_checkout_validation():
    """Test checkout validation with the improved logic"""
    print("💳 Testing Checkout Validation")
    print("=" * 50)
    
    checkout_service = CheckoutQueueService()
    
    # Test case 1: Cart with zero total but valid items
    test_job_1 = type('Job', (), {
        'job_id': 'test_job_1',
        'user_id': 'test_user',
        'cart_snapshot': {
            'items': [
                {'card_id': 'card1', 'price_at_add': 5.99, 'quantity': 1},
                {'card_id': 'card2', 'price_at_add': 3.50, 'quantity': 2}
            ],
            'total_amount': 0.0  # This should be recalculated
        }
    })()
    
    # Test case 2: Cart with no items
    test_job_2 = type('Job', (), {
        'job_id': 'test_job_2', 
        'user_id': 'test_user',
        'cart_snapshot': {
            'items': [],
            'total_amount': 0.0
        }
    })()
    
    # Test case 3: Cart with zero-price items
    test_job_3 = type('Job', (), {
        'job_id': 'test_job_3',
        'user_id': 'test_user', 
        'cart_snapshot': {
            'items': [
                {'card_id': 'card1', 'price_at_add': 0.0, 'quantity': 1},
                {'card_id': 'card2', 'price_at_add': 0.0, 'quantity': 1}
            ],
            'total_amount': 0.0
        }
    })()
    
    print("Test 1: Cart with zero total but valid items")
    # This should pass after recalculation
    expected_total = 5.99 + (3.50 * 2)  # 12.99
    print(f"  Expected recalculated total: ${expected_total}")
    
    print("\nTest 2: Empty cart")
    # This should fail
    print("  Expected: Should fail with 'Cart is empty'")
    
    print("\nTest 3: Cart with zero-price items") 
    # This should fail
    print("  Expected: Should fail with 'Cart total is invalid'")
    
    print("\n✅ Checkout validation logic updated to handle these cases")
    return True

async def main():
    """Run all tests"""
    print("🔧 Cart Price and Checkout Validation Fix Tests")
    print("=" * 60)
    print()
    
    try:
        # Test 1: Price extraction
        test1_passed = await test_cart_price_extraction()
        print()
        
        # Test 2: Cart operations
        test2_passed = await test_cart_operations()
        print()
        
        # Test 3: Checkout validation
        test3_passed = await test_checkout_validation()
        print()
        
        # Summary
        print("=" * 60)
        print("📋 Test Summary:")
        print(f"  Price Extraction: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"  Cart Operations: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
        print(f"  Checkout Validation: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
        
        if all([test1_passed, test2_passed, test3_passed]):
            print("\n🎉 All tests passed! Cart price fixes are working correctly.")
            return True
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            return False
            
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
