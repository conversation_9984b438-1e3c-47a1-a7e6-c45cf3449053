#!/usr/bin/env python3
"""
Complete cart workflow test demonstrating:
1. Add items to cart
2. View cart contents
3. Clear cart using API v3 methods
4. Verify cart is empty
"""

import asyncio
import logging
import os
import sys
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(override=True)

from services.external_api_service import ExternalAPIService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_complete_cart_workflow():
    """Test complete cart workflow with add, view, and clear operations"""
    logger.info("🛒 Testing Complete Cart Workflow")
    logger.info("=" * 60)
    
    try:
        # Initialize external API service
        external_service = ExternalAPIService()
        logger.info(f"External API version: {external_service.api_version}")
        
        # Step 1: Get some items to add to cart
        logger.info("\n📋 Step 1: Get available items...")
        list_response = await external_service.list_items()
        
        if not list_response.success:
            logger.error(f"❌ Failed to get items: {list_response.error}")
            return False
        
        # Get first few items for testing
        items_data = list_response.data
        if isinstance(items_data, dict) and "data" in items_data:
            available_items = items_data["data"][:3]  # Take first 3 items
        else:
            logger.error("❌ Unexpected items data format")
            return False
        
        if not available_items:
            logger.error("❌ No items available for testing")
            return False
        
        logger.info(f"📦 Found {len(available_items)} items to add:")
        for i, item in enumerate(available_items, 1):
            item_id = item.get("id", "Unknown")
            item_name = item.get("name", "Unknown")
            logger.info(f"   Item {i}: {item_id} - {item_name}")
        
        # Step 2: Add items to cart
        logger.info("\n➕ Step 2: Add items to cart...")
        for i, item in enumerate(available_items, 1):
            item_id = item.get("id")
            if item_id:
                logger.info(f"   Adding item {i}: {item_id}")
                add_response = await external_service.add_to_cart([item_id])
                
                if add_response.success:
                    logger.info(f"   ✅ Successfully added item {i}")
                else:
                    logger.warning(f"   ⚠️ Failed to add item {i}: {add_response.error}")
        
        # Step 3: View cart contents
        logger.info("\n📋 Step 3: View cart contents...")
        view_response = await external_service.view_cart()
        
        if view_response.success:
            cart_data = view_response.data
            if isinstance(cart_data, dict) and "data" in cart_data:
                cart_items = cart_data["data"]
                logger.info(f"📦 Cart contains {len(cart_items)} items:")
                
                for i, item in enumerate(cart_items, 1):
                    item_id = item.get("id", "Unknown")
                    item_name = item.get("name", "Unknown")
                    logger.info(f"   Cart item {i}: {item_id} - {item_name}")
                
                if not cart_items:
                    logger.warning("⚠️ Cart is empty after adding items")
                    return False
            else:
                logger.error("❌ Unexpected cart data format")
                return False
        else:
            logger.error(f"❌ Failed to view cart: {view_response.error}")
            return False
        
        # Step 4: Clear cart using API v3
        logger.info("\n🧹 Step 4: Clear cart using API v3...")
        clear_response = await external_service.clear_cart()
        
        if clear_response.success:
            logger.info("✅ Cart cleared successfully")
        else:
            logger.error(f"❌ Failed to clear cart: {clear_response.error}")
            return False
        
        # Step 5: Verify cart is empty
        logger.info("\n📋 Step 5: Verify cart is empty...")
        verify_response = await external_service.view_cart()
        
        if verify_response.success:
            cart_data_after = verify_response.data
            if isinstance(cart_data_after, dict) and "data" in cart_data_after:
                remaining_items = cart_data_after["data"]
                logger.info(f"📦 Cart contains {len(remaining_items)} items after clearing")
                
                if not remaining_items:
                    logger.info("✅ Cart is successfully empty")
                    return True
                else:
                    logger.error(f"❌ Cart still contains {len(remaining_items)} items:")
                    for item in remaining_items:
                        item_id = item.get("id", "Unknown")
                        logger.error(f"   Remaining: {item_id}")
                    return False
            else:
                logger.error("❌ Unexpected cart data format after clearing")
                return False
        else:
            logger.error(f"❌ Failed to verify cart state: {verify_response.error}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def test_checkout_queue_service_integration():
    """Test that checkout queue service can use the new cart clearing methods"""
    logger.info("\n🔄 Testing Checkout Queue Service Integration")
    logger.info("=" * 60)
    
    try:
        from services.checkout_queue_service import CheckoutQueueService
        
        # Initialize checkout queue service
        checkout_service = CheckoutQueueService()
        
        logger.info("📋 Testing _clear_external_cart method...")
        
        # This should use the new API v3 clear_cart method
        success = await checkout_service._clear_external_cart()
        
        if success:
            logger.info("✅ Checkout queue service cart clearing succeeded")
            return True
        else:
            logger.error("❌ Checkout queue service cart clearing failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Checkout queue service test failed: {e}", exc_info=True)
        return False


async def main():
    """Run complete cart workflow tests"""
    logger.info("🚀 Starting Complete Cart Workflow Tests")
    logger.info("=" * 80)
    
    test_results = []
    
    # Test 1: Complete cart workflow
    result1 = await test_complete_cart_workflow()
    test_results.append(("Complete Cart Workflow", result1))
    
    # Test 2: Checkout queue service integration
    result2 = await test_checkout_queue_service_integration()
    test_results.append(("Checkout Queue Service Integration", result2))
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 80)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        logger.info("🎉 All cart workflow tests completed successfully!")
        logger.info("   The API v3 cart clearing integration is fully functional.")
        logger.info("   ✅ External API service routes to API v3 correctly")
        logger.info("   ✅ Checkout queue service uses API v3 cart clearing")
        logger.info("   ✅ CSRF token extraction works properly")
        logger.info("   ✅ Cart operations work end-to-end")
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
    
    return passed == len(test_results)


if __name__ == "__main__":
    asyncio.run(main())
