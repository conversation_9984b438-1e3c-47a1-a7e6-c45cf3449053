#!/usr/bin/env python3
"""
Simple test for price extraction logic without database dependencies
"""

import sys
sys.path.insert(0, '.')

def extract_price_from_card_data(card_data):
    """
    Safely extract price from card data, handling various formats
    
    Args:
        card_data: Card data dictionary
        
    Returns:
        Price as float, defaults to 1.00 if not found or invalid
    """
    try:
        price = card_data.get("price", 1.00)
        
        # Handle string prices
        if isinstance(price, str):
            # Remove any currency symbols and whitespace
            price_str = price.replace("$", "").replace("€", "").replace("£", "").strip()
            if price_str:
                price = float(price_str)
            else:
                price = 1.00
        
        # Handle numeric prices
        elif isinstance(price, (int, float)):
            price = float(price)
        
        # Ensure price is positive
        if price <= 0:
            print(f"Invalid price {price} in card data, using default 1.00")
            price = 1.00
            
        return round(price, 2)
        
    except (ValueError, TypeError) as e:
        print(f"Error extracting price from card data: {e}, using default 1.00")
        return 1.00

def test_price_extraction():
    """Test the price extraction from various card data formats"""
    print("🧪 Testing Price Extraction Logic")
    print("=" * 50)
    
    # Test cases for price extraction
    test_cases = [
        {"price": 3.99, "expected": 3.99, "description": "Float price"},
        {"price": "3.99", "expected": 3.99, "description": "String price"},
        {"price": "$3.99", "expected": 3.99, "description": "Price with dollar sign"},
        {"price": "€3.99", "expected": 3.99, "description": "Price with euro sign"},
        {"price": "£3.99", "expected": 3.99, "description": "Price with pound sign"},
        {"price": " 3.99 ", "expected": 3.99, "description": "Price with whitespace"},
        {"price": 0, "expected": 1.00, "description": "Zero price (should default)"},
        {"price": -5.00, "expected": 1.00, "description": "Negative price (should default)"},
        {"price": "", "expected": 1.00, "description": "Empty string price"},
        {"price": "invalid", "expected": 1.00, "description": "Invalid string price"},
        {"expected": 1.00, "description": "Missing price field"},
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        card_data = test_case.copy()
        expected = card_data.pop("expected")
        description = card_data.pop("description")
        
        try:
            result = extract_price_from_card_data(card_data)
            
            if abs(result - expected) < 0.01:  # Allow for floating point precision
                print(f"✅ Test {i}: {description} - Got {result}")
            else:
                print(f"❌ Test {i}: {description} - Expected {expected}, got {result}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Test {i}: {description} - Exception: {e}")
            all_passed = False
    
    print()
    return all_passed

def test_checkout_validation_logic():
    """Test checkout validation logic"""
    print("💳 Testing Checkout Validation Logic")
    print("=" * 50)
    
    def validate_cart_snapshot(cart_snapshot):
        """Simulate the improved cart validation logic"""
        items = cart_snapshot.get("items", [])
        total_amount = float(cart_snapshot.get("total_amount", 0.0))
        
        # Check if we have items
        if not items:
            return False, "Cart is empty"
        
        # If total_amount is 0 but we have items, recalculate from items
        if total_amount <= 0:
            calculated_total = sum(
                float(item.get("price_at_add", 0)) * int(item.get("quantity", 1))
                for item in items
            )
            
            if calculated_total <= 0:
                return False, "Cart total is invalid (all items have zero price)"
            
            # Update the total_amount for the rest of the checkout process
            total_amount = calculated_total
            cart_snapshot["total_amount"] = total_amount
        
        return True, f"Cart validated with total ${total_amount}"
    
    # Test cases
    test_cases = [
        {
            "description": "Cart with zero total but valid items",
            "cart_snapshot": {
                'items': [
                    {'card_id': 'card1', 'price_at_add': 5.99, 'quantity': 1},
                    {'card_id': 'card2', 'price_at_add': 3.50, 'quantity': 2}
                ],
                'total_amount': 0.0
            },
            "expected_success": True,
            "expected_total": 12.99
        },
        {
            "description": "Empty cart",
            "cart_snapshot": {
                'items': [],
                'total_amount': 0.0
            },
            "expected_success": False
        },
        {
            "description": "Cart with zero-price items",
            "cart_snapshot": {
                'items': [
                    {'card_id': 'card1', 'price_at_add': 0.0, 'quantity': 1},
                    {'card_id': 'card2', 'price_at_add': 0.0, 'quantity': 1}
                ],
                'total_amount': 0.0
            },
            "expected_success": False
        },
        {
            "description": "Cart with valid total",
            "cart_snapshot": {
                'items': [
                    {'card_id': 'card1', 'price_at_add': 5.99, 'quantity': 1}
                ],
                'total_amount': 5.99
            },
            "expected_success": True,
            "expected_total": 5.99
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        description = test_case["description"]
        cart_snapshot = test_case["cart_snapshot"].copy()
        expected_success = test_case["expected_success"]
        expected_total = test_case.get("expected_total")
        
        print(f"Test {i}: {description}")
        
        success, message = validate_cart_snapshot(cart_snapshot)
        
        if success == expected_success:
            print(f"  ✅ Success status: {success} - {message}")
            
            if expected_total is not None and success:
                actual_total = cart_snapshot.get("total_amount", 0.0)
                if abs(actual_total - expected_total) < 0.01:
                    print(f"  ✅ Total amount: ${actual_total}")
                else:
                    print(f"  ❌ Total amount: Expected ${expected_total}, got ${actual_total}")
                    all_passed = False
        else:
            print(f"  ❌ Expected success={expected_success}, got success={success}")
            print(f"     Message: {message}")
            all_passed = False
        
        print()
    
    return all_passed

def main():
    """Run all tests"""
    print("🔧 Cart Price and Checkout Validation Fix Tests")
    print("=" * 60)
    print()
    
    try:
        # Test 1: Price extraction
        test1_passed = test_price_extraction()
        
        # Test 2: Checkout validation
        test2_passed = test_checkout_validation_logic()
        
        # Summary
        print("=" * 60)
        print("📋 Test Summary:")
        print(f"  Price Extraction: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"  Checkout Validation: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
        
        if all([test1_passed, test2_passed]):
            print("\n🎉 All tests passed! Cart price fixes are working correctly.")
            return True
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            return False
            
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
