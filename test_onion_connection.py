#!/usr/bin/env python3
"""
Test .onion domain connectivity through Tor

This script tests if we can reach .onion domains through Tor.
"""
import os
import sys
import requests
from dotenv import load_dotenv

# Load environment
load_dotenv()


def test_onion_connection(onion_url: str, socks_url: str, timeout: int = 15):
    """Test connection to an .onion domain"""
    print(f"🔍 Testing connection to: {onion_url}")
    print(f"📡 Using SOCKS proxy: {socks_url}")
    print(f"⏱️  Timeout: {timeout} seconds")
    print()

    try:
        # Create session with SOCKS proxy
        session = requests.Session()
        session.proxies = {
            "http": socks_url,
            "https": socks_url,
        }

        # Set headers
        session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; rv:128.0) Gecko/20100101 Firefox/128.0"
            }
        )

        print("🚀 Attempting connection...")
        response = session.get(onion_url, timeout=timeout)

        print(f"✅ Connection successful!")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Length: {len(response.text)} bytes")
        print(f"   Content Type: {response.headers.get('Content-Type', 'N/A')}")

        # Check if it's a login page
        if "/login" in onion_url.lower() or "login" in response.text.lower():
            print(f"   🔐 Appears to be a login page")

        return True

    except requests.exceptions.ConnectTimeout:
        print(f"❌ Connection timed out after {timeout} seconds")
        print()
        print("💡 Possible causes:")
        print("   1. The .onion site might be down or unreachable")
        print("   2. Tor Browser needs more time to establish circuit")
        print("   3. The onion address might be incorrect")
        print()
        print("🔧 Suggestions:")
        print("   - Try opening the URL in Tor Browser first")
        print("   - Wait a few seconds and try again")
        print("   - Increase timeout value")
        return False

    except requests.exceptions.ProxyError as e:
        print(f"❌ Proxy error: {e}")
        print()
        print("💡 This usually means:")
        print("   1. Tor Browser is not running properly")
        print("   2. SOCKS proxy port is incorrect")
        print("   3. Tor is starting up (wait a moment and retry)")
        return False

    except Exception as e:
        print(f"❌ Error: {type(e).__name__}: {e}")
        return False


def main():
    print("=" * 70)
    print("🧅 Tor .onion Domain Connection Test")
    print("=" * 70)
    print()

    # Get configuration
    base_url = os.getenv("API_V3_BASE_URL", "").strip('"')
    socks_url = os.getenv("SOCKS_URL", "socks5h://127.0.0.1:9150")

    if not base_url:
        print("❌ Error: API_V3_BASE_URL not found in .env")
        return 1

    print(f"📋 Configuration:")
    print(f"   Base URL: {base_url}")
    print(f"   SOCKS Proxy: {socks_url}")
    print()

    # Test base URL first
    print("-" * 70)
    print("Test 1: Base URL")
    print("-" * 70)
    success1 = test_onion_connection(base_url, socks_url, timeout=30)
    print()

    # Test login URL
    login_url = base_url.rstrip("/") + "/login"
    print("-" * 70)
    print("Test 2: Login URL")
    print("-" * 70)
    success2 = test_onion_connection(login_url, socks_url, timeout=30)
    print()

    # Summary
    print("=" * 70)
    print("📊 Summary")
    print("=" * 70)

    if success1 or success2:
        print("✅ Successfully connected to .onion domain!")
        print()
        print("🎉 Your Tor configuration is working correctly.")
        print("   You can now run the bot with API v3 enabled.")
        return 0
    else:
        print("❌ Could not connect to .onion domain")
        print()
        print("🔧 Troubleshooting steps:")
        print()
        print("1. Verify Tor Browser is running:")
        print("   - Open Tor Browser")
        print("   - Wait for 'Connected to Tor Network' message")
        print("   - Keep it running in the background")
        print()
        print("2. Test the .onion URL manually:")
        print(f"   - Open in Tor Browser: {base_url}")
        print("   - Verify the site loads")
        print()
        print("3. Check if the .onion address is correct:")
        print(f"   - Current address: {base_url}")
        print("   - Verify this matches your service")
        print()
        print("4. Try running this test again after a few seconds")
        print()
        return 1


if __name__ == "__main__":
    sys.exit(main())
