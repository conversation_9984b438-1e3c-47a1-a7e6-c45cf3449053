
BOT_TOKEN=7919576856:AAHLNmuWVGcC-vaj6iuRYLNrtun46jGzVAk
DEMO_API_BASE=https://demo.api.example
DEMO_API_TOKEN=
# Note: Leave DEMO_API_TOKEN empty to use fallback mode with local catalog

# Database settings
USE_MONGODB=true
DATABASE_URL=sqlite:///demo_wallet_bot.db
MONGODB_URL=mongodb+srv://alpha:<EMAIL>/democc?retryWrites=true&w=majority&authSource=admin&appName=Cluster0
DATABASE_NAME=democc
MONGODB_AUTH_SOURCE=admin
MONGODB_AUTH_MECHANISM=SCRAM-SHA-256
# Admin settings (comma-separated Telegram user IDs)
ADMIN_USER_IDS=6382814265
ADMIN_PASSPHRASE=SecureAdminPass2024!

# Initial settings
INITIAL_BALANCE=100.00
DEFAULT_CURRENCY=USD

# Sanctions & Compliance (comma-separated values)
SANCTIONED_COUNTRIES=CU,IR,KP,SY,UA-CRIMEA
AML_HOURLY_LIMIT=200
DAILY_SPEND_CAP=500
MONTHLY_SPEND_CAP=2000

# Rate limits (per-user)
PURCHASES_PER_MINUTE=3
PURCHASES_PER_DAY=50
SEARCHES_PER_MINUTE=10
MESSAGES_PER_MINUTE=10
CALLBACKS_PER_MINUTE=20

# Fallback generator
FALLBACK_ENABLED=true

# Security Configuration
ENVIRONMENT=development
API_ENCRYPTION_KEY=secure_encryption_key_for_api_data_123456789
API_ENCRYPTION_SALT=secure_salt_for_encryption_987654321
API_CONFIG_ENCRYPTION_KEY=zws03BpEIKLFNxfZ8YF1pIKcwl-Ug3HSAxH7riUwxmY=

# Observability
LOG_LEVEL=INFO
LOG_TO_FILE=false
LOG_FILE_PATH=logs/bot.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5
LOG_COLOR=true
LOG_STRUCTURED=true
LOG_SHOW_CATEGORY=false
METRICS_ENABLED=true
METRICS_PORT=8000
RETENTION_ENABLED=true
RETENTION_DAYS=45

EXTERNAL_LOGIN_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk5MDk0LCJpYXQiOjE3NTc3NjM4NTMsImV4cCI6MTc2MDM1NTg1M30.BKbfNvqOTQKGyO_kyrnqP2SIBNcFRnp2Iy3kqgY1Cxo
EXTERNAL_DDG1=
EXTERNAL_DDG8=k4Z3HstdcwqdTUou
EXTERNAL_DDG9=103.176.152.23
EXTERNAL_DDG10=k4Z3HstdcwqdTUou
EXTERNAL_GA=
EXTERNAL_GA_KZWCRF57VT=

# API v3 (HTML/Tor) Configuration - Integrated from api3
EXTERNAL_API_VERSION=v3

API_V3_BASE_URL="http://blgnjdcvrpavgdtt7xhrk6mqvowtq6bp56lyzoktr3n5lwfwdrklfxid.onion"
API_V3_USERNAME="justine_r7v5f"
API_V3_PASSWORD="Chetas@1234"

# SOCKS Proxy for .onion domains (Tor)
USE_SOCKS_PROXY=true
SOCKS_URL=socks5h://127.0.0.1:9150