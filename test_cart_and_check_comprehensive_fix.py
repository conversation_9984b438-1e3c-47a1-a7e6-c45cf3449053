#!/usr/bin/env python3
"""
Comprehensive Fix for Cart and Check Card Issues

This script creates fixes for both cart display and check card button issues.
"""

import asyncio
import sys
import os
from typing import Dict, Any
sys.path.insert(0, '.')

def analyze_cart_error_sources():
    """Analyze potential sources of cart display errors"""
    print("🔍 ANALYZING CART ERROR SOURCES")
    print("=" * 60)
    
    issues_found = []
    fixes_needed = []
    
    try:
        # Check cart service error handling
        with open('services/cart_service.py', 'r') as f:
            cart_service_code = f.read()
        
        print("📋 Cart Service Analysis:")
        
        # Check format_cart_for_display error handling
        if 'def format_cart_for_display(' in cart_service_code:
            format_start = cart_service_code.find('def format_cart_for_display(')
            format_end = cart_service_code.find('\n    async def', format_start)
            if format_end == -1:
                format_end = cart_service_code.find('\n    @monitor_performance', format_start)
            
            if format_end != -1:
                format_method = cart_service_code[format_start:format_end]
                
                has_try_catch = 'try:' in format_method and 'except Exception as e:' in format_method
                has_error_return = 'Error displaying cart contents' in format_method
                
                print(f"   • Has try-catch: {'✅' if has_try_catch else '❌'}")
                print(f"   • Has error return: {'✅' if has_error_return else '❌'}")
                
                if not has_try_catch:
                    issues_found.append("format_cart_for_display missing try-catch")
                    fixes_needed.append("Add try-catch to format_cart_for_display")
                
                # Check for card_data access patterns
                card_data_accesses = format_method.count('card_data.get(')
                print(f"   • Card data accesses: {card_data_accesses}")
                
                if card_data_accesses == 0:
                    issues_found.append("No card_data.get() calls found")
                    fixes_needed.append("Verify card data access patterns")
        
        # Check get_cart_contents error handling
        if 'async def get_cart_contents(' in cart_service_code:
            get_contents_start = cart_service_code.find('async def get_cart_contents(')
            get_contents_end = cart_service_code.find('\n    async def', get_contents_start)
            if get_contents_end == -1:
                get_contents_end = cart_service_code.find('\n    def', get_contents_start)
            
            if get_contents_end != -1:
                get_contents_method = cart_service_code[get_contents_start:get_contents_end]
                
                has_error_field = '"error"' in get_contents_method
                has_exception_handling = 'except Exception as e:' in get_contents_method
                
                print(f"   • Returns error field: {'✅' if has_error_field else '❌'}")
                print(f"   • Has exception handling: {'✅' if has_exception_handling else '❌'}")
                
                if not has_error_field:
                    issues_found.append("get_cart_contents doesn't return error field")
                    fixes_needed.append("Add error field to get_cart_contents return")
        
        return issues_found, fixes_needed
        
    except Exception as e:
        print(f"❌ Error analyzing cart service: {e}")
        return ["Analysis failed"], ["Manual code review needed"]

def analyze_order_details_error():
    """Analyze the 'Error loading card details' in order details"""
    print(f"\n🔧 ANALYZING ORDER DETAILS ERROR")
    print("=" * 60)
    
    issues_found = []
    fixes_needed = []
    
    try:
        with open('handlers/orders_handlers.py', 'r') as f:
            orders_code = f.read()
        
        print("📋 Order Details Analysis:")
        
        # Check _format_order_details method
        if 'def _format_order_details(' in orders_code:
            format_start = orders_code.find('def _format_order_details(')
            format_end = orders_code.find('\n    def', format_start)
            
            if format_end != -1:
                format_method = orders_code[format_start:format_end]
                
                has_try_catch = 'try:' in format_method and 'except Exception as e:' in format_method
                has_error_message = 'Error loading card details' in format_method
                has_ui_formatter = 'UIFormatter' in format_method
                
                print(f"   • Has try-catch: {'✅' if has_try_catch else '❌'}")
                print(f"   • Has error message: {'✅' if has_error_message else '❌'}")
                print(f"   • Uses UIFormatter: {'✅' if has_ui_formatter else '❌'}")
                
                if has_ui_formatter:
                    issues_found.append("_format_order_details imports UIFormatter which may not exist")
                    fixes_needed.append("Remove UIFormatter import or create the module")
                
                # Check for order data access patterns
                order_get_calls = format_method.count('order.get(')
                print(f"   • Order data accesses: {order_get_calls}")
                
                if order_get_calls < 5:
                    issues_found.append("Limited order data access - may cause errors")
                    fixes_needed.append("Add defensive programming for order data access")
        
        return issues_found, fixes_needed
        
    except Exception as e:
        print(f"❌ Error analyzing orders handlers: {e}")
        return ["Analysis failed"], ["Manual code review needed"]

def analyze_check_card_button_issues():
    """Analyze check card button implementation"""
    print(f"\n🔄 ANALYZING CHECK CARD BUTTON ISSUES")
    print("=" * 60)
    
    issues_found = []
    fixes_needed = []
    
    try:
        with open('handlers/orders_handlers.py', 'r') as f:
            orders_code = f.read()
        
        print("📋 Check Card Button Analysis:")
        
        # Check cb_check_card method
        if 'async def cb_check_card(' in orders_code:
            check_start = orders_code.find('async def cb_check_card(')
            check_end = orders_code.find('\n    async def', check_start)
            if check_end == -1:
                check_end = orders_code.find('\n    def', check_start)
            
            if check_end != -1:
                check_method = orders_code[check_start:check_end]
                
                has_data_parsing = 'callback.data or "").split(":")' in check_method
                has_parts_validation = 'len(parts) not in (4, 5)' in check_method
                has_api_call = 'check_order(' in check_method
                has_error_handling = 'except Exception as e:' in check_method
                
                print(f"   • Parses callback data: {'✅' if has_data_parsing else '❌'}")
                print(f"   • Validates parts: {'✅' if has_parts_validation else '❌'}")
                print(f"   • Calls check_order API: {'✅' if has_api_call else '❌'}")
                print(f"   • Has error handling: {'✅' if has_error_handling else '❌'}")
                
                if not has_api_call:
                    issues_found.append("cb_check_card doesn't call check_order API")
                    fixes_needed.append("Add check_order API call to cb_check_card")
        
        # Check router registration
        if 'def get_orders_router(' in orders_code:
            router_start = orders_code.find('def get_orders_router(')
            router_end = orders_code.find('\n\n', router_start)
            
            if router_end != -1:
                router_method = orders_code[router_start:router_end]
                
                has_check_registration = 'cb_check_card' in router_method and 'orders:check:' in router_method
                print(f"   • Registers check callback: {'✅' if has_check_registration else '❌'}")
                
                if not has_check_registration:
                    issues_found.append("cb_check_card not registered in router")
                    fixes_needed.append("Register cb_check_card in get_orders_router")
        
        # Check button creation in view card
        if 'async def cb_view_purchased_card(' in orders_code:
            view_start = orders_code.find('async def cb_view_purchased_card(')
            view_end = orders_code.find('\n    async def', view_start)
            
            if view_end != -1:
                view_method = orders_code[view_start:view_end]
                
                has_check_button = 'Check Card Status' in view_method or 'orders:check:' in view_method
                has_keyboard_creation = 'InlineKeyboardMarkup' in view_method
                
                print(f"   • Creates check button: {'✅' if has_check_button else '❌'}")
                print(f"   • Creates keyboard: {'✅' if has_keyboard_creation else '❌'}")
                
                if not has_check_button:
                    issues_found.append("View card doesn't create check button")
                    fixes_needed.append("Add check button to view card keyboard")
        
        return issues_found, fixes_needed
        
    except Exception as e:
        print(f"❌ Error analyzing check card button: {e}")
        return ["Analysis failed"], ["Manual code review needed"]

def create_fix_recommendations():
    """Create comprehensive fix recommendations"""
    print(f"\n📋 CREATING FIX RECOMMENDATIONS")
    print("=" * 60)
    
    # Analyze all issues
    cart_issues, cart_fixes = analyze_cart_error_sources()
    order_issues, order_fixes = analyze_order_details_error()
    check_issues, check_fixes = analyze_check_card_button_issues()
    
    all_issues = cart_issues + order_issues + check_issues
    all_fixes = cart_fixes + order_fixes + check_fixes
    
    print(f"\n📊 SUMMARY:")
    print(f"   • Total issues found: {len(all_issues)}")
    print(f"   • Total fixes needed: {len(all_fixes)}")
    
    if all_issues:
        print(f"\n❌ ISSUES IDENTIFIED:")
        for i, issue in enumerate(all_issues, 1):
            print(f"   {i}. {issue}")
    
    if all_fixes:
        print(f"\n🔧 FIXES RECOMMENDED:")
        for i, fix in enumerate(all_fixes, 1):
            print(f"   {i}. {fix}")
    
    # Priority fixes
    priority_fixes = []
    
    if any("UIFormatter" in issue for issue in all_issues):
        priority_fixes.append("HIGH: Fix UIFormatter import in _format_order_details")
    
    if any("check_order" in issue for issue in all_issues):
        priority_fixes.append("HIGH: Fix check_order API call in cb_check_card")
    
    if any("error field" in issue for issue in all_issues):
        priority_fixes.append("MEDIUM: Add error handling to get_cart_contents")
    
    if priority_fixes:
        print(f"\n🚨 PRIORITY FIXES:")
        for i, fix in enumerate(priority_fixes, 1):
            print(f"   {i}. {fix}")
    
    return all_issues, all_fixes, priority_fixes

def main():
    """Run comprehensive analysis and create fix recommendations"""
    print("🔍 COMPREHENSIVE CART AND CHECK CARD ANALYSIS")
    print("=" * 60)
    
    try:
        issues, fixes, priority_fixes = create_fix_recommendations()
        
        print(f"\n🎯 ANALYSIS COMPLETE")
        print(f"   • Issues identified: {len(issues)}")
        print(f"   • Fixes recommended: {len(fixes)}")
        print(f"   • Priority fixes: {len(priority_fixes)}")
        
        if len(issues) == 0:
            print(f"\n✅ NO CRITICAL ISSUES FOUND")
            print(f"   The code appears to be correctly implemented.")
            print(f"   Issues may be runtime-related or configuration-related.")
        else:
            print(f"\n📋 NEXT STEPS:")
            print(f"1. Address priority fixes first")
            print(f"2. Test each fix individually")
            print(f"3. Verify end-to-end functionality")
            print(f"4. Monitor for runtime errors")
        
        return len(priority_fixes) == 0
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
