#!/usr/bin/env python3
"""
Test script to verify that the cart service is working correctly with string card IDs
and the updated CartItem model.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv
load_dotenv()

from services.cart_service import CartService
from models.catalog import CartItem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_cart_service_with_string_ids():
    """Test that cart service works with string card IDs."""
    logger.info("🧪 Testing Cart Service with String Card IDs")
    logger.info("=" * 50)
    
    try:
        # Initialize cart service
        cart_service = CartService()
        
        # Test user ID
        test_user_id = "test_user_cart_fix"
        
        # Test card ID (string hash)
        test_card_id = "03faed8f5a9b80fd0c074c423138ee110b1143a3"
        
        # Test card data (minimal required data)
        test_card_data = {
            "_id": test_card_id,
            "bin": "123456",
            "name": "Test Card",
            "country": "Test Country", 
            "price": 12.0,
            "brand": "Test Brand",
            "type": "Test Type",
            "level": "Test Level",
            "quality": "Test Quality",
            "expiry": "12/25"
        }
        
        logger.info(f"🔍 Testing with card ID: {test_card_id}")
        logger.info(f"   Card ID type: {type(test_card_id).__name__}")
        logger.info(f"   Card ID length: {len(test_card_id)}")
        
        # Clear any existing cart items for this user
        logger.info("🧹 Clearing existing cart items...")
        await cart_service.clear_cart(test_user_id)
        
        # Test adding item to cart
        logger.info("📝 Testing add_to_cart...")
        success, message = await cart_service.add_to_cart(
            test_user_id, test_card_id, 1, test_card_data
        )
        
        if not success:
            logger.error(f"❌ Failed to add item to cart: {message}")
            return False
        
        logger.info(f"✅ Successfully added item to cart: {message}")
        
        # Test getting cart items
        logger.info("📋 Testing get_cart_items...")
        cart_items = await cart_service.get_cart_items(test_user_id)
        
        if not cart_items:
            logger.error("❌ No cart items found after adding")
            return False
        
        logger.info(f"✅ Found {len(cart_items)} cart items")
        
        # Verify the cart item
        cart_item = cart_items[0]
        logger.info(f"   Item ID: {cart_item.card_id}")
        logger.info(f"   Item ID type: {type(cart_item.card_id).__name__}")
        logger.info(f"   Quantity: {cart_item.quantity}")
        logger.info(f"   Price: ${cart_item.price_at_add}")
        
        if cart_item.card_id != test_card_id:
            logger.error(f"❌ Card ID mismatch: expected {test_card_id}, got {cart_item.card_id}")
            return False
        
        if not isinstance(cart_item.card_id, str):
            logger.error(f"❌ Card ID should be string, got {type(cart_item.card_id).__name__}")
            return False
        
        # Test cart count
        logger.info("🔢 Testing get_cart_item_count...")
        cart_count = await cart_service.get_cart_item_count(test_user_id)
        
        if cart_count != 1:
            logger.error(f"❌ Expected cart count 1, got {cart_count}")
            return False
        
        logger.info(f"✅ Cart count correct: {cart_count}")
        
        # Test updating quantity
        logger.info("📈 Testing update_cart_item_quantity...")
        success, message = await cart_service.update_cart_item_quantity(
            test_user_id, test_card_id, 3
        )
        
        if not success:
            logger.error(f"❌ Failed to update quantity: {message}")
            return False
        
        logger.info(f"✅ Successfully updated quantity: {message}")
        
        # Verify quantity update
        updated_items = await cart_service.get_cart_items(test_user_id)
        if not updated_items or updated_items[0].quantity != 3:
            logger.error("❌ Quantity update failed")
            return False
        
        logger.info("✅ Quantity update verified")
        
        # Test removing item
        logger.info("🗑️  Testing remove_from_cart...")
        success, message = await cart_service.remove_from_cart(test_user_id, test_card_id)
        
        if not success:
            logger.error(f"❌ Failed to remove item: {message}")
            return False
        
        logger.info(f"✅ Successfully removed item: {message}")
        
        # Verify removal
        final_items = await cart_service.get_cart_items(test_user_id)
        if final_items:
            logger.error(f"❌ Expected empty cart, found {len(final_items)} items")
            return False
        
        logger.info("✅ Item removal verified")
        
        logger.info("🎉 All cart service tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def test_cart_item_model():
    """Test the CartItem model directly."""
    logger.info("\n🏗️  Testing CartItem Model")
    logger.info("=" * 50)
    
    try:
        # Test card ID (string hash)
        test_card_id = "03faed8f5a9b80fd0c074c423138ee110b1143a3"
        
        # Test card data (minimal required data)
        test_card_data = {
            "_id": test_card_id,
            "bin": "123456",
            "name": "Test Card",
            "price": 12.0
        }
        
        logger.info(f"🔍 Testing CartItem model with card ID: {test_card_id}")
        
        # Create CartItem instance
        cart_item = CartItem(
            user_id="test_user",
            card_id=test_card_id,  # This should now accept string
            card_data=test_card_data,
            quantity=2,
            price_at_add=12.0,
            currency="USD"
        )
        
        logger.info("✅ CartItem created successfully")
        logger.info(f"   Card ID: {cart_item.card_id}")
        logger.info(f"   Card ID type: {type(cart_item.card_id).__name__}")
        logger.info(f"   Quantity: {cart_item.quantity}")
        logger.info(f"   Total price: ${cart_item.get_total_price()}")
        
        # Verify card ID is string
        if not isinstance(cart_item.card_id, str):
            logger.error(f"❌ Card ID should be string, got {type(cart_item.card_id).__name__}")
            return False
        
        # Verify total price calculation
        expected_total = 12.0 * 2
        if cart_item.get_total_price() != expected_total:
            logger.error(f"❌ Total price calculation failed: expected {expected_total}, got {cart_item.get_total_price()}")
            return False
        
        logger.info("✅ CartItem model test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ CartItem model test failed: {e}", exc_info=True)
        return False


async def main():
    """Run all tests."""
    logger.info("🚀 Starting Cart Service Fix Tests")
    logger.info("=" * 60)
    
    test1_result = await test_cart_item_model()
    test2_result = await test_cart_service_with_string_ids()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"CartItem Model Test: {'✅ PASSED' if test1_result else '❌ FAILED'}")
    logger.info(f"Cart Service Test: {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    overall_success = test1_result and test2_result
    logger.info(f"\nOverall Result: {'🎉 ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("\n✅ The cart service fix is working correctly!")
        logger.info("   CartItem model now accepts string card IDs.")
        logger.info("   Cart operations work with string-based card IDs.")
        logger.info("   Pydantic validation errors should be resolved.")
    else:
        logger.error("\n❌ Some tests failed. Please review the errors above.")
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
