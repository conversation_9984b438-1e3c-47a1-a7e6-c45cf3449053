#!/usr/bin/env python3
"""
Comprehensive test to verify both API v1 and API v2 are working correctly
"""

import asyncio
import logging

from services.card_service import CardService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_both_apis():
    """Test both API v1 and API v2"""
    print("============================================================")
    print("Comprehensive API v1 vs API v2 Test")
    print("============================================================")
    
    results = {"api_v1": False, "api_v2": False}
    
    # Test API v2 (default)
    print("\n🔹 Testing API v2...")
    try:
        card_service_v2 = CardService(use_api_v2=True)
        result_v2 = await card_service_v2.fetch_cards(page=1, limit=3, user_id='test_user')
        
        if result_v2.get("success", False):
            cards_v2 = result_v2.get("data", [])
            total_v2 = result_v2.get("totalCount", 0)
            print(f"✅ API v2 working! Found {len(cards_v2)} cards (total: {total_v2:,})")
            
            # Show sample card
            if cards_v2:
                card = cards_v2[0]
                print(f"   Sample: BIN {card.get('bin', 'N/A')} | {card.get('country', 'N/A')} | ${card.get('price', 'N/A')}")
            
            results["api_v2"] = True
        else:
            print(f"❌ API v2 failed: {result_v2.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ API v2 error: {e}")
    
    # Test API v1 fallback
    print("\n🔹 Testing API v1...")
    try:
        card_service_v1 = CardService(use_api_v2=False)
        result_v1 = await card_service_v1.fetch_cards(page=1, limit=3, user_id='test_user')
        
        if result_v1.get("success", False):
            cards_v1 = result_v1.get("data", [])
            total_v1 = result_v1.get("totalCount", 0)
            print(f"✅ API v1 working! Found {len(cards_v1)} cards (total: {total_v1:,})")
            
            # Show sample card
            if cards_v1:
                card = cards_v1[0]
                print(f"   Sample: BIN {card.get('bin', 'N/A')} | {card.get('country', 'N/A')} | ${card.get('price', 'N/A')}")
            
            results["api_v1"] = True
        else:
            print(f"❌ API v1 failed: {result_v1.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ API v1 error: {e}")
    
    # Summary
    print("\n============================================================")
    print("📊 Test Results Summary:")
    print(f"   API v1: {'✅ WORKING' if results['api_v1'] else '❌ FAILED'}")
    print(f"   API v2: {'✅ WORKING' if results['api_v2'] else '❌ FAILED'}")
    
    if results["api_v1"] and results["api_v2"]:
        print("\n🎉 SUCCESS: Both API v1 and API v2 are working correctly!")
        print("   - Authentication issues resolved")
        print("   - Response parsing fixed")
        print("   - Card service supports both APIs")
        return True
    elif results["api_v2"]:
        print("\n✅ API v2 is working (primary goal achieved)")
        print("❌ API v1 fallback has issues")
        return True
    else:
        print("\n💥 FAILURE: Critical issues remain")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_both_apis())
    print(f"\nOverall result: {'PASS' if success else 'FAIL'}")
