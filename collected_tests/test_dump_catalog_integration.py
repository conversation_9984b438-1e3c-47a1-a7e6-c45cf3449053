"""
Test dump catalog integration workflow
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.dump_service import get_dump_service
from services.product_service import ProductService
from models.product import ProductType


async def test_dump_catalog_integration():
    """Test the dump catalog integration workflow"""
    print("🧪 Testing Dump Catalog Integration...")

    try:
        # Test 1: Product Service Integration
        print("\n1️⃣ Testing Product Service...")
        product_service = ProductService()
        print("✅ Product service instance created")

        # Test 2: Dump Service Integration
        print("\n2️⃣ Testing Dump Service Integration...")
        dump_service = get_dump_service()
        print("✅ Dump service instance created")

        # Test 3: Simulate catalog workflow for dumps v1
        print("\n3️⃣ Testing Dumps v1 Catalog Workflow...")
        current_product = ProductType.DUMP
        current_api = "v1"

        if current_product == ProductType.DUMP:
            if current_api == "v1":
                cards_data = await dump_service.list_dumps(page=1, limit=5)
            else:
                cards_data = await dump_service.list_vdumps(page=1, limit=5)

            print(f"✅ Dumps v1 workflow success: {cards_data.get('success', False)}")
            if cards_data.get("success"):
                data = cards_data.get("data", [])
                print(f"   Retrieved {len(data)} dumps")
                if data:
                    print(
                        f"   Sample: {data[0].get('bin', 'N/A')} - {data[0].get('country', 'N/A')}"
                    )

        # Test 4: Simulate catalog workflow for vdumps v2
        print("\n4️⃣ Testing VDumps v2 Catalog Workflow...")
        current_api = "v2"

        if current_product == ProductType.DUMP:
            if current_api == "v1":
                cards_data = await dump_service.list_dumps(page=1, limit=5)
            else:
                cards_data = await dump_service.list_vdumps(page=1, limit=5)

            print(f"✅ VDumps v2 workflow success: {cards_data.get('success', False)}")
            if cards_data.get("success"):
                data = cards_data.get("data", [])
                print(f"   Retrieved {len(data)} vdumps")
                if data:
                    print(
                        f"   Sample: {data[0].get('bin', 'N/A')} - {data[0].get('country', 'N/A')}"
                    )

        # Test 5: Error handling simulation
        print("\n5️⃣ Testing Error Handling...")
        if not cards_data.get("success", False):
            if current_product == ProductType.DUMP:
                api_status = "Dump API is currently unavailable"
            else:
                api_status = "Card API error"
            print(f"✅ Error handling works: {api_status}")
        else:
            print("✅ No errors to handle - all services working")

        print("\n🎉 All tests passed! Dump catalog integration is working correctly.")
        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = asyncio.run(test_dump_catalog_integration())
    print(f"\n{'✅ SUCCESS' if result else '❌ FAILED'}: Dump catalog integration test")
    sys.exit(0 if result else 1)
