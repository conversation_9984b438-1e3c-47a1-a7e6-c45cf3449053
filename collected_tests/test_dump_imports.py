"""
Simple import test for dump APIs without database dependencies
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_imports():
    """Test all imports without database dependencies"""
    print("🧪 Testing Dump APIs Imports")
    print("=" * 40)

    try:
        # Test service import
        print("1. Testing service import...")
        from services.dump_service import get_dump_service, DumpService

        print("✅ Dump service imported successfully")

        # Test product model updates
        print("2. Testing product model...")
        from models.product import DEFAULT_PRODUCT_CONFIG, ProductType

        products = DEFAULT_PRODUCT_CONFIG.products
        dump_product = None
        for product in products:
            if product.type == ProductType.DUMP:
                dump_product = product
                break

        if dump_product:
            print(f"✅ Dump product found: {dump_product.name}")
            print(f"   - APIs: {len(dump_product.apis)}")
            active_apis = dump_product.get_active_apis()
            print(f"   - Active APIs: {len(active_apis)}")
            for api in active_apis:
                print(f"     * {api.name} ({api.status.value})")
        else:
            print("❌ Dump product not found")

        # Test main menu integration
        print("3. Testing main menu integration...")
        from utils.keyboards import enhanced_main_menu_keyboard

        keyboard = enhanced_main_menu_keyboard()

        # Look for dump button
        dump_button_found = False
        for row in keyboard.inline_keyboard:
            for button in row:
                if "Dumps" in button.text:
                    dump_button_found = True
                    print(
                        f"✅ Dump button found: '{button.text}' -> '{button.callback_data}'"
                    )
                    break

        if not dump_button_found:
            print("❌ Dump button not found in main menu")

        # Test handlers import (just the module, not instantiation)
        print("4. Testing handlers module import...")
        import handlers.dump_handlers

        print("✅ Dump handlers module imported successfully")

        print("\n" + "=" * 40)
        print("✅ All import tests passed!")
        return True

    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
