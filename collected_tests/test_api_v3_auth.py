"""
Test API v3 Authentication and Browse

This script tests the fixed API v3 implementation with:
- Session-based authentication
- SOCKS proxy for .onion domains
- Form-based login with CSRF tokens
"""

import asyncio
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%H:%M:%S",
)

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_v3.auth.session_handler import APIV3SessionHandler
from api_v3 import get_api_v3_browse_service, APIV3BrowseParams
from dotenv import load_dotenv
import os

# Load environment
load_dotenv()


async def test_authentication():
    """Test API v3 authentication flow"""
    print("\n" + "=" * 60)
    print("TEST 1: Authentication")
    print("=" * 60)

    base_url = os.getenv("API_V3_BASE_URL", "").strip().strip('"')
    username = os.getenv("API_V3_USERNAME", "").strip().strip('"')
    password = os.getenv("API_V3_PASSWORD", "").strip().strip('"')

    if not all([base_url, username, password]):
        print("❌ Missing API v3 credentials in .env")
        return False

    print(f"Base URL: {base_url}")
    print(f"Username: {username}")
    print(f"Using SOCKS: {'.onion' in base_url}")

    handler = APIV3SessionHandler(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=".onion" in base_url,
    )

    try:
        success = await handler.login()

        if success:
            print("✅ Authentication successful!")
            print(f"   - Session authenticated: {handler.is_authenticated()}")
            return True
        else:
            print("❌ Authentication failed")
            return False
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    finally:
        await handler.close()


async def test_browse_service():
    """Test API v3 browse service"""
    print("\n" + "=" * 60)
    print("TEST 2: Browse Service")
    print("=" * 60)

    try:
        service = get_api_v3_browse_service()

        # Test 1: List items without filters
        print("\n📋 Fetching cards (no filters)...")
        params = APIV3BrowseParams()
        response = await service.list_items(params, user_id="test_user")

        if response.success:
            data = response.data or {}
            cards = data.get("data", [])
            print(f"✅ Browse successful!")
            print(f"   - Cards returned: {len(cards)}")
            print(f"   - Total count: {data.get('totalCount', 0)}")

            if cards:
                print(f"\n   First card preview:")
                first_card = cards[0]
                print(f"   - BIN: {first_card.get('bin', 'N/A')}")
                print(f"   - Country: {first_card.get('country', 'N/A')}")
                print(f"   - Scheme: {first_card.get('scheme', 'N/A')}")
                print(f"   - Price: {first_card.get('price', 'N/A')}")
        else:
            print(f"❌ Browse failed: {response.error}")
            return False

        # Test 2: List with filters
        print("\n📋 Fetching cards with filters (BIN: 555426)...")
        params = APIV3BrowseParams(bins="555426")
        response = await service.list_items(params, user_id="test_user")

        if response.success:
            data = response.data or {}
            cards = data.get("data", [])
            print(f"✅ Filtered browse successful!")
            print(f"   - Cards returned: {len(cards)}")
        else:
            print(f"❌ Filtered browse failed: {response.error}")
            return False

        # Test 3: Get filters
        print("\n🔍 Fetching available filters...")
        response = await service.get_filters(user_id="test_user")

        if response.success:
            filters = response.data.get("filters", [])
            print(f"✅ Filters retrieved!")
            print(f"   - Filter count: {len(filters)}")
            if filters:
                print(
                    f"   - Available: {', '.join(f.get('name', '') for f in filters[:5])}"
                )
        else:
            print(f"⚠️  Filters failed: {response.error}")

        return True

    except Exception as e:
        print(f"❌ Browse service error: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_prerequisites():
    """Check prerequisites"""
    print("\n" + "=" * 60)
    print("PREREQUISITES CHECK")
    print("=" * 60)

    # Check .env
    base_url = os.getenv("API_V3_BASE_URL", "").strip().strip('"')
    username = os.getenv("API_V3_USERNAME", "").strip().strip('"')
    password = os.getenv("API_V3_PASSWORD", "").strip().strip('"')
    use_socks = os.getenv("USE_SOCKS_PROXY", "").lower() in ("true", "1")
    socks_url = os.getenv("SOCKS_URL", "")

    print(f"✅ API_V3_BASE_URL: {base_url if base_url else '❌ Missing'}")
    print(f"✅ API_V3_USERNAME: {username if username else '❌ Missing'}")
    print(f"✅ API_V3_PASSWORD: {'***' if password else '❌ Missing'}")
    print(f"✅ USE_SOCKS_PROXY: {use_socks}")
    print(f"✅ SOCKS_URL: {socks_url}")

    # Check dependencies
    try:
        import aiohttp_socks

        print("✅ aiohttp-socks installed")
    except ImportError:
        print("❌ aiohttp-socks NOT installed - run: pip install aiohttp-socks")
        return False

    try:
        import bs4

        print("✅ beautifulsoup4 installed")
    except ImportError:
        print("❌ beautifulsoup4 NOT installed - run: pip install beautifulsoup4")
        return False

    # Check Tor (if needed)
    if ".onion" in base_url:
        print("\n⚠️  .onion domain detected - Tor required!")
        print("   Make sure Tor Browser or Tor service is running")
        print("   Default ports: 9150 (Tor Browser) or 9050 (Tor service)")

        # Try to check if port is open
        import socket

        port = 9150
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(("127.0.0.1", port))
            sock.close()
            if result == 0:
                print(f"   ✅ Port {port} is open (Tor likely running)")
            else:
                print(f"   ⚠️  Port {port} is closed - start Tor Browser")
        except Exception:
            print(f"   ⚠️  Could not check port {port}")

    return bool(base_url and username and password)


async def main():
    """Run all tests"""
    print("\n" + "=" * 60)
    print("API v3 Authentication Fix - Test Suite")
    print("=" * 60)

    # Check prerequisites
    if not await test_prerequisites():
        print("\n❌ Prerequisites not met. Fix issues and try again.")
        return False

    # Test authentication
    auth_success = await test_authentication()
    if not auth_success:
        print("\n❌ Authentication failed. Check credentials and Tor connection.")
        return False

    # Test browse service
    browse_success = await test_browse_service()

    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Authentication: {'✅ PASS' if auth_success else '❌ FAIL'}")
    print(f"Browse Service: {'✅ PASS' if browse_success else '❌ FAIL'}")
    print("=" * 60)

    if auth_success and browse_success:
        print("\n🎉 All tests passed! API v3 is working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Check logs above for details.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
