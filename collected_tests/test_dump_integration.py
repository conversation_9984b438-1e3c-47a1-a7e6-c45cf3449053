"""
Simple integration test for dump APIs
Tests the complete integration including imports and basic functionality
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_integration():
    """Test complete dump integration"""
    print("🧪 Testing Dump APIs Integration")
    print("=" * 50)

    try:
        # Test service import
        print("1. Testing service import...")
        from services.dump_service import get_dump_service

        dump_service = get_dump_service()
        print("✅ Dump service imported and initialized")

        # Test handlers import
        print("2. Testing handlers import...")
        from handlers.dump_handlers import get_dump_router

        dump_router = get_dump_router()
        print("✅ Dump handlers imported and router created")

        # Test product model updates
        print("3. Testing product model...")
        from models.product import get_all_products, ProductType

        products = get_all_products()
        dump_product = None
        for product in products:
            if product.type == ProductType.DUMP:
                dump_product = product
                break

        if dump_product:
            print(f"✅ Dump product found: {dump_product.name}")
            print(f"   - APIs: {len(dump_product.apis)}")
            active_apis = dump_product.get_active_apis()
            print(f"   - Active APIs: {len(active_apis)}")
            for api in active_apis:
                print(f"     * {api.name} ({api.status.value})")
        else:
            print("❌ Dump product not found")

        # Test main menu integration
        print("4. Testing main menu integration...")
        from utils.keyboards import enhanced_main_menu_keyboard

        keyboard = enhanced_main_menu_keyboard()

        # Look for dump button
        dump_button_found = False
        for row in keyboard.inline_keyboard:
            for button in row:
                if "Dumps" in button.text:
                    dump_button_found = True
                    print(
                        f"✅ Dump button found: '{button.text}' -> '{button.callback_data}'"
                    )
                    break

        if not dump_button_found:
            print("❌ Dump button not found in main menu")

        # Close service
        await dump_service.close()

        print("\n" + "=" * 50)
        print("✅ All integration tests passed!")
        return True

    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_integration())
    sys.exit(0 if success else 1)
