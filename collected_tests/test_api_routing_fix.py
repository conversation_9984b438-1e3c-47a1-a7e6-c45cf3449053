#!/usr/bin/env python3
"""
Test API Routing Fix

This script tests the API routing fix to ensure API v3 requests are properly
handled and don't fall back to API v2 due to import errors.
"""

import sys
import asyncio
from pathlib import Path

# Add the bot root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

async def test_external_api_service_initialization():
    """Test that ExternalAPIService initializes correctly with proper routing"""
    print("🔧 Testing ExternalAPIService Initialization")
    print("=" * 50)
    
    try:
        from services.external_api_service import ExternalAPIService
        
        # Create service instance
        service = ExternalAPIService()
        
        print(f"✅ ExternalAPIService created successfully")
        
        # Check the routing configuration
        print(f"📋 _use_api_v3: {service._use_api_v3}")
        
        # Check if API v3 is available
        from services.external_api_service import API_V3_AVAILABLE
        print(f"📋 API_V3_AVAILABLE: {API_V3_AVAILABLE}")
        
        # Test client creation
        client = service._ensure_api_v3_client()
        if client is not None:
            print(f"✅ API v3 client created successfully")
            print(f"📋 Client type: {type(client)}")
        else:
            print(f"⚠️  API v3 client is None (expected if dependencies missing)")
        
        return True, service
        
    except Exception as e:
        print(f"❌ ExternalAPIService initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None


async def test_list_items_routing():
    """Test that list_items routes correctly"""
    print("\n📋 Testing list_items Routing")
    print("=" * 50)
    
    try:
        from services.external_api_service import ExternalAPIService
        
        service = ExternalAPIService()
        
        print(f"🔍 Testing list_items call...")
        print(f"📋 Will route to API v3: {service._use_api_v3}")
        
        # This should show routing logs
        try:
            response = await service.list_items()
            print(f"✅ list_items call completed")
            print(f"📊 Response success: {response.success}")
            if not response.success:
                print(f"📋 Error: {response.error}")
            else:
                print(f"📋 Data keys: {list(response.data.keys()) if response.data else 'None'}")
        except Exception as e:
            print(f"⚠️  list_items call failed (expected if no dependencies): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ list_items routing test failed: {e}")
        return False


async def test_add_to_cart_routing():
    """Test that add_to_cart routes correctly"""
    print("\n🛒 Testing add_to_cart Routing")
    print("=" * 50)
    
    try:
        from services.external_api_service import ExternalAPIService
        
        service = ExternalAPIService()
        
        print(f"🔍 Testing add_to_cart call...")
        print(f"📋 Will route to API v3: {service._use_api_v3}")
        
        # This should show routing logs
        try:
            response = await service.add_to_cart("test_item_123")
            print(f"✅ add_to_cart call completed")
            print(f"📊 Response success: {response.success}")
            if not response.success:
                print(f"📋 Error: {response.error}")
        except Exception as e:
            print(f"⚠️  add_to_cart call failed (expected if no dependencies): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ add_to_cart routing test failed: {e}")
        return False


async def test_api_v3_connection_test():
    """Test the API v3 connection test method"""
    print("\n🔗 Testing API v3 Connection Test")
    print("=" * 50)
    
    try:
        from services.external_api_service import ExternalAPIService
        
        service = ExternalAPIService()
        
        print(f"🔍 Testing test_api_v3_connection...")
        
        try:
            response = await service.test_api_v3_connection()
            print(f"✅ test_api_v3_connection call completed")
            print(f"📊 Response success: {response.success}")
            if not response.success:
                print(f"📋 Error: {response.error}")
            else:
                print(f"📋 Connection info: {response.data.get('connection_info', {})}")
        except Exception as e:
            print(f"⚠️  test_api_v3_connection failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ API v3 connection test failed: {e}")
        return False


def test_import_handling():
    """Test that import handling works correctly"""
    print("\n📦 Testing Import Handling")
    print("=" * 50)
    
    try:
        # Test the conditional imports
        from services.external_api_service import API_V3_AVAILABLE, APIV3Client, APIV3Config
        
        print(f"📋 API_V3_AVAILABLE: {API_V3_AVAILABLE}")
        print(f"📋 APIV3Client: {APIV3Client}")
        print(f"📋 APIV3Config: {APIV3Config}")
        
        if API_V3_AVAILABLE:
            print(f"✅ API v3 imports successful")
        else:
            print(f"⚠️  API v3 imports failed (dependencies missing)")
        
        return True
        
    except Exception as e:
        print(f"❌ Import handling test failed: {e}")
        return False


async def test_configuration_loading():
    """Test configuration loading"""
    print("\n⚙️  Testing Configuration Loading")
    print("=" * 50)
    
    try:
        from config.settings import get_settings
        
        settings = get_settings()
        
        # Check API version
        api_version = getattr(settings, 'EXTERNAL_API_VERSION', 'NOT_SET')
        print(f"📋 EXTERNAL_API_VERSION: '{api_version}'")
        
        # Check API v3 settings
        v3_settings = {
            'EXTERNAL_V3_BASE_URL': getattr(settings, 'EXTERNAL_V3_BASE_URL', 'NOT_SET'),
            'EXTERNAL_V3_USERNAME': getattr(settings, 'EXTERNAL_V3_USERNAME', 'NOT_SET'),
            'EXTERNAL_V3_PASSWORD': '***' if getattr(settings, 'EXTERNAL_V3_PASSWORD', '') else 'NOT_SET',
        }
        
        print(f"📋 API v3 Configuration:")
        for key, value in v3_settings.items():
            status = "✅" if value != 'NOT_SET' else "❌"
            print(f"   {status} {key}: {value}")
        
        # Check if configuration is complete
        config_complete = all(v != 'NOT_SET' for v in v3_settings.values())
        if config_complete:
            print(f"✅ API v3 configuration is complete")
        else:
            print(f"⚠️  API v3 configuration is incomplete")
        
        return config_complete
        
    except Exception as e:
        print(f"❌ Configuration loading test failed: {e}")
        return False


async def main():
    """Run all routing tests"""
    print("🧪 API Routing Fix Tests")
    print("=" * 70)
    
    results = {}
    
    # Test 1: Import handling
    results['imports'] = test_import_handling()
    
    # Test 2: Configuration loading
    results['config'] = await test_configuration_loading()
    
    # Test 3: Service initialization
    init_success, service = await test_external_api_service_initialization()
    results['init'] = init_success
    
    # Test 4: list_items routing
    results['list_items'] = await test_list_items_routing()
    
    # Test 5: add_to_cart routing
    results['add_to_cart'] = await test_add_to_cart_routing()
    
    # Test 6: API v3 connection test
    results['connection_test'] = await test_api_v3_connection_test()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}: {result}")
    
    # Overall assessment
    critical_tests = ['imports', 'config', 'init']
    critical_passed = all(results.get(test, False) for test in critical_tests)
    
    if critical_passed:
        print(f"\n🎉 Critical tests passed - API routing should work!")
        
        from services.external_api_service import API_V3_AVAILABLE
        if API_V3_AVAILABLE:
            print(f"✅ API v3 is available and should be used")
        else:
            print(f"⚠️  API v3 dependencies missing - will fall back to API v2")
            print(f"💡 Install dependencies: pip install beautifulsoup4 'requests[socks]'")
    else:
        print(f"\n❌ Critical tests failed - API routing may not work correctly")
        failed_critical = [test for test in critical_tests if not results.get(test, False)]
        for test in failed_critical:
            print(f"   • {test} failed")
    
    return 0 if critical_passed else 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
