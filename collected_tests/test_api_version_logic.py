"""
Diagnostic test to check API version detection for dumps
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.dump_service import get_dump_service


async def test_api_version_logic():
    """Test API version logic to diagnose the issue"""
    print("🔍 Testing API Version Logic...")

    try:
        dump_service = get_dump_service()

        # Test both scenarios explicitly
        test_scenarios = [
            {"current_api": "v1", "expected_method": "list_dumps"},
            {"current_api": "v2", "expected_method": "list_vdumps"},
        ]

        for scenario in test_scenarios:
            current_api = scenario["current_api"]
            expected_method = scenario["expected_method"]

            print(f"\n📋 Testing scenario: current_api = '{current_api}'")
            print(f"Expected method: {expected_method}")

            # Simulate the exact logic from catalog handlers
            if current_api == "v1":
                print("✅ Condition matched: current_api == 'v1'")
                print("🔄 Calling dump_service.list_dumps()...")
                cards_data = await dump_service.list_dumps(page=1, limit=3)
                actual_method = "list_dumps"
            else:  # v2
                print("✅ Condition matched: current_api != 'v1' (treating as v2)")
                print("🔄 Calling dump_service.list_vdumps()...")
                cards_data = await dump_service.list_vdumps(page=1, limit=3)
                actual_method = "list_vdumps"

            print(f"Actual method called: {actual_method}")
            print(
                f"Method match: {'✅ CORRECT' if actual_method == expected_method else '❌ WRONG'}"
            )
            print(f"API success: {'✅ YES' if cards_data.get('success') else '❌ NO'}")

            if cards_data.get("success"):
                data = cards_data.get("data", [])
                print(f"Items returned: {len(data)}")
                if data:
                    sample = data[0]
                    print(
                        f"Sample item: BIN={sample.get('bin', 'N/A')}, Country={sample.get('country', 'N/A')}"
                    )
            else:
                print(f"Error: {cards_data.get('error', 'Unknown error')}")

        # Test edge cases
        print(f"\n🧪 Testing edge cases...")
        edge_cases = ["v1", "v2", "V1", "V2", "", None, "invalid"]

        for case in edge_cases:
            print(f"\nTesting current_api = {repr(case)}")
            if case == "v1":
                print("→ Would call list_dumps() ✅")
            else:
                print("→ Would call list_vdumps() (fallback)")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = asyncio.run(test_api_version_logic())
    print(f"\n{'✅ SUCCESS' if result else '❌ FAILED'}: API version logic test")
    sys.exit(0 if result else 1)
