"""
Test script for dump APIs implementation
Tests both dumps v1 and vdumps v2 functionality
"""

import asyncio
import logging
from services.dump_service import get_dump_service

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_dump_apis():
    """Test dump APIs functionality"""
    dump_service = get_dump_service()

    logger.info("Testing Dump APIs...")

    try:
        # Test Dumps v1 List
        logger.info("Testing dumps v1 list...")
        dumps_v1_response = await dump_service.list_dumps(page=1, limit=5)
        logger.info(f"Dumps v1 response: {dumps_v1_response.get('success', False)}")
        if dumps_v1_response.get("success"):
            logger.info(
                f"Found {len(dumps_v1_response.get('data', []))} dumps v1 items"
            )

        # Test VDumps v2 List
        logger.info("Testing vdumps v2 list...")
        vdumps_v2_response = await dump_service.list_vdumps(page=1, limit=5)
        logger.info(f"VDumps v2 response: {vdumps_v2_response.get('success', False)}")
        if vdumps_v2_response.get("success"):
            logger.info(
                f"Found {len(vdumps_v2_response.get('data', []))} vdumps v2 items"
            )

        # Test Cart operations
        logger.info("Testing cart operations...")
        cart_response = await dump_service.get_cart()
        logger.info(f"Cart response: {cart_response.get('success', False)}")
        if cart_response.get("success"):
            logger.info(f"Cart has {len(cart_response.get('data', []))} items")

        # Test Orders (dumps v1)
        logger.info("Testing dump orders...")
        orders_response = await dump_service.get_dump_orders(page=1, limit=5)
        logger.info(f"Orders response: {orders_response.get('success', False)}")
        if orders_response.get("success"):
            logger.info(f"Found {len(orders_response.get('data', []))} dump orders")

        logger.info("✅ All dump API tests completed successfully!")

    except Exception as e:
        logger.error(f"❌ Error during dump API testing: {e}", exc_info=True)

    finally:
        await dump_service.close()


async def test_specific_dump_operations():
    """Test specific dump operations that might be available"""
    dump_service = get_dump_service()

    try:
        # Test with specific filters
        logger.info("Testing with specific filters...")
        filtered_response = await dump_service.list_dumps(
            page=1, limit=3, country="US", brand="VISA", price_from=0, price_to=50
        )

        if filtered_response.get("success"):
            dumps = filtered_response.get("data", [])
            logger.info(f"Found {len(dumps)} filtered dumps")

            if dumps:
                # Show example dump data
                example_dump = dumps[0]
                logger.info(
                    f"Example dump: BIN={example_dump.get('bin')}, "
                    f"Brand={example_dump.get('brand')}, "
                    f"Price=${example_dump.get('price')}, "
                    f"Country={example_dump.get('country')}"
                )

        # Test VDumps with filters
        logger.info("Testing vdumps with filters...")
        vdumps_filtered = await dump_service.list_vdumps(
            page=1, limit=3, country="US", price_from=0, price_to=20
        )

        if vdumps_filtered.get("success"):
            vdumps = vdumps_filtered.get("data", [])
            logger.info(f"Found {len(vdumps)} filtered vdumps")

            if vdumps:
                example_vdump = vdumps[0]
                logger.info(
                    f"Example vdump: BIN={example_vdump.get('bin')}, "
                    f"Bank={example_vdump.get('bank')}, "
                    f"Price=${example_vdump.get('price')}"
                )

    except Exception as e:
        logger.error(
            f"❌ Error during specific dump operations test: {e}", exc_info=True
        )

    finally:
        await dump_service.close()


if __name__ == "__main__":
    print("🧪 Starting Dump APIs Tests...")
    print("=" * 50)

    # Run basic tests
    asyncio.run(test_dump_apis())

    print("\n" + "=" * 50)
    print("🔍 Running specific operations tests...")

    # Run specific tests
    asyncio.run(test_specific_dump_operations())

    print("\n✅ All tests completed!")
