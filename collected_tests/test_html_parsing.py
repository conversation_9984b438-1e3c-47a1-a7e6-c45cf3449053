"""Test HTML parsing for API v3 shop endpoint"""

import asyncio
import logging
from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams

logging.basicConfig(level=logging.INFO)


async def test_list():
    """Test list_items with BIN filter"""
    service = APIV3BrowseService()

    # Test with specific BIN
    params = APIV3BrowseParams(bins="405621")

    print("Testing list_items with BIN filter...")
    result = await service.list_items(params, user_id="test_user")

    print(f"\n{'='*60}")
    print(f"Success: {result.success}")
    if result.data:
        data = result.data
        print(f"Total cards: {data.get('totalCount', 0)}")
        print(f"Headers: {data.get('headers', [])}")
        print(
            f"First row sample: {data.get('data', [])[0] if data.get('data') else 'No data'}"
        )
    else:
        print(f"Error: {result.error}")
    print(f"{'='*60}\n")

    await service.close()


if __name__ == "__main__":
    asyncio.run(test_list())
