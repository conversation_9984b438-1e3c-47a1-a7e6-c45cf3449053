#!/usr/bin/env python3
"""
Test Card Service Routing

This script tests that the CardService correctly routes to API v3 when configured.
"""

import sys
import asyncio
import logging
import io
from pathlib import Path

# Add the bot root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Set up logging to capture routing messages
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def test_card_service_initialization():
    """Test that CardService initializes with correct API version detection"""
    print("🔧 Testing CardService Initialization")
    print("=" * 60)
    
    try:
        from services.card_service import CardService
        from config.settings import get_settings
        
        settings = get_settings()
        api_version = getattr(settings, 'EXTERNAL_API_VERSION', '')
        print(f"📋 Current EXTERNAL_API_VERSION: '{api_version}'")
        
        # Capture logs
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.INFO)
        
        card_logger = logging.getLogger('services.card_service')
        card_logger.addHandler(handler)
        card_logger.setLevel(logging.INFO)
        
        try:
            # Test auto-detection (default behavior)
            card_service = CardService()
            
            # Get captured logs
            log_output = log_capture.getvalue()
            
            print(f"✅ CardService created successfully")
            print(f"📋 card_service.use_api_v2: {card_service.use_api_v2}")
            
            # Check expected behavior based on API version
            expected_use_api_v2 = not (api_version.lower().startswith("v3") or api_version.lower() == "base3")
            
            if card_service.use_api_v2 == expected_use_api_v2:
                print(f"✅ API version detection correct: use_api_v2={card_service.use_api_v2} (expected {expected_use_api_v2})")
            else:
                print(f"❌ API version detection incorrect: use_api_v2={card_service.use_api_v2} (expected {expected_use_api_v2})")
                return False
            
            # Check which service is being used
            if card_service.use_api_v2:
                if card_service.api_v2_service is not None:
                    print(f"✅ Using API v2 service: {type(card_service.api_v2_service)}")
                else:
                    print(f"❌ Expected API v2 service but got None")
                    return False
            else:
                if card_service.external_api is not None:
                    print(f"✅ Using main ExternalAPIService: {type(card_service.external_api)}")
                else:
                    print(f"❌ Expected ExternalAPIService but got None")
                    return False
            
            print(f"📋 Captured logs:")
            for line in log_output.strip().split('\n'):
                if line.strip():
                    print(f"   {line}")
            
            return True
            
        finally:
            card_logger.removeHandler(handler)
        
    except Exception as e:
        print(f"❌ CardService initialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_card_service_fetch_cards():
    """Test that CardService.fetch_cards routes correctly"""
    print("\n📋 Testing CardService.fetch_cards Routing")
    print("=" * 60)
    
    try:
        from services.card_service import CardService
        
        # Create card service with auto-detection
        card_service = CardService()
        
        print(f"📋 CardService will use API v2: {card_service.use_api_v2}")
        
        # Capture logs from multiple loggers
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.INFO)
        
        # Add handler to relevant loggers
        loggers_to_monitor = [
            'services.card_service',
            'services.external_api_service',
            'api_v2.services.browse_service.APIV2BrowseService'
        ]
        
        for logger_name in loggers_to_monitor:
            logger_obj = logging.getLogger(logger_name)
            logger_obj.addHandler(handler)
            logger_obj.setLevel(logging.INFO)
        
        try:
            print("🔍 Calling fetch_cards...")
            
            # Call fetch_cards
            result = await card_service.fetch_cards(page=1, limit=5, user_id="test_user")
            
            # Get captured logs
            log_output = log_capture.getvalue()
            
            print(f"✅ fetch_cards call completed")
            print(f"📊 Result success: {result.get('success', True) if isinstance(result, dict) else 'N/A'}")
            
            # Analyze logs to determine routing
            if card_service.use_api_v2:
                if "Starting list_items request for user test_user" in log_output:
                    print("✅ Confirmed routing to API v2 service")
                    routing_correct = True
                else:
                    print("❌ Expected API v2 routing but didn't find API v2 logs")
                    routing_correct = False
            else:
                if "Routing list_items to API v3" in log_output:
                    print("✅ Confirmed routing to API v3 via ExternalAPIService")
                    routing_correct = True
                elif "Routing list_items to API v2" in log_output:
                    print("⚠️  Routed to API v2 via ExternalAPIService (fallback behavior)")
                    routing_correct = True
                else:
                    print("❌ Expected ExternalAPIService routing but didn't find routing logs")
                    routing_correct = False
            
            print(f"📋 Captured logs:")
            for line in log_output.strip().split('\n'):
                if line.strip():
                    print(f"   {line}")
            
            # Check result structure
            if isinstance(result, dict):
                if 'data' in result:
                    data_count = len(result.get('data', []))
                    print(f"📊 Returned {data_count} items")
                else:
                    print(f"📊 Result keys: {list(result.keys())}")
            
            return routing_correct
            
        finally:
            for logger_name in loggers_to_monitor:
                logger_obj = logging.getLogger(logger_name)
                logger_obj.removeHandler(handler)
        
    except Exception as e:
        print(f"❌ fetch_cards routing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_version_scenarios():
    """Test different API version scenarios"""
    print("\n🔀 Testing Different API Version Scenarios")
    print("=" * 60)
    
    try:
        from services.card_service import CardService
        
        # Test scenarios: (api_version, expected_use_api_v2)
        scenarios = [
            ('v2', True),
            ('base2', True),
            ('v3', False),
            ('base3', False),
            ('V3', False),
            ('', True),  # Default to API v2
        ]
        
        print("📋 Testing version detection scenarios:")
        all_correct = True
        
        for api_version, expected_use_api_v2 in scenarios:
            # Mock the settings temporarily
            import unittest.mock
            
            with unittest.mock.patch('services.card_service.get_settings') as mock_settings:
                mock_settings.return_value.EXTERNAL_API_VERSION = api_version
                
                card_service = CardService()
                actual_use_api_v2 = card_service.use_api_v2
                
                status = "✅" if actual_use_api_v2 == expected_use_api_v2 else "❌"
                print(f"   {status} '{api_version}' -> use_api_v2={actual_use_api_v2} (expected {expected_use_api_v2})")
                
                if actual_use_api_v2 != expected_use_api_v2:
                    all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Version scenarios test failed: {e}")
        return False


async def test_explicit_override():
    """Test explicit API version override"""
    print("\n🎯 Testing Explicit API Version Override")
    print("=" * 60)
    
    try:
        from services.card_service import CardService
        
        # Test explicit override
        card_service_v2 = CardService(use_api_v2=True)
        card_service_v3 = CardService(use_api_v2=False)
        
        print(f"📋 Explicit use_api_v2=True: {card_service_v2.use_api_v2}")
        print(f"📋 Explicit use_api_v2=False: {card_service_v3.use_api_v2}")
        
        if card_service_v2.use_api_v2 and not card_service_v3.use_api_v2:
            print("✅ Explicit override working correctly")
            return True
        else:
            print("❌ Explicit override not working")
            return False
        
    except Exception as e:
        print(f"❌ Explicit override test failed: {e}")
        return False


async def main():
    """Run all CardService routing tests"""
    print("🧪 CardService Routing Tests")
    print("=" * 80)
    
    results = {}
    
    # Test 1: Service initialization
    results['initialization'] = await test_card_service_initialization()
    
    # Test 2: fetch_cards routing
    results['fetch_cards_routing'] = await test_card_service_fetch_cards()
    
    # Test 3: Version scenarios
    results['version_scenarios'] = await test_version_scenarios()
    
    # Test 4: Explicit override
    results['explicit_override'] = await test_explicit_override()
    
    # Summary
    print("\n📊 CardService Test Results")
    print("=" * 80)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}: {result}")
    
    # Overall assessment
    all_passed = all(results.values())
    
    if all_passed:
        print(f"\n🎉 ALL CARDSERVICE TESTS PASSED!")
        print(f"✅ CardService correctly detects API version from configuration")
        print(f"✅ CardService routes to appropriate service based on version")
        print(f"✅ Version detection logic handles all scenarios correctly")
        print(f"✅ Explicit override functionality works")
        
        print(f"\n🚀 Expected Behavior:")
        print(f"   • When EXTERNAL_API_VERSION=v3: CardService uses ExternalAPIService (API v3 routing)")
        print(f"   • When EXTERNAL_API_VERSION=v2: CardService uses APIV2BrowseService")
        print(f"   • CardService logs show which service is being used")
        
    else:
        print(f"\n❌ SOME CARDSERVICE TESTS FAILED")
        failed_tests = [test for test, result in results.items() if not result]
        for test in failed_tests:
            print(f"   • {test}")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
