"""
Simple test for dump service integration without database dependencies
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.dump_service import get_dump_service
from models.product import ProductType


async def test_dump_integration_logic():
    """Test the dump integration logic used in catalog handlers"""
    print("🧪 Testing Dump Integration Logic...")

    try:
        # Simulate the catalog handler logic
        print("\n📋 Simulating Catalog Handler Logic...")

        # Test scenario 1: User has selected dumps with v1 API
        current_product = ProductType.DUMP
        current_api = "v1"
        page = 1

        print(f"Current product: {current_product}")
        print(f"Current API: {current_api}")
        print(f"Page: {page}")

        # Use appropriate service based on product type (catalog handler logic)
        if current_product == ProductType.DUMP:
            print("✅ Detected dump product - using dump service")

            # Use dump service for dump products
            dump_service = get_dump_service()
            print("✅ Got dump service instance")

            # Fetch dumps data using dump service (exact logic from catalog handlers)
            if current_api == "v1":
                print("📋 Calling list_dumps for v1...")
                cards_data = await dump_service.list_dumps(page=page, limit=5)
            else:  # v2
                print("📦 Calling list_vdumps for v2...")
                cards_data = await dump_service.list_vdumps(page=page, limit=5)

            print(f"✅ API call successful: {cards_data.get('success', False)}")

            if cards_data.get("success"):
                data = cards_data.get("data", [])
                print(f"✅ Retrieved {len(data)} items")
                if data:
                    sample = data[0]
                    print(
                        f"✅ Sample item: BIN={sample.get('bin', 'N/A')}, Country={sample.get('country', 'N/A')}"
                    )
            else:
                print(f"❌ API call failed: {cards_data.get('error', 'Unknown error')}")

        # Test scenario 2: User has selected dumps with v2 API
        print(f"\n📦 Testing v2 API scenario...")
        current_api = "v2"

        if current_product == ProductType.DUMP:
            if current_api == "v1":
                cards_data = await dump_service.list_dumps(page=page, limit=5)
            else:  # v2
                cards_data = await dump_service.list_vdumps(page=page, limit=5)

            print(f"✅ v2 API call successful: {cards_data.get('success', False)}")

            if cards_data.get("success"):
                data = cards_data.get("data", [])
                print(f"✅ Retrieved {len(data)} vdump items")
                if data:
                    sample = data[0]
                    print(
                        f"✅ Sample vdump: BIN={sample.get('bin', 'N/A')}, Country={sample.get('country', 'N/A')}"
                    )

        # Test error handling logic
        print(f"\n🛠️ Testing Error Handling Logic...")
        if not cards_data.get("success", False):
            if current_product == ProductType.DUMP:
                api_status = "Dump API is currently unavailable"
            else:
                api_status = "Other service error"
            print(f"✅ Error handling: {api_status}")
        else:
            print("✅ No errors - all services working correctly")

        print(f"\n🎉 All integration logic tests passed!")
        print(f"✅ Dump service integration working correctly")
        print(f"✅ API version selection working correctly")
        print(f"✅ Error handling working correctly")

        return True

    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = asyncio.run(test_dump_integration_logic())
    print(f"\n{'✅ SUCCESS' if result else '❌ FAILED'}: Dump integration logic test")
    sys.exit(0 if result else 1)
