"""
Quick test to verify custom APIV3HTTPClient is being used
"""

import asyncio
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%H:%M:%S",
)

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_v3 import get_api_v3_browse_service
from api_v3.http.client import APIV3HTTPClient
from dotenv import load_dotenv

# Load environment
load_dotenv()


async def test_custom_client():
    """Test that service uses APIV3HTTPClient"""
    print("\n" + "=" * 60)
    print("Testing Custom Client Usage")
    print("=" * 60)

    try:
        # Get service
        service = get_api_v3_browse_service()
        print(f"\n✅ Service created: {type(service).__name__}")

        # Get client
        client = service._get_client()
        print(f"✅ Client retrieved: {type(client).__name__}")

        # Check if it's our custom client
        if isinstance(client, APIV3HTTPClient):
            print("✅ Client is APIV3HTTPClient (CORRECT!)")
            print(f"   - Has session handler: {hasattr(client, 'session_handler')}")
            if hasattr(client, "session_handler"):
                print(
                    f"   - Session handler type: {type(client.session_handler).__name__}"
                )
                print(f"   - Base URL: {client.session_handler.base_url}")
                print(f"   - Using SOCKS: {client.session_handler.use_socks_proxy}")
            return True
        else:
            print(f"❌ Client is {type(client).__name__} (WRONG!)")
            print(f"   Expected: APIV3HTTPClient")
            print(f"   Got: {type(client).__module__}.{type(client).__name__}")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    success = await test_custom_client()

    print("\n" + "=" * 60)
    if success:
        print("✅ PASS: Custom client is being used correctly!")
    else:
        print("❌ FAIL: Default client is being used instead!")
    print("=" * 60 + "\n")

    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
