"""
Test dump service functionality
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.dump_service import get_dump_service


async def test_dump_service():
    """Test dump service functionality"""
    print("🧪 Testing Dump Service...")

    try:
        # Get dump service instance
        dump_service = get_dump_service()
        print("✅ Dump service instance created successfully")

        # Test dumps v1
        print("\n📋 Testing Dumps v1...")
        dumps_v1_result = await dump_service.list_dumps(page=1, limit=3)
        print(f"Dumps v1 success: {dumps_v1_result.get('success', False)}")
        if dumps_v1_result.get("success"):
            data = dumps_v1_result.get("data", [])
            print(f"Dumps v1 count: {len(data)}")
            if data:
                print(
                    f"Sample dump: {data[0].get('bin', 'N/A')} - {data[0].get('country', 'N/A')}"
                )
        else:
            print(f"Dumps v1 error: {dumps_v1_result.get('error', 'Unknown error')}")

        # Test vdumps v2
        print("\n📦 Testing VDumps v2...")
        vdumps_v2_result = await dump_service.list_vdumps(page=1, limit=3)
        print(f"VDumps v2 success: {vdumps_v2_result.get('success', False)}")
        if vdumps_v2_result.get("success"):
            data = vdumps_v2_result.get("data", [])
            print(f"VDumps v2 count: {len(data)}")
            if data:
                print(
                    f"Sample vdump: {data[0].get('bin', 'N/A')} - {data[0].get('country', 'N/A')}"
                )
        else:
            print(f"VDumps v2 error: {vdumps_v2_result.get('error', 'Unknown error')}")

        print("\n✅ Dump service test completed!")
        return True

    except Exception as e:
        print(f"❌ Dump service test failed: {e}")
        return False


if __name__ == "__main__":
    result = asyncio.run(test_dump_service())
    sys.exit(0 if result else 1)
