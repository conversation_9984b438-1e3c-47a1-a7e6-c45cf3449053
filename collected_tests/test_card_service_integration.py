#!/usr/bin/env python3
"""
Test card service integration with API v2 authentication fix
"""

import asyncio
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_card_service():
    """Test card service with API v2 authentication"""
    print("============================================================")
    print("Card Service Integration Test")
    print("============================================================")
    
    try:
        from services.card_service import CardService
        
        print("Testing card service with API v2...")
        card_service = CardService()
        
        result = await card_service.fetch_cards(page=1, limit=3, user_id='test_user')

        if result.get("success", False):
            cards = result.get("data", [])
            print(f"✅ Card service working! Found {len(cards)} cards")
            for i, card in enumerate(cards[:2], 1):  # Show first 2 cards
                bin_num = card.get("bin", "N/A")
                country = card.get("country", "N/A")
                price = card.get("price", "N/A")
                print(f"  {i}. Card: {bin_num} | {country} | ${price}")
            print("============================================================")
            print("🎉 Card service integration test PASSED!")
            return True
        else:
            error = result.get("error", "Unknown error")
            print(f"❌ Card service failed: {error}")
            print("============================================================")
            print("💥 Card service integration test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Card service error: {e}")
        import traceback
        traceback.print_exc()
        print("============================================================")
        print("💥 Card service integration test FAILED!")
        return False

if __name__ == "__main__":
    asyncio.run(test_card_service())
