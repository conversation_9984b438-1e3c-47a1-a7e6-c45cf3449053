"""
Test the simple session handler using requests library
"""

import asyncio
import logging
from api_v3.auth.session_handler import APIV3SessionHandler
from dotenv import load_dotenv
import os

logging.basicConfig(level=logging.INFO)
load_dotenv()


async def test_simple():
    base_url = os.getenv("API_V3_BASE_URL", "").strip().strip('"')
    username = os.getenv("API_V3_USERNAME", "").strip().strip('"')
    password = os.getenv("API_V3_PASSWORD", "").strip().strip('"')

    print(f"\nTesting simple session handler (using requests library)...")
    print(f"Base URL: {base_url}")

    handler = APIV3SessionHandler(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=True,
    )

    try:
        success = await handler.login()
        if success:
            print("✅ Login successful with simple handler!")

            # Try to get the session and make a request
            session = await handler.get_session()
            print(f"Session cookies: {len(list(session.cookies))}")

        else:
            print("❌ Login failed")
        await handler.close()
        return success
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_simple())
    exit(0 if success else 1)
