#!/usr/bin/env python3
"""
Comprehensive API Issues Analysis Script

This script tests all three API versions to identify specific runtime issues,
authentication problems, data inconsistencies, and performance bottlenecks.
"""

import asyncio
import logging
import os
import sys
import time
import traceback
from pathlib import Path
from typing import Dict, Any, List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%H:%M:%S",
)

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

# Load environment
load_dotenv()

logger = logging.getLogger(__name__)


class APIIssueAnalyzer:
    """Comprehensive API issue analyzer"""

    def __init__(self):
        self.results = {
            "api_v1": {"status": "unknown", "issues": [], "tests": {}},
            "api_v2": {"status": "unknown", "issues": [], "tests": {}},
            "api_v3": {"status": "unknown", "issues": [], "tests": {}},
            "general": {"issues": [], "recommendations": []},
        }

    async def analyze_all_apis(self) -> Dict[str, Any]:
        """Run comprehensive analysis of all API versions"""

        print("\n" + "=" * 80)
        print("🔍 COMPREHENSIVE API ISSUES ANALYSIS")
        print("=" * 80)

        # Test import capabilities
        await self._test_imports()

        # Test API v1
        await self._analyze_api_v1()

        # Test API v2
        await self._analyze_api_v2()

        # Test API v3
        await self._analyze_api_v3()

        # Test card service integration
        await self._analyze_card_service()

        # Test external API service
        await self._analyze_external_api_service()

        # Generate recommendations
        self._generate_recommendations()

        # Print summary
        self._print_summary()

        return self.results

    async def _test_imports(self):
        """Test if all API modules can be imported"""
        print("\n📦 Testing Module Imports...")

        import_tests = [
            ("api_v1.services.api_config", "get_api_config_service"),
            ("api_v1.services.http_client", "get_http_client"),
            ("api_v2.services.browse_service", "get_api_v2_browse_service"),
            ("api_v3.services.browse_service", "get_api_v3_browse_service"),
            ("services.card_service", "CardService"),
            ("services.external_api_service", "ExternalAPIService"),
            ("services.api_status_service", "get_api_status_service"),
        ]

        for module_name, class_name in import_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                print(f"✅ {module_name}.{class_name}")
            except Exception as e:
                print(f"❌ {module_name}.{class_name}: {e}")
                self.results["general"]["issues"].append(
                    f"Import error: {module_name}.{class_name} - {e}"
                )

    async def _analyze_api_v1(self):
        """Analyze API v1 specific issues"""
        print("\n🔌 Analyzing API v1...")

        try:
            from api_v1.services.api_config import get_api_config_service
            from api_v1.services.http_client import get_http_client

            # Test configuration service
            config_service = get_api_config_service()
            self.results["api_v1"]["tests"]["config_service"] = "✅ Available"

            # Test HTTP client
            http_client = get_http_client()
            self.results["api_v1"]["tests"]["http_client"] = "✅ Available"

            # Check authentication setup
            # Note: This would require actual API credentials to test
            self.results["api_v1"]["tests"][
                "authentication"
            ] = "⚠️ Requires credentials to test"

            self.results["api_v1"]["status"] = "available"

        except Exception as e:
            self.results["api_v1"]["status"] = "error"
            self.results["api_v1"]["issues"].append(f"Initialization error: {e}")
            print(f"❌ API v1 Error: {e}")

    async def _analyze_api_v2(self):
        """Analyze API v2 specific issues"""
        print("\n🔌 Analyzing API v2...")

        try:
            from api_v2.services.browse_service import (
                get_api_v2_browse_service,
                APIV2BrowseParams,
            )
            from shared_api.config.registry import api_registry

            # Test browse service initialization
            service = get_api_v2_browse_service()
            self.results["api_v2"]["tests"]["browse_service"] = "✅ Available"

            # Test parameter handling
            params = APIV2BrowseParams(country="US", brand="VISA")
            query_pairs = params.to_query_pairs()
            if query_pairs:
                self.results["api_v2"]["tests"]["parameter_handling"] = "✅ Working"
            else:
                self.results["api_v2"]["tests"][
                    "parameter_handling"
                ] = "❌ No query pairs generated"

            # Test registry access
            try:
                registry_apis = api_registry.list_apis()
                self.results["api_v2"]["tests"][
                    "registry"
                ] = f"✅ {len(registry_apis)} APIs registered"
            except Exception as e:
                self.results["api_v2"]["tests"]["registry"] = f"❌ Registry error: {e}"
                self.results["api_v2"]["issues"].append(
                    f"Shared API registry issue: {e}"
                )

            self.results["api_v2"]["status"] = "available"

        except Exception as e:
            self.results["api_v2"]["status"] = "error"
            self.results["api_v2"]["issues"].append(f"Initialization error: {e}")
            print(f"❌ API v2 Error: {e}")

    async def _analyze_api_v3(self):
        """Analyze API v3 specific issues"""
        print("\n🔌 Analyzing API v3...")

        try:
            from api_v3.services.browse_service import (
                get_api_v3_browse_service,
                APIV3BrowseParams,
            )
            from api_v3.auth.session_handler import APIV3SessionHandler
            from api_v3.http.client import APIV3HTTPClient

            # Test browse service (without credentials)
            try:
                # This will fail without credentials, but we can test the structure
                service = get_api_v3_browse_service(
                    base_url="https://example.com", username="test", password="test"
                )
                self.results["api_v3"]["tests"]["browse_service"] = "✅ Available"
            except ValueError as e:
                if "not initialized" in str(e):
                    self.results["api_v3"]["tests"][
                        "browse_service"
                    ] = "⚠️ Requires configuration"
                else:
                    raise

            # Test parameter handling
            params = APIV3BrowseParams(country="US", scheme="VISA")
            query_params = params.to_query_params()
            if query_params:
                self.results["api_v3"]["tests"]["parameter_handling"] = "✅ Working"
            else:
                self.results["api_v3"]["tests"][
                    "parameter_handling"
                ] = "❌ No query params generated"

            # Test session handler structure
            try:
                handler = APIV3SessionHandler(
                    base_url="https://example.com", username="test", password="test"
                )
                if hasattr(handler, "ensure_session"):
                    self.results["api_v3"]["tests"][
                        "session_handler"
                    ] = "✅ ensure_session method available"
                else:
                    self.results["api_v3"]["tests"][
                        "session_handler"
                    ] = "❌ ensure_session method missing"
                    self.results["api_v3"]["issues"].append(
                        "SessionHandler missing ensure_session method"
                    )
            except Exception as e:
                self.results["api_v3"]["tests"][
                    "session_handler"
                ] = f"❌ SessionHandler error: {e}"
                self.results["api_v3"]["issues"].append(
                    f"SessionHandler initialization error: {e}"
                )

            # Test HTTP client
            try:
                client = APIV3HTTPClient(
                    base_url="https://example.com", username="test", password="test"
                )
                self.results["api_v3"]["tests"]["http_client"] = "✅ Available"
            except Exception as e:
                self.results["api_v3"]["tests"][
                    "http_client"
                ] = f"❌ HTTP client error: {e}"
                self.results["api_v3"]["issues"].append(
                    f"HTTP client initialization error: {e}"
                )

            self.results["api_v3"]["status"] = "available"

        except Exception as e:
            self.results["api_v3"]["status"] = "error"
            self.results["api_v3"]["issues"].append(f"Initialization error: {e}")
            print(f"❌ API v3 Error: {e}")
            traceback.print_exc()

    async def _analyze_card_service(self):
        """Analyze CardService integration issues"""
        print("\n🃏 Analyzing CardService...")

        try:
            from services.card_service import CardService

            # Test CardService initialization
            card_service = CardService()

            # Check API version detection
            if hasattr(card_service, "use_api_v2"):
                self.results["general"]["tests"] = self.results["general"].get(
                    "tests", {}
                )
                self.results["general"]["tests"][
                    "card_service_v2_flag"
                ] = f"✅ use_api_v2={card_service.use_api_v2}"

            if hasattr(card_service, "use_api_v3"):
                self.results["general"]["tests"][
                    "card_service_v3_flag"
                ] = f"✅ use_api_v3={card_service.use_api_v3}"

            # Test status service integration
            if hasattr(card_service, "status_service"):
                self.results["general"]["tests"][
                    "status_service_integration"
                ] = "✅ Status service available"
            else:
                self.results["general"]["issues"].append(
                    "CardService missing status_service integration"
                )

            # Test method availability
            methods_to_check = ["fetch_cards", "get_filter_options", "search_cards"]
            for method_name in methods_to_check:
                if hasattr(card_service, method_name):
                    self.results["general"]["tests"][
                        f"card_service_{method_name}"
                    ] = "✅ Available"
                else:
                    self.results["general"]["issues"].append(
                        f"CardService missing {method_name} method"
                    )

        except Exception as e:
            self.results["general"]["issues"].append(f"CardService error: {e}")
            print(f"❌ CardService Error: {e}")

    async def _analyze_external_api_service(self):
        """Analyze ExternalAPIService issues"""
        print("\n🌐 Analyzing ExternalAPIService...")

        try:
            from services.external_api_service import (
                ExternalAPIService,
                get_external_api_service,
            )

            # Test service initialization
            service = get_external_api_service()
            self.results["general"]["tests"] = self.results["general"].get("tests", {})
            self.results["general"]["tests"]["external_api_service"] = "✅ Available"

            # Check for required methods
            required_methods = [
                "list_items",
                "add_to_cart",
                "get_user_info",
                "health_check",
            ]
            for method_name in required_methods:
                if hasattr(service, method_name):
                    self.results["general"]["tests"][
                        f"external_api_{method_name}"
                    ] = "✅ Available"
                else:
                    self.results["general"]["issues"].append(
                        f"ExternalAPIService missing {method_name} method"
                    )

        except Exception as e:
            self.results["general"]["issues"].append(f"ExternalAPIService error: {e}")
            print(f"❌ ExternalAPIService Error: {e}")

    def _generate_recommendations(self):
        """Generate recommendations based on analysis"""
        recommendations = []

        # Check for critical issues
        if self.results["api_v3"]["status"] == "error":
            recommendations.append(
                "🔧 Fix API v3 initialization errors - check authentication and HTTP client"
            )

        if any(
            "missing ensure_session" in issue
            for issue in self.results["api_v3"]["issues"]
        ):
            recommendations.append(
                "🔧 API v3 SessionHandler needs ensure_session method implementation"
            )

        if any(
            "registry" in issue.lower()
            for issues in [self.results[api]["issues"] for api in self.results]
            for issue in issues
        ):
            recommendations.append(
                "🔧 Shared API registry has configuration issues - check initialization"
            )

        # Performance recommendations
        if self.results["api_v3"]["status"] == "available":
            recommendations.append(
                "⚡ Optimize API v3 by replacing sync requests with async HTTP client"
            )
            recommendations.append(
                "💾 Implement caching for API v3 HTML parsing results"
            )

        # Standardization recommendations
        recommendations.append(
            "📋 Standardize parameter naming across all API versions"
        )
        recommendations.append("🔄 Implement unified error handling across all APIs")
        recommendations.append("📊 Add comprehensive monitoring for all API endpoints")

        # Testing recommendations
        recommendations.append(
            "🧪 Fix pytest-asyncio configuration for proper async test execution"
        )
        recommendations.append("🔍 Add integration tests for API version switching")

        self.results["general"]["recommendations"] = recommendations

    def _print_summary(self):
        """Print comprehensive analysis summary"""
        print("\n" + "=" * 80)
        print("📋 ANALYSIS SUMMARY")
        print("=" * 80)

        # API Status Summary
        for api_name in ["api_v1", "api_v2", "api_v3"]:
            status = self.results[api_name]["status"]
            issues_count = len(self.results[api_name]["issues"])

            status_emoji = {"available": "🟢", "error": "🔴", "unknown": "🟡"}.get(
                status, "❓"
            )

            print(f"\n{status_emoji} **{api_name.upper()}**: {status.title()}")

            if self.results[api_name]["tests"]:
                print("   Tests:")
                for test_name, test_result in self.results[api_name]["tests"].items():
                    print(f"   - {test_name}: {test_result}")

            if issues_count > 0:
                print(f"   Issues ({issues_count}):")
                for issue in self.results[api_name]["issues"]:
                    print(f"   - {issue}")

        # General Issues
        if self.results["general"]["issues"]:
            print(
                f"\n🚨 **GENERAL ISSUES** ({len(self.results['general']['issues'])}):"
            )
            for issue in self.results["general"]["issues"]:
                print(f"   - {issue}")

        # General Tests
        if self.results["general"].get("tests"):
            print(f"\n🧪 **INTEGRATION TESTS**:")
            for test_name, test_result in self.results["general"]["tests"].items():
                print(f"   - {test_name}: {test_result}")

        # Recommendations
        if self.results["general"]["recommendations"]:
            print(
                f"\n💡 **RECOMMENDATIONS** ({len(self.results['general']['recommendations'])}):"
            )
            for i, rec in enumerate(self.results["general"]["recommendations"], 1):
                print(f"   {i}. {rec}")

        print("\n" + "=" * 80)
        print("🎯 ANALYSIS COMPLETE")
        print("=" * 80)


async def main():
    """Run the analysis"""
    analyzer = APIIssueAnalyzer()
    results = await analyzer.analyze_all_apis()

    # Save results to file
    import json

    results_file = Path("api_analysis_results.json")

    # Convert results to JSON-serializable format
    json_results = {}
    for key, value in results.items():
        if isinstance(value, dict):
            json_results[key] = value
        else:
            json_results[key] = str(value)

    with open(results_file, "w") as f:
        json.dump(json_results, f, indent=2)

    print(f"\n💾 Results saved to: {results_file}")

    return results


if __name__ == "__main__":
    try:
        results = asyncio.run(main())

        # Exit with error code if critical issues found
        critical_issues = sum(
            1
            for api in ["api_v1", "api_v2", "api_v3"]
            if results[api]["status"] == "error"
        )

        sys.exit(0 if critical_issues == 0 else 1)

    except KeyboardInterrupt:
        print("\n\n⚠️ Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Analysis failed: {e}")
        traceback.print_exc()
        sys.exit(1)
