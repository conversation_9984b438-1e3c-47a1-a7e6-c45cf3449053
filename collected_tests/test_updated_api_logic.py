#!/usr/bin/env python3
"""
Test updated API ID comparison logic for dumps
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import init_database
from services.dump_service import get_dump_service


async def test_updated_api_logic():
    """Test updated API ID comparison logic"""
    print("🧪 Testing Updated API ID Comparison Logic...")

    try:
        # Connect to database
        await init_database()
        print("✅ Database connected")

        dump_service = get_dump_service()
        print("✅ Dump service obtained")

        # Test both dump API IDs
        test_cases = [
            {
                "current_api": "dump_base_1",
                "expected": "list_dumps",
                "description": "DUMPS v1 API (dump_base_1)",
            },
            {
                "current_api": "DUMP_BASE_1",
                "expected": "list_dumps",
                "description": "DUMPS v1 API (uppercase)",
            },
            {
                "current_api": "dump_base_2",
                "expected": "list_vdumps",
                "description": "VDUMPS v2 API (dump_base_2)",
            },
            {
                "current_api": "DUMP_BASE_2",
                "expected": "list_vdumps",
                "description": "VDUMPS v2 API (uppercase)",
            },
            {
                "current_api": "",
                "expected": "list_vdumps",
                "description": "Empty string (fallback)",
            },
            {
                "current_api": None,
                "expected": "list_vdumps",
                "description": "None (fallback)",
            },
            {
                "current_api": "invalid_api",
                "expected": "list_vdumps",
                "description": "Invalid API ID (fallback)",
            },
        ]

        for test_case in test_cases:
            current_api = test_case["current_api"]
            expected = test_case["expected"]
            description = test_case["description"]

            print(f"\n📋 Testing: {description}")
            print(f"   Input: current_api = {repr(current_api)}")

            # Apply the updated logic from catalog handlers
            if current_api and current_api.lower() == "dump_base_1":
                method_used = "list_dumps"
                print("   ✅ Condition: current_api.lower() == 'dump_base_1' → TRUE")
                cards_data = await dump_service.list_dumps(page=1, limit=2)
            else:
                method_used = "list_vdumps"
                print(
                    "   ✅ Condition: current_api.lower() == 'dump_base_1' → FALSE (using fallback)"
                )
                cards_data = await dump_service.list_vdumps(page=1, limit=2)

            print(f"   Method called: {method_used}")
            print(f"   Expected: {expected}")

            if method_used == expected:
                print("   ✅ CORRECT: Method selection matches expectation")
            else:
                print("   ❌ WRONG: Method selection does not match expectation")
                return False

            if cards_data.get("success"):
                data = cards_data.get("data", [])
                print(f"   ✅ API call successful: {len(data)} items returned")
                if data:
                    sample = data[0]
                    print(
                        f"   Sample: BIN={sample.get('bin', 'N/A')}, Country={sample.get('country', 'N/A')}"
                    )
            else:
                print(
                    f"   ❌ API call failed: {cards_data.get('error', 'Unknown error')}"
                )

        print(f"\n🎉 All updated API ID tests passed!")
        print(f"✅ dump_base_1 → list_dumps() (DUMPS v1)")
        print(f"✅ dump_base_2 and others → list_vdumps() (VDUMPS v2)")
        print(f"✅ Case-insensitive and fallback handling works correctly")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = asyncio.run(test_updated_api_logic())
    print(
        f"\n{'✅ SUCCESS' if result else '❌ FAILED'}: Updated API ID comparison test"
    )
    sys.exit(0 if result else 1)
