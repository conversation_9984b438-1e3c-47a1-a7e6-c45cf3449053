"""
Configuration management for the Demo Wallet Bot
"""

from __future__ import annotations

import logging
from typing import List, Optional

from pydantic import Field, ValidationError, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    """Application settings with environment variable support"""

    model_config = SettingsConfigDict(
        env_file=(".env", "config.example.env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",  # Allow extra fields to prevent validation errors
    )

    # Bot & API Configuration
    BOT_TOKEN: str = Field(default="", description="Telegram Bot API token")
    DEMO_API_BASE: str = Field(
        default="https://demo.api.example", description="Demo API base URL"
    )
    DEMO_API_TOKEN: str = Field(default="", description="Demo API authentication token")

    # Database Configuration
    DATABASE_URL: str = Field(
        default="sqlite:///demo_wallet_bot.db", description="Database connection URL"
    )
    DATABASE_NAME: str = Field(default="demo_wallet_bot", description="Database name")
    USE_MONGODB: bool = Field(
        default=False, description="Use MongoDB instead of SQLite"
    )
    MONGODB_URL: str = Field(
        default="mongodb://localhost:27017", description="MongoDB connection URL"
    )
    MONGODB_AUTH_SOURCE: str = Field(
        default="admin", description="MongoDB authentication database"
    )
    MONGODB_AUTH_MECHANISM: str = Field(
        default="SCRAM-SHA-256", description="MongoDB authentication mechanism"
    )

    # Initial Wallet Settings
    INITIAL_BALANCE: float = Field(
        default=100.0, ge=0, description="Initial wallet balance for new users"
    )
    DEFAULT_CURRENCY: str = Field(default="USD", description="Default currency code")

    # Compliance & Security
    SANCTIONED_COUNTRIES: str = Field(
        default="CU,IR,KP,SY,UA-CRIMEA",
        description="Comma-separated sanctioned country codes",
    )
    AML_HOURLY_LIMIT: float = Field(
        default=200.0, ge=0, description="AML hourly transaction limit"
    )
    DAILY_SPEND_CAP: float = Field(
        default=500.0, ge=0, description="Daily spending cap"
    )
    MONTHLY_SPEND_CAP: float = Field(
        default=2000.0, ge=0, description="Monthly spending cap"
    )

    # Rate Limiting
    PURCHASES_PER_MINUTE: int = Field(
        default=3, ge=1, description="Purchase rate limit per minute"
    )
    PURCHASES_PER_DAY: int = Field(
        default=50, ge=1, description="Purchase rate limit per day"
    )
    SEARCHES_PER_MINUTE: int = Field(
        default=10, ge=1, description="Search rate limit per minute"
    )
    MESSAGES_PER_MINUTE: int = Field(
        default=10, ge=1, description="Generic message rate limit per minute"
    )
    CALLBACKS_PER_MINUTE: int = Field(
        default=20, ge=1, description="Generic callback rate limit per minute"
    )

    # Feature Flags
    FALLBACK_ENABLED: bool = Field(
        default=True, description="Enable fallback card generation"
    )
    METRICS_ENABLED: bool = Field(default=True, description="Enable Prometheus metrics")
    RETENTION_ENABLED: bool = Field(
        default=True, description="Enable data retention cleanup"
    )

    # Observability
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    ENVIRONMENT: str = Field(
        default="development",
        description="App environment: development|production|test",
    )
    LOG_TO_FILE: bool = Field(default=False, description="Enable file logging")
    LOG_FILE_PATH: str = Field(default="logs/bot.log", description="Log file path")
    LOG_MAX_SIZE: int = Field(
        default=10 * 1024 * 1024, ge=1024, description="Max log file size in bytes"
    )
    LOG_BACKUP_COUNT: int = Field(
        default=5, ge=1, description="Number of rotated log files to keep"
    )
    LOG_COLOR: bool = Field(default=True, description="Colorize console logs")
    LOG_STRUCTURED: bool = Field(
        default=True, description="Use structured log formatter"
    )
    LOG_SHOW_CATEGORY: bool = Field(
        default=False, description="Show category tags like <SYSTEM>/<USER>"
    )
    METRICS_PORT: int = Field(
        default=8000, ge=1024, le=65535, description="Prometheus metrics server port"
    )

    # Data Retention
    RETENTION_DAYS: int = Field(
        default=45, ge=1, description="Data retention period in days"
    )

    # Admin Configuration
    ADMIN_USER_IDS: str = Field(
        default="", description="Comma-separated Telegram user IDs for admin access"
    )
    # Centralized API Config encryption key (Fernet, URL-safe base64)
    API_CONFIG_ENCRYPTION_KEY: str | None = Field(
        default=None, description="Fernet key for API config credential encryption"
    )

    # External API auth (ronaldo-club) — used by services/external_api_service.py
    # These are intentionally plain strings; do not commit real values.
    EXTERNAL_LOGIN_TOKEN: str = Field(
        default="", description="JWT loginToken cookie for external API"
    )
    EXTERNAL_DDG1: str = Field(default="", description="__ddg1_ session cookie value")
    EXTERNAL_DDG8: str = Field(default="", description="__ddg8_ session cookie value")
    EXTERNAL_DDG9: str = Field(
        default="", description="__ddg9_ IP/session cookie value"
    )
    EXTERNAL_DDG10: str = Field(
        default="", description="__ddg10_ timestamp/session cookie value"
    )
    EXTERNAL_GA: str = Field(
        default="", description="_ga Google Analytics cookie value"
    )
    EXTERNAL_GA_KZWCRF57VT: str = Field(
        default="", description="_ga_KZWCRF57VT GA session cookie value"
    )

    # API integration versioning
    EXTERNAL_API_VERSION: str = Field(
        default="v2",
        description="External API version to use (v1, v2, or v3)",
    )

    @property
    def admin_ids(self) -> List[int]:
        """Parse admin user IDs from comma-separated string"""
        ids = []
        for part in self.ADMIN_USER_IDS.split(","):
            part = part.strip()
            if not part:
                continue
            try:
                ids.append(int(part))
            except ValueError:
                logger.warning(f"Invalid admin user ID: {part}")
                continue
        return ids

    @property
    def sanctioned_countries_set(self) -> set[str]:
        """Parse sanctioned countries into a set for fast lookup"""
        return {
            country.strip().upper()
            for country in self.SANCTIONED_COUNTRIES.split(",")
            if country.strip()
        }

    @field_validator("BOT_TOKEN")
    @classmethod
    def validate_bot_token(cls, v: str):
        """Validate Telegram bot token format"""
        if v and ":" not in v:
            raise ValueError("BOT_TOKEN must be in format <bot_id>:<token>")
        return v

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str):
        """Validate logging level"""
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of: {valid_levels}")
        return v.upper()

    @field_validator("ENVIRONMENT")
    @classmethod
    def validate_environment(cls, v: str):
        valid_envs = {"development", "production", "test"}
        value = (v or "").lower()
        if value not in valid_envs:
            raise ValueError(f"ENVIRONMENT must be one of: {valid_envs}")
        return value


def load_settings() -> Settings:
    """Load application settings with proper error handling"""
    # Ensure .env file is loaded into environment from bot_v2 directory
    from pathlib import Path

    bot_v2_dir = Path(__file__).parent.parent
    load_dotenv(bot_v2_dir / ".env")
    load_dotenv(bot_v2_dir / "config.example.env")

    try:
        settings = Settings()
        logger.info("Configuration loaded successfully")
        return settings
    except ValidationError as e:
        logger.error(f"Configuration validation failed: {e}")
        raise RuntimeError(f"Invalid configuration: {e}")
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise RuntimeError(f"Configuration loading failed: {e}")


# Global settings instance (lazy loaded)
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get cached settings instance"""
    global _settings
    if _settings is None:
        _settings = load_settings()
    return _settings
