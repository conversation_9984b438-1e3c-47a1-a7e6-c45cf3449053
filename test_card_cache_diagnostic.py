#!/usr/bin/env python3
"""
Card Cache Diagnostic Test

This script investigates the card caching issue in add-to-cart operations.
"""

import asyncio
import sys
import os
import json
from typing import Dict, Any, List
from unittest.mock import AsyncMock, patch, MagicMock
sys.path.insert(0, '.')

async def test_card_data_structure():
    """Test the actual card data structure from the API"""
    print("🔍 TESTING CARD DATA STRUCTURE")
    print("=" * 60)
    
    try:
        from services.card_service import CardService
        
        card_service = CardService()
        
        print("📡 Fetching cards from API...")
        result = await card_service.fetch_cards(page=1, limit=3)
        
        if result.get("success", False):
            cards = result.get("data", [])
            print(f"✅ Fetched {len(cards)} cards")
            
            for i, card in enumerate(cards, 1):
                card_id = card.get("_id")
                card_id_type = type(card_id).__name__
                bank = card.get("bank", "N/A")
                bin_num = card.get("bin", "N/A")
                
                print(f"\n📋 Card {i}:")
                print(f"   ID: {card_id} (type: {card_id_type})")
                print(f"   Bank: {bank}")
                print(f"   BIN: {bin_num}")
                
                # Show all keys for debugging
                print(f"   Keys: {list(card.keys())}")
                
                # Check if there are any numeric ID fields
                for key, value in card.items():
                    if isinstance(value, (int, str)) and str(value).isdigit():
                        print(f"   Numeric field {key}: {value} (type: {type(value).__name__})")
            
            return True, cards
        else:
            print(f"❌ Failed to fetch cards: {result.get('error', 'Unknown error')}")
            return False, []
    
    except Exception as e:
        print(f"❌ Error testing card data: {e}")
        import traceback
        traceback.print_exc()
        return False, []

async def test_catalog_handlers_cache():
    """Test the catalog handlers caching mechanism"""
    print(f"\n🗂️ TESTING CATALOG HANDLERS CACHE")
    print("=" * 60)
    
    try:
        from handlers.catalog_handlers import CatalogHandlers
        
        catalog_handlers = CatalogHandlers()
        
        # Simulate user ID
        test_user_id = 12345
        
        # Create test card data (simulating what comes from API)
        test_cards = [
            {
                "_id": "008e5d0531aed73302f4b12cbe86b26550ba522e",
                "bank": "Test Bank 1",
                "bin": "424242",
                "price": "5.00"
            },
            {
                "_id": "1864461",  # This might be the problematic one
                "bank": "Test Bank 2", 
                "bin": "555555",
                "price": "3.50"
            },
            {
                "_id": 1864461,  # Test with numeric ID
                "bank": "Test Bank 3",
                "bin": "666666", 
                "price": "4.25"
            }
        ]
        
        print(f"📝 Simulating cache for user {test_user_id}...")
        
        # Cache the cards
        catalog_handlers.user_current_cards[test_user_id] = test_cards
        
        print(f"✅ Cached {len(test_cards)} cards")
        
        # Test card lookup with different ID formats
        test_lookups = [
            "008e5d0531aed73302f4b12cbe86b26550ba522e",  # String hash
            "1864461",  # String numeric
            1864461,    # Integer
        ]
        
        for lookup_id in test_lookups:
            print(f"\n🔍 Looking up card ID: {lookup_id} (type: {type(lookup_id).__name__})")
            
            cached_cards = catalog_handlers.user_current_cards.get(test_user_id, [])
            found_card = None
            
            for card in cached_cards:
                card_id = card.get("_id")
                print(f"   Comparing with cached ID: {card_id} (type: {type(card_id).__name__})")
                
                if card_id == lookup_id:
                    found_card = card
                    print(f"   ✅ EXACT MATCH found!")
                    break
                elif str(card_id) == str(lookup_id):
                    found_card = card
                    print(f"   ✅ STRING MATCH found!")
                    break
            
            if found_card:
                print(f"   Found card: {found_card.get('bank')}")
            else:
                print(f"   ❌ No match found for {lookup_id}")
        
        return True
    
    except Exception as e:
        print(f"❌ Error testing cache: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_data_parsing():
    """Test callback data parsing"""
    print(f"\n📞 TESTING CALLBACK DATA PARSING")
    print("=" * 60)
    
    # Test different callback data formats
    test_callbacks = [
        "catalog:add_to_cart:008e5d0531aed73302f4b12cbe86b26550ba522e",
        "catalog:add_to_cart:1864461",
        "catalog:add_to_cart:424242",
    ]
    
    for callback_data in test_callbacks:
        print(f"\n📝 Testing callback: {callback_data}")
        
        parts = callback_data.split(":")
        if len(parts) >= 3:
            card_id = parts[2]
            card_id_type = type(card_id).__name__
            
            print(f"   Extracted card_id: {card_id} (type: {card_id_type})")
            
            # Test if it's numeric
            if card_id.isdigit():
                print(f"   ✅ Card ID is numeric string")
                print(f"   As integer: {int(card_id)}")
            else:
                print(f"   ✅ Card ID is hash string")
        else:
            print(f"   ❌ Invalid callback format")
    
    return True

async def test_button_creation():
    """Test how buttons are created"""
    print(f"\n🔘 TESTING BUTTON CREATION")
    print("=" * 60)
    
    try:
        from utils.product_display import ProductDisplayFormatter
        
        formatter = ProductDisplayFormatter()
        
        # Test cards with different ID formats
        test_cards = [
            {
                "_id": "008e5d0531aed73302f4b12cbe86b26550ba522e",
                "bank": "Hash ID Bank",
                "bin": "424242",
                "price": "5.00"
            },
            {
                "_id": "1864461",
                "bank": "String Numeric Bank",
                "bin": "555555", 
                "price": "3.50"
            }
        ]
        
        print("📝 Creating keyboard with test cards...")
        
        keyboard = formatter.create_enhanced_card_keyboard(
            cards=test_cards,
            page=1,
            total_count=2,
            has_filters=False,
            callback_prefix="catalog"
        )
        
        print(f"✅ Created keyboard with {len(keyboard.inline_keyboard)} rows")
        
        # Examine the callback data
        for row_idx, row in enumerate(keyboard.inline_keyboard):
            for btn_idx, button in enumerate(row):
                if "add_to_cart" in button.callback_data:
                    print(f"\n🔘 Button {row_idx}-{btn_idx}:")
                    print(f"   Text: {button.text}")
                    print(f"   Callback: {button.callback_data}")
                    
                    # Extract card ID from callback
                    parts = button.callback_data.split(":")
                    if len(parts) >= 3:
                        extracted_id = parts[2]
                        print(f"   Extracted ID: {extracted_id} (type: {type(extracted_id).__name__})")
        
        return True
    
    except Exception as e:
        print(f"❌ Error testing button creation: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run comprehensive card cache diagnostic"""
    print("🔍 CARD CACHE DIAGNOSTIC")
    print("=" * 60)
    
    results = []
    
    # Test 1: Card data structure
    success, cards = await test_card_data_structure()
    results.append(("Card Data Structure", success))
    
    # Test 2: Cache mechanism
    success = await test_catalog_handlers_cache()
    results.append(("Cache Mechanism", success))
    
    # Test 3: Callback parsing
    success = test_callback_data_parsing()
    results.append(("Callback Parsing", success))
    
    # Test 4: Button creation
    success = await test_button_creation()
    results.append(("Button Creation", success))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    passed = [name for name, success in results if success]
    failed = [name for name, success in results if not success]
    
    print(f"✅ PASSED: {len(passed)}/{len(results)}")
    for test_name in passed:
        print(f"   • {test_name}")
    
    if failed:
        print(f"\n❌ FAILED: {len(failed)}")
        for test_name in failed:
            print(f"   • {test_name}")
    
    print(f"\n🔧 POTENTIAL ISSUES TO INVESTIGATE:")
    print("1. Check if card IDs are being converted somewhere in the pipeline")
    print("2. Verify that the same card ID format is used in cache and callback")
    print("3. Look for any ID transformation in the API response processing")
    print("4. Check if there are multiple sources of card data with different ID formats")
    
    return len(failed) == 0

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
