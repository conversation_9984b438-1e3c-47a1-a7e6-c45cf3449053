#!/usr/bin/env python3
"""
Simple Fix Verification

Test the UIFormatter import fix.
"""

import sys
import os
sys.path.insert(0, '.')

def test_uiformatter_fix():
    """Test that UIFormatter import is removed"""
    print("🔍 Testing UIFormatter Import Fix")
    print("=" * 50)
    
    try:
        with open('handlers/orders_handlers.py', 'r') as f:
            content = f.read()
        
        has_import = 'from utils.ui_components import UIFormatter' in content
        
        if has_import:
            print("❌ UIFormatter import still present")
            return False
        else:
            print("✅ UIFormatter import removed")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_order_details_method():
    """Test that _format_order_details method exists and has proper structure"""
    print("\n🔧 Testing Order Details Method")
    print("=" * 50)
    
    try:
        with open('handlers/orders_handlers.py', 'r') as f:
            content = f.read()
        
        has_method = 'def _format_order_details(' in content
        has_try_catch = 'try:' in content and 'except Exception as e:' in content
        has_error_message = 'Error loading card details' in content
        
        print(f"Method exists: {'✅' if has_method else '❌'}")
        print(f"Has try-catch: {'✅' if has_try_catch else '❌'}")
        print(f"Has error message: {'✅' if has_error_message else '❌'}")
        
        return has_method and has_try_catch and has_error_message
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run simple verification"""
    print("🔍 SIMPLE FIX VERIFICATION")
    print("=" * 50)
    
    test1 = test_uiformatter_fix()
    test2 = test_order_details_method()
    
    print(f"\n📊 RESULTS:")
    print(f"UIFormatter fix: {'✅' if test1 else '❌'}")
    print(f"Order details method: {'✅' if test2 else '❌'}")
    
    if test1 and test2:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ The 'Error loading card details' issue should be fixed")
        return True
    else:
        print(f"\n❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
