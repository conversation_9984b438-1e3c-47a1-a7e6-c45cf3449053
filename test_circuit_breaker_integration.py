#!/usr/bin/env python3
"""
Test script for circuit breaker integration and service status monitoring.

This script tests the circuit breaker pattern implementation and validates
that it properly protects against cascading failures in the checkout process.
"""

import asyncio
import os
import sys
sys.path.insert(0, '.')

from dotenv import load_dotenv
load_dotenv(override=True)

from database.connection import init_database
from services.checkout_queue_service import CheckoutQueueService
from services.circuit_breaker import circuit_breaker_manager, CircuitBreakerOpenError
from services.api_health_monitor import health_monitor


async def test_circuit_breaker_integration():
    """Test the circuit breaker integration with checkout service"""
    
    print("🔌 Testing Circuit Breaker Integration")
    print("=" * 60)
    
    # Initialize database
    await init_database()
    print("✅ Database connected")
    
    # Initialize checkout service
    checkout_service = CheckoutQueueService()
    print("✅ Checkout service initialized")
    
    # Test 1: Circuit Breaker Initialization
    print("\n🔧 Test 1: Circuit Breaker Initialization")
    print("-" * 40)
    
    circuit_breaker = checkout_service.api_v3_circuit_breaker
    print(f"   Circuit breaker name: {circuit_breaker.name}")
    print(f"   Initial state: {circuit_breaker.state.value}")
    print(f"   Failure threshold: {circuit_breaker.config.failure_threshold}")
    print(f"   Recovery timeout: {circuit_breaker.config.recovery_timeout}s")
    print(f"   Success threshold: {circuit_breaker.config.success_threshold}")
    
    # Test 2: Circuit Breaker Stats
    print("\n📊 Test 2: Circuit Breaker Statistics")
    print("-" * 40)
    
    stats = circuit_breaker.get_stats()
    print(f"   State: {stats.state.value}")
    print(f"   Total requests: {stats.total_requests}")
    print(f"   Total failures: {stats.total_failures}")
    print(f"   Total successes: {stats.total_successes}")
    print(f"   Failure rate: {circuit_breaker.get_failure_rate():.1f}%")
    print(f"   Is available: {circuit_breaker.is_available()}")
    
    # Test 3: Service Health Check with Circuit Breaker
    print("\n🏥 Test 3: Service Health Check with Circuit Breaker")
    print("-" * 40)
    
    try:
        health_result = await circuit_breaker.call(
            health_monitor.check_api_v3_health,
            checkout_service.external_api_service
        )
        print(f"   Health check result: {health_result.status.value}")
        print(f"   Response time: {health_result.response_time_ms:.1f}ms")
        if health_result.error_message:
            print(f"   Error: {health_result.error_message}")
    except CircuitBreakerOpenError as e:
        print(f"   ❌ Circuit breaker blocked health check: {e}")
    except Exception as e:
        print(f"   ❌ Health check failed: {e}")
    
    # Test 4: Cart Operations with Circuit Breaker
    print("\n🛒 Test 4: Cart Operations with Circuit Breaker")
    print("-" * 40)
    
    # Test cart clearing
    print("   Testing cart clearing with circuit breaker...")
    try:
        clear_success = await checkout_service._clear_external_cart_v3()
        print(f"   Clear result: {clear_success}")
    except Exception as e:
        print(f"   Clear failed: {e}")
    
    # Test cart viewing
    print("   Testing cart viewing with circuit breaker...")
    try:
        cart_items = await checkout_service._get_external_cart_items_v3_with_retry()
        print(f"   Cart items count: {len(cart_items)}")
    except Exception as e:
        print(f"   Cart view failed: {e}")
    
    # Test 5: Complete Checkout Workflow with Circuit Breaker
    print("\n🔄 Test 5: Complete Checkout Workflow")
    print("-" * 40)
    
    test_cart_items = [
        {
            "card_id": "test123",
            "quantity": 1,
            "card_data": {
                "_id": "test123",
                "name": "Test Card"
            }
        }
    ]
    
    print("   Testing complete checkout workflow with circuit breaker...")
    try:
        workflow_success = await checkout_service._populate_external_cart(test_cart_items)
        print(f"   Workflow result: {workflow_success}")
    except Exception as e:
        print(f"   Workflow failed: {e}")
    
    # Test 6: Circuit Breaker Manager
    print("\n🔧 Test 6: Circuit Breaker Manager")
    print("-" * 40)
    
    all_stats = circuit_breaker_manager.get_all_stats()
    print(f"   Total circuit breakers: {len(all_stats)}")
    
    for name, stats in all_stats.items():
        breaker = circuit_breaker_manager.get_breaker(name)
        print(f"   {name}:")
        print(f"     State: {stats.state.value}")
        print(f"     Requests: {stats.total_requests}")
        print(f"     Failures: {stats.total_failures}")
        print(f"     Failure rate: {breaker.get_failure_rate():.1f}%")
        print(f"     Available: {breaker.is_available()}")
    
    # Test 7: Circuit Breaker Summary
    print("\n📋 Test 7: Circuit Breaker Summary")
    print("-" * 40)
    
    summary = circuit_breaker_manager.get_summary()
    for name, info in summary.items():
        print(f"   {name}: {info}")
    
    # Test 8: Simulate Circuit Breaker Behavior
    print("\n⚡ Test 8: Circuit Breaker Behavior Simulation")
    print("-" * 40)
    
    # Check current state
    current_stats = circuit_breaker.get_stats()
    print(f"   Current state: {current_stats.state.value}")
    print(f"   Current failures: {current_stats.failure_count}")
    print(f"   Failure threshold: {circuit_breaker.config.failure_threshold}")
    
    if current_stats.failure_count >= circuit_breaker.config.failure_threshold:
        print("   ⚠️ Circuit breaker should be OPEN due to failures")
    elif current_stats.state.value == "open":
        print("   🔴 Circuit breaker is OPEN - blocking requests")
        if current_stats.next_attempt_time:
            print(f"   Next attempt time: {current_stats.next_attempt_time}")
    else:
        print("   🟢 Circuit breaker is allowing requests")
    
    # Test 9: Health Monitoring Integration
    print("\n🏥 Test 9: Health Monitoring Integration")
    print("-" * 40)
    
    api_v3_metrics = health_monitor.get_service_health("api_v3")
    if api_v3_metrics:
        print(f"   Service status: {api_v3_metrics.current_status.value}")
        print(f"   Success rate (24h): {api_v3_metrics.success_rate_24h:.1f}%")
        print(f"   Avg response time: {api_v3_metrics.avg_response_time_ms:.1f}ms")
        print(f"   Consecutive failures: {api_v3_metrics.consecutive_failures}")
        if api_v3_metrics.last_error:
            print(f"   Last error: {api_v3_metrics.last_error}")
    else:
        print("   No health metrics available")
    
    print("\n🎯 Circuit Breaker Integration Test Summary")
    print("=" * 60)
    
    final_stats = circuit_breaker.get_stats()
    final_health = health_monitor.get_service_health("api_v3")
    
    print(f"✅ Circuit breaker properly initialized and configured")
    print(f"✅ Circuit breaker state: {final_stats.state.value.upper()}")
    print(f"✅ Total requests processed: {final_stats.total_requests}")
    print(f"✅ Current failure rate: {circuit_breaker.get_failure_rate():.1f}%")
    
    if final_health:
        print(f"✅ Service health monitoring: {final_health.current_status.value.upper()}")
        print(f"✅ Service success rate: {final_health.success_rate_24h:.1f}%")
    
    if final_stats.state.value == "open":
        print("🔴 Circuit breaker is OPEN - protecting against failures")
        print("   This is expected behavior when service is unavailable")
        print("   Circuit breaker will automatically retry after recovery timeout")
    elif final_stats.state.value == "closed":
        print("🟢 Circuit breaker is CLOSED - service is healthy")
    else:
        print("🟡 Circuit breaker is HALF-OPEN - testing service recovery")
    
    print("\n📋 Recommendations:")
    if final_stats.state.value == "open":
        print("   1. Monitor service recovery and wait for automatic retry")
        print("   2. Check external service status and connectivity")
        print("   3. Consider manual circuit breaker reset if service is restored")
    else:
        print("   1. Continue monitoring service health and circuit breaker status")
        print("   2. Set up alerts for circuit breaker state changes")
        print("   3. Monitor failure rates and response times")
    
    return True


if __name__ == "__main__":
    asyncio.run(test_circuit_breaker_integration())
