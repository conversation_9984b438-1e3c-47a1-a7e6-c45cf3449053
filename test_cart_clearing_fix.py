#!/usr/bin/env python3
"""
Test script for API v3 cart clearing functionality.

This script tests:
1. API v3 clear_cart method (empty all items at once)
2. API v3 remove_from_cart method (remove specific items)
3. External API service routing to API v3
4. Cart state verification before and after operations
"""

import asyncio
import logging
import os
import sys
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(override=True)

from api_v3.services.cart_service import APIV3CartService
from api_v3.config.api_config import get_api_v3_config_from_env
from services.external_api_service import ExternalAPIService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_api_v3_cart_clearing():
    """Test API v3 cart clearing functionality directly"""
    logger.info("🧪 Testing API v3 Cart Clearing Functionality")
    logger.info("=" * 60)

    cart_service = None
    try:
        # Get API v3 configuration
        config = get_api_v3_config_from_env()
        if not config:
            logger.error("❌ API v3 configuration not found in environment")
            logger.error("   Set EXTERNAL_V3_BASE_URL, EXTERNAL_V3_USERNAME, EXTERNAL_V3_PASSWORD")
            return False

        # Initialize API v3 cart service
        cart_service = APIV3CartService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )
        
        logger.info("📋 Step 1: View current cart state...")
        view_response = await cart_service.view_cart()
        
        if view_response.get("success"):
            items_before = view_response.get("items", [])
            logger.info(f"📦 Cart contains {len(items_before)} items before clearing")
            
            if items_before:
                for i, item in enumerate(items_before[:3], 1):  # Show first 3 items
                    item_id = item.get("id", "Unknown")
                    item_name = item.get("name", "Unknown")
                    logger.info(f"   Item {i}: {item_id} - {item_name}")
                if len(items_before) > 3:
                    logger.info(f"   ... and {len(items_before) - 3} more items")
            else:
                logger.info("   Cart is empty")
        else:
            logger.error(f"❌ Failed to view cart: {view_response.get('error')}")
            return False
        
        # Test clear_cart method
        logger.info("\n🧹 Step 2: Testing clear_cart method...")
        clear_response = await cart_service.clear_cart()
        
        if clear_response.get("success"):
            logger.info("✅ Clear cart method succeeded")
        else:
            logger.error(f"❌ Clear cart method failed: {clear_response.get('error')}")
            return False
        
        # Verify cart is empty
        logger.info("\n📋 Step 3: Verify cart is empty after clearing...")
        view_response_after = await cart_service.view_cart()
        
        if view_response_after.get("success"):
            items_after = view_response_after.get("items", [])
            logger.info(f"📦 Cart contains {len(items_after)} items after clearing")
            
            if not items_after:
                logger.info("✅ Cart is successfully empty")
                return True
            else:
                logger.error(f"❌ Cart still contains {len(items_after)} items after clearing")
                for item in items_after:
                    item_id = item.get("id", "Unknown")
                    logger.error(f"   Remaining item: {item_id}")
                return False
        else:
            logger.error(f"❌ Failed to verify cart state: {view_response_after.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False
    finally:
        if cart_service:
            await cart_service.close()


async def test_external_api_service_routing():
    """Test external API service routing to API v3"""
    logger.info("\n🧪 Testing External API Service Routing")
    logger.info("=" * 60)
    
    try:
        # Initialize external API service
        external_service = ExternalAPIService()
        
        logger.info(f"External API version: {external_service.api_version}")
        
        # Test view_cart routing
        logger.info("\n📋 Step 1: Test view_cart routing...")
        view_response = await external_service.view_cart()
        
        if view_response.success:
            cart_data = view_response.data
            if isinstance(cart_data, dict) and "data" in cart_data:
                items = cart_data["data"]
                logger.info(f"✅ View cart succeeded - {len(items)} items found")
            else:
                logger.info(f"✅ View cart succeeded - data format: {type(cart_data)}")
        else:
            logger.error(f"❌ View cart failed: {view_response.error}")
            return False
        
        # Test clear_cart routing
        logger.info("\n🧹 Step 2: Test clear_cart routing...")
        clear_response = await external_service.clear_cart()
        
        if clear_response.success:
            logger.info("✅ Clear cart routing succeeded")
        else:
            logger.error(f"❌ Clear cart routing failed: {clear_response.error}")
            return False
        
        # Verify cart is empty
        logger.info("\n📋 Step 3: Verify cart state after clearing...")
        view_response_after = await external_service.view_cart()
        
        if view_response_after.success:
            cart_data_after = view_response_after.data
            if isinstance(cart_data_after, dict) and "data" in cart_data_after:
                items_after = cart_data_after["data"]
                logger.info(f"📦 Cart contains {len(items_after)} items after clearing")
                
                if not items_after:
                    logger.info("✅ External API service cart clearing verified")
                    return True
                else:
                    logger.warning(f"⚠️ Cart still contains {len(items_after)} items")
                    return True  # May be expected if cart had items added during test
            else:
                logger.info("✅ External API service cart clearing completed")
                return True
        else:
            logger.error(f"❌ Failed to verify cart state: {view_response_after.error}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def test_remove_specific_items():
    """Test removing specific items from cart"""
    logger.info("\n🧪 Testing Remove Specific Items")
    logger.info("=" * 60)

    cart_service = None
    try:
        # Get API v3 configuration
        config = get_api_v3_config_from_env()
        if not config:
            logger.error("❌ API v3 configuration not found in environment")
            return False

        # Initialize API v3 cart service
        cart_service = APIV3CartService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )
        
        # First, view cart to get some item IDs
        logger.info("📋 Step 1: Get current cart items...")
        view_response = await cart_service.view_cart()
        
        if not view_response.get("success"):
            logger.error(f"❌ Failed to view cart: {view_response.get('error')}")
            return False
        
        items = view_response.get("items", [])
        if not items:
            logger.info("ℹ️ Cart is empty, cannot test item removal")
            return True
        
        # Take first 2 items for removal test
        items_to_remove = items[:2]
        item_ids = [item.get("id") for item in items_to_remove if item.get("id")]
        
        if not item_ids:
            logger.info("ℹ️ No valid item IDs found for removal test")
            return True
        
        logger.info(f"🎯 Testing removal of {len(item_ids)} items:")
        for item_id in item_ids:
            logger.info(f"   - {item_id}")
        
        # Test remove_from_cart method
        logger.info("\n🗑️ Step 2: Remove specific items...")
        remove_response = await cart_service.remove_from_cart(item_ids)
        
        if remove_response.get("success"):
            logger.info(f"✅ Successfully removed {len(item_ids)} items")
        else:
            logger.error(f"❌ Failed to remove items: {remove_response.get('error')}")
            return False
        
        # Verify items were removed
        logger.info("\n📋 Step 3: Verify items were removed...")
        view_response_after = await cart_service.view_cart()
        
        if view_response_after.get("success"):
            items_after = view_response_after.get("items", [])
            remaining_ids = [item.get("id") for item in items_after]
            
            removed_count = 0
            for item_id in item_ids:
                if item_id not in remaining_ids:
                    removed_count += 1
                    logger.info(f"✅ Item {item_id} successfully removed")
                else:
                    logger.warning(f"⚠️ Item {item_id} still in cart")
            
            logger.info(f"📊 Removal summary: {removed_count}/{len(item_ids)} items removed")
            return removed_count > 0
        else:
            logger.error(f"❌ Failed to verify removal: {view_response_after.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False
    finally:
        if cart_service:
            await cart_service.close()


async def main():
    """Run all cart clearing tests"""
    logger.info("🚀 Starting Cart Clearing Functionality Tests")
    logger.info("=" * 80)
    
    test_results = []
    
    # Test 1: API v3 cart clearing
    result1 = await test_api_v3_cart_clearing()
    test_results.append(("API v3 Cart Clearing", result1))
    
    # Test 2: External API service routing
    result2 = await test_external_api_service_routing()
    test_results.append(("External API Service Routing", result2))
    
    # Test 3: Remove specific items
    result3 = await test_remove_specific_items()
    test_results.append(("Remove Specific Items", result3))
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 80)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        logger.info("🎉 All cart clearing tests completed successfully!")
        logger.info("   The API v3 cart clearing integration is working correctly.")
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
    
    return passed == len(test_results)


if __name__ == "__main__":
    asyncio.run(main())
