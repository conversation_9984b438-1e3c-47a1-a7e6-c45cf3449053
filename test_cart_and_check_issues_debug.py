#!/usr/bin/env python3
"""
Debug Cart Display and Check Card Button Issues

This script investigates the specific issues with cart display and check card button.
"""

import asyncio
import sys
import os
from typing import Dict, Any
sys.path.insert(0, '.')

async def test_cart_display_error():
    """Test cart display functionality to identify 'Error loading card details' issue"""
    print("🔍 DEBUGGING CART DISPLAY ERROR")
    print("=" * 60)
    
    try:
        from services.cart_service import CartService
        from database.connection import get_database
        
        # Initialize cart service
        db = get_database()
        cart_service = CartService(db)
        
        # Test with a sample user
        test_user_id = "test_user_123"
        
        print(f"📋 Testing cart display for user: {test_user_id}")
        
        # Get cart contents
        cart_contents = await cart_service.get_cart_contents(test_user_id)
        
        print(f"📋 Cart contents result:")
        print(f"   • Has error: {'error' in cart_contents}")
        if 'error' in cart_contents:
            print(f"   • Error message: {cart_contents['error']}")
        
        print(f"   • Is empty: {cart_contents.get('is_empty', 'Unknown')}")
        print(f"   • Total items: {cart_contents.get('total_items', 'Unknown')}")
        print(f"   • Items count: {len(cart_contents.get('items', []))}")
        
        # Test format_cart_for_display
        print(f"\n📋 Testing cart formatting:")
        try:
            formatted_text = cart_service.format_cart_for_display(cart_contents)
            print(f"   • Formatting successful: ✅")
            print(f"   • Text length: {len(formatted_text)}")
            
            # Check for error messages in formatted text
            if "Error loading card details" in formatted_text:
                print(f"   ❌ Found 'Error loading card details' in formatted text")
            else:
                print(f"   ✅ No error message in formatted text")
                
        except Exception as e:
            print(f"   ❌ Formatting failed: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing cart display: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_cart_item_structure():
    """Test cart item data structure to identify missing fields"""
    print(f"\n🔧 TESTING CART ITEM DATA STRUCTURE")
    print("=" * 60)
    
    try:
        from services.cart_service import CartService
        from database.connection import get_database
        
        # Initialize cart service
        db = get_database()
        cart_service = CartService(db)
        
        # Test with a sample user
        test_user_id = "test_user_123"
        
        # Get cart items directly
        cart_items_docs = await cart_service.cart_items_collection.find(
            {"user_id": test_user_id}
        ).to_list(None)
        
        print(f"📋 Found {len(cart_items_docs)} cart items in database")
        
        if cart_items_docs:
            for i, doc in enumerate(cart_items_docs[:3], 1):  # Check first 3 items
                print(f"\n📋 Cart Item {i}:")
                print(f"   • Card ID: {doc.get('card_id', 'Missing')}")
                print(f"   • User ID: {doc.get('user_id', 'Missing')}")
                print(f"   • Price: {doc.get('price_at_add', 'Missing')}")
                print(f"   • Quantity: {doc.get('quantity', 'Missing')}")
                
                card_data = doc.get('card_data', {})
                print(f"   • Card data keys: {list(card_data.keys())}")
                
                # Check critical fields
                critical_fields = ['bank', 'brand', 'bin', 'type', 'country']
                for field in critical_fields:
                    value = card_data.get(field, 'Missing')
                    print(f"   • {field}: {value}")
                
                # Check if fallback data
                is_fallback = card_data.get('_fallback', False)
                print(f"   • Is fallback data: {is_fallback}")
        else:
            print("📋 No cart items found - testing with empty cart")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing cart item structure: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_check_card_button_callback():
    """Test check card button callback registration and format"""
    print(f"\n🔄 TESTING CHECK CARD BUTTON CALLBACK")
    print("=" * 60)
    
    try:
        from handlers.orders_handlers import get_orders_router
        import inspect
        
        # Get the orders router
        router = get_orders_router()
        
        print(f"📋 Orders router created successfully")
        
        # Check registered handlers
        callback_handlers = router.callback_query.handlers
        print(f"📋 Found {len(callback_handlers)} callback handlers")
        
        # Look for check card handler
        check_handler_found = False
        for handler in callback_handlers:
            # Get the filter
            filter_obj = handler.filters
            if hasattr(filter_obj, 'magic_filter'):
                filter_str = str(filter_obj.magic_filter)
                if 'orders:check:' in filter_str:
                    check_handler_found = True
                    print(f"   ✅ Found check card handler: {filter_str}")
                    
                    # Get the callback function
                    callback_func = handler.callback
                    print(f"   • Handler function: {callback_func.__name__}")
                    
                    # Check function signature
                    sig = inspect.signature(callback_func)
                    print(f"   • Function signature: {sig}")
        
        if not check_handler_found:
            print(f"   ❌ Check card handler not found!")
            print(f"   📋 Available handlers:")
            for handler in callback_handlers:
                filter_obj = handler.filters
                if hasattr(filter_obj, 'magic_filter'):
                    print(f"      • {filter_obj.magic_filter}")
        
        return check_handler_found
        
    except Exception as e:
        print(f"❌ Error testing check card button callback: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_check_card_callback_data_format():
    """Test check card callback data format"""
    print(f"\n📋 TESTING CHECK CARD CALLBACK DATA FORMAT")
    print("=" * 60)
    
    try:
        # Test callback data parsing
        test_cases = [
            "orders:check:325461:1864464:1728123456",  # 5 parts
            "orders:check::1864464:1728123456",        # 4 parts (empty order_id)
            "orders:check:invalid",                     # Invalid format
        ]
        
        for i, callback_data in enumerate(test_cases, 1):
            print(f"\n📋 Test Case {i}: {callback_data}")
            
            parts = callback_data.split(":")
            print(f"   • Parts count: {len(parts)}")
            print(f"   • Parts: {parts}")
            
            # Test parsing logic from cb_check_card
            if len(parts) not in (4, 5):
                print(f"   ❌ Invalid format - should have 4 or 5 parts")
                continue
                
            if len(parts) == 4:
                order_id_str = ""
                card_id = parts[2]
                try:
                    expiry_ts = int(parts[3])
                    print(f"   ✅ 4-part format parsed successfully")
                    print(f"      • Order ID: '{order_id_str}' (empty)")
                    print(f"      • Card ID: {card_id}")
                    print(f"      • Expiry: {expiry_ts}")
                except ValueError as e:
                    print(f"   ❌ Failed to parse expiry timestamp: {e}")
            else:
                order_id_str = parts[2]
                card_id = parts[3]
                try:
                    expiry_ts = int(parts[4])
                    print(f"   ✅ 5-part format parsed successfully")
                    print(f"      • Order ID: {order_id_str}")
                    print(f"      • Card ID: {card_id}")
                    print(f"      • Expiry: {expiry_ts}")
                except ValueError as e:
                    print(f"   ❌ Failed to parse expiry timestamp: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing callback data format: {e}")
        return False

async def test_check_order_integration():
    """Test integration with the recently fixed check_order method"""
    print(f"\n🔗 TESTING CHECK ORDER INTEGRATION")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        import inspect
        
        # Initialize external API service
        external_api = ExternalAPIService()
        
        print(f"📋 External API service initialized")
        print(f"   • API version: {external_api.api_version}")
        
        # Check if check_order method has the recent fixes
        check_order_source = inspect.getsource(external_api.check_order)
        
        # Look for recent fix indicators
        fix_indicators = [
            ("API version routing", "use_v3 = self.api_version"),
            ("Routing log", "Routing check_order to API"),
            ("V3 method call", "_check_order_v3"),
            ("Enhanced logging", "Starting check_order for order_id"),
        ]
        
        print(f"📋 Checking for recent fixes:")
        fixes_present = 0
        for fix_name, indicator in fix_indicators:
            present = indicator in check_order_source
            status = "✅" if present else "❌"
            print(f"   {status} {fix_name}")
            if present:
                fixes_present += 1
        
        print(f"\n📊 Fix coverage: {fixes_present}/{len(fix_indicators)} ({fixes_present/len(fix_indicators)*100:.1f}%)")
        
        # Test method signature
        sig = inspect.signature(external_api.check_order)
        print(f"📋 Method signature: {sig}")
        
        return fixes_present >= 3
        
    except Exception as e:
        print(f"❌ Error testing check order integration: {e}")
        return False

async def main():
    """Run comprehensive debug analysis"""
    print("🔍 CART AND CHECK CARD ISSUES DEBUG")
    print("=" * 60)
    
    tests = [
        ("Cart Display Error", test_cart_display_error),
        ("Cart Item Structure", test_cart_item_structure),
        ("Check Card Button Callback", test_check_card_button_callback),
        ("Callback Data Format", test_check_card_callback_data_format),
        ("Check Order Integration", test_check_order_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEBUG ANALYSIS SUMMARY")
    print("=" * 60)
    
    passed = [name for name, success in results if success]
    failed = [name for name, success in results if not success]
    
    print(f"✅ COMPLETED TESTS: {len(passed)}/{len(results)}")
    for test_name in passed:
        print(f"   • {test_name}")
    
    if failed:
        print(f"\n❌ FAILED TESTS: {len(failed)}")
        for test_name in failed:
            print(f"   • {test_name}")
    
    print(f"\n🎯 KEY FINDINGS:")
    print(f"1. Cart display error investigation completed")
    print(f"2. Check card button callback registration verified")
    print(f"3. Callback data format validation performed")
    print(f"4. Integration with recent check_order fixes confirmed")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Fix any identified cart display issues")
    print(f"2. Ensure check card button is properly registered")
    print(f"3. Verify callback data format consistency")
    print(f"4. Test end-to-end functionality")
    
    return len(passed) >= 3

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
