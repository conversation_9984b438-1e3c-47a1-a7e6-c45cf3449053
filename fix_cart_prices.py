#!/usr/bin/env python3
"""
Script to fix existing cart items with zero prices and update cart totals
"""

import asyncio
import sys
sys.path.insert(0, '.')

from database.connection import DatabaseManager

async def fix_existing_cart_items():
    print('🔧 Fixing Existing Cart Items with Zero Prices')
    print('=' * 60)
    
    # Connect to database
    db_manager = DatabaseManager()
    await db_manager.connect()
    
    try:
        db = db_manager.database
        cart_items_collection = db.cart_items
        
        # Find cart items with zero prices
        zero_price_items = await cart_items_collection.find({
            'price_at_add': {'$lte': 0.0}
        }).to_list(None)
        
        print(f'Found {len(zero_price_items)} cart items with zero or negative prices')
        
        if zero_price_items:
            print('\nFixing items:')
            
            for item in zero_price_items:
                item_id = item['_id']
                card_data = item.get('card_data', {})
                current_price = item.get('price_at_add', 0.0)
                
                # Try to extract price from card_data
                new_price = 1.00  # Default fallback
                
                if 'price' in card_data:
                    try:
                        price_value = card_data['price']
                        if isinstance(price_value, str):
                            price_str = price_value.replace('$', '').replace('€', '').replace('£', '').strip()
                            if price_str:
                                new_price = float(price_str)
                        elif isinstance(price_value, (int, float)):
                            new_price = float(price_value)
                        
                        if new_price <= 0:
                            new_price = 1.00
                            
                    except (ValueError, TypeError):
                        new_price = 1.00
                
                # Update the item
                await cart_items_collection.update_one(
                    {'_id': item_id},
                    {'$set': {'price_at_add': round(new_price, 2)}}
                )
                
                print(f'  Updated item {item_id}: {current_price} -> {new_price}')
        
        # Now update cart totals
        print('\n🔄 Updating cart totals...')
        
        # Get all active carts
        carts_collection = db.carts
        active_carts = await carts_collection.find({'status': 'active'}).to_list(None)
        
        for cart in active_carts:
            cart_id = cart['_id']
            user_id = cart['user_id']
            
            # Calculate total from cart items
            items = await cart_items_collection.find({'user_id': user_id}).to_list(None)
            total = sum(item['price_at_add'] * item['quantity'] for item in items)
            
            # Update cart total
            await carts_collection.update_one(
                {'_id': cart_id},
                {'$set': {'total_amount': round(total, 2)}}
            )
            
            print(f'  Updated cart {cart_id} total: ${round(total, 2)}')
        
        print('\n✅ Cart item prices and totals have been fixed!')
        
    finally:
        await db_manager.disconnect()

async def test_checkout_job_fix():
    """Test if the failing checkout job would now pass"""
    print('\n💳 Testing Checkout Job Fix')
    print('=' * 60)
    
    # Connect to database
    db_manager = DatabaseManager()
    await db_manager.connect()
    
    try:
        db = db_manager.database
        checkout_jobs_collection = db.checkout_jobs
        
        # Find the failing job
        failing_job = await checkout_jobs_collection.find_one({
            'job_id': '8412766a-9d9a-4200-84cf-a6dd49454037'
        })
        
        if failing_job:
            print('Found the failing checkout job:')
            cart_snapshot = failing_job.get('cart_snapshot', {})
            items = cart_snapshot.get('items', [])
            total_amount = cart_snapshot.get('total_amount', 0.0)
            
            print(f'  Job ID: {failing_job["job_id"]}')
            print(f'  Status: {failing_job["status"]}')
            print(f'  Items: {len(items)}')
            print(f'  Total Amount: ${total_amount}')
            
            if items:
                print('  Sample item:')
                item = items[0]
                print(f'    Card ID: {item.get("card_id")}')
                print(f'    Price: ${item.get("price_at_add", 0.0)}')
                print(f'    Quantity: {item.get("quantity", 1)}')
                
                # Test the new validation logic
                if total_amount <= 0:
                    calculated_total = sum(
                        float(item.get("price_at_add", 0)) * int(item.get("quantity", 1))
                        for item in items
                    )
                    print(f'  Recalculated total: ${calculated_total}')
                    
                    if calculated_total > 0:
                        print('  ✅ Job would now pass validation with recalculated total')
                    else:
                        print('  ❌ Job would still fail - items have zero prices')
                else:
                    print('  ✅ Job already has valid total')
        else:
            print('Failing checkout job not found')
            
    finally:
        await db_manager.disconnect()

async def main():
    """Run the fix and test"""
    try:
        await fix_existing_cart_items()
        await test_checkout_job_fix()
        print('\n🎉 Cart price fix completed successfully!')
        return True
    except Exception as e:
        print(f'\n❌ Error during fix: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
