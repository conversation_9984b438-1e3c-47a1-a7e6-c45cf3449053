# Demo Wallet & Card Bot — Production Configuration
# This file contains production-ready security configurations
# Copy to .env and customize for your production environment

# REQUIRED: Bot Configuration
BOT_TOKEN=your_production_bot_token_here
DEMO_API_BASE=https://your-production-api.example.com
DEMO_API_TOKEN=your_production_api_token_here

# Database Configuration (Production MongoDB)
USE_MONGODB=true
MONGODB_URL=mongodb://username:<EMAIL>:27017/demo_wallet_bot?ssl=true&authSource=admin
DATABASE_NAME=demo_wallet_bot_prod
MONGODB_AUTH_SOURCE=admin
MONGODB_AUTH_MECHANISM=SCRAM-SHA-256

# Security Configuration
ENVIRONMENT=production
ADMIN_USER_IDS=your_admin_telegram_id_here
ADMIN_PASSPHRASE=your_secure_admin_passphrase_here

# API Security (Generate secure keys)
# Generate with: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
API_CONFIG_ENCRYPTION_KEY=your_fernet_encryption_key_here
API_ENCRYPTION_KEY=your_api_encryption_key_here
API_ENCRYPTION_SALT=your_base64_encoded_salt_here

# Wallet Settings (Production Limits)
INITIAL_BALANCE=50.00
DEFAULT_CURRENCY=USD

# Enhanced Compliance & Security
SANCTIONED_COUNTRIES=CU,IR,KP,SY,UA-CRIMEA,RU,BY
AML_HOURLY_LIMIT=100
DAILY_SPEND_CAP=250
MONTHLY_SPEND_CAP=1000

# Strict Rate Limiting (Production)
PURCHASES_PER_MINUTE=2
PURCHASES_PER_DAY=25
SEARCHES_PER_MINUTE=5
MESSAGES_PER_MINUTE=5
CALLBACKS_PER_MINUTE=10

# Production Features
FALLBACK_ENABLED=false
METRICS_ENABLED=true
METRICS_PORT=8000
RETENTION_ENABLED=true
RETENTION_DAYS=30

# Production Logging
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_FILE_PATH=/var/log/demo-wallet-bot/bot.log
LOG_MAX_SIZE=52428800
LOG_BACKUP_COUNT=10
LOG_COLOR=false
LOG_STRUCTURED=true
LOG_SHOW_CATEGORY=true

# Security Monitoring
ENABLE_SECURITY_LOGGING=true
SECURITY_LOG_FILE=/var/log/demo-wallet-bot/security.log
ENABLE_AUDIT_LOGGING=true

# Performance & Reliability
CONNECTION_POOL_SIZE=20
CONNECTION_TIMEOUT=10
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1

# Health Check Configuration
HEALTH_CHECK_INTERVAL=60
HEALTH_CHECK_TIMEOUT=10
ENABLE_HEALTH_MONITORING=true

# Backup Configuration
ENABLE_AUTOMATED_BACKUPS=true
BACKUP_INTERVAL_HOURS=6
BACKUP_RETENTION_DAYS=7
BACKUP_LOCATION=/var/backups/demo-wallet-bot

# SSL/TLS Configuration
FORCE_HTTPS=true
SSL_VERIFY=true
TLS_VERSION=1.2

# Additional Security Headers
SECURITY_HEADERS_ENABLED=true
CONTENT_SECURITY_POLICY=default-src 'self'
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff

# Monitoring & Alerting
ENABLE_PROMETHEUS_METRICS=true
PROMETHEUS_PORT=9090
ENABLE_HEALTH_ENDPOINT=true
HEALTH_ENDPOINT_PORT=8080

# Error Reporting
ENABLE_ERROR_REPORTING=true
ERROR_REPORTING_ENDPOINT=https://your-error-tracking.example.com/api/errors
ERROR_REPORTING_API_KEY=your_error_reporting_api_key

# Data Protection
ENABLE_DATA_ENCRYPTION=true
ENCRYPTION_ALGORITHM=AES-256-GCM
KEY_ROTATION_INTERVAL_DAYS=90

# Compliance
GDPR_COMPLIANCE_ENABLED=true
DATA_RETENTION_POLICY_DAYS=365
ENABLE_RIGHT_TO_DELETION=true
ENABLE_DATA_EXPORT=true

# Network Security
ALLOWED_IPS=0.0.0.0/0
BLOCKED_IPS=
ENABLE_GEOBLOCKING=false
ALLOWED_COUNTRIES=

# Session Security
SESSION_TIMEOUT_MINUTES=30
MAX_CONCURRENT_SESSIONS=3
ENABLE_SESSION_ENCRYPTION=true

# API Rate Limiting
API_RATE_LIMIT_PER_MINUTE=60
API_RATE_LIMIT_PER_HOUR=1000
API_RATE_LIMIT_PER_DAY=10000

# External API integration (API v3 removed - use v2 by default)
EXTERNAL_API_VERSION=v2

# Webhook Security (if using webhooks)
WEBHOOK_SECRET=your_webhook_secret_here
WEBHOOK_URL=https://your-domain.com/webhook
WEBHOOK_MAX_CONNECTIONS=100

# Development/Testing Overrides (set to false in production)
DEBUG_MODE=false
ENABLE_TEST_ENDPOINTS=false
SKIP_SSL_VERIFICATION=false
ALLOW_INSECURE_CONNECTIONS=false

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.

# Feature Flags
ENABLE_NEW_FEATURES=false
ENABLE_BETA_FEATURES=false
ENABLE_EXPERIMENTAL_FEATURES=false

# Performance Tuning
MAX_WORKERS=4
WORKER_TIMEOUT=30
KEEP_ALIVE_TIMEOUT=2
MAX_REQUESTS_PER_WORKER=1000

# Cache Configuration
ENABLE_CACHING=true
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000
REDIS_URL=redis://your-redis-server:6379/0

# Queue Configuration
ENABLE_TASK_QUEUE=true
QUEUE_BACKEND=redis
QUEUE_URL=redis://your-redis-server:6379/1
MAX_QUEUE_SIZE=10000

# Notification Configuration
ENABLE_NOTIFICATIONS=true
NOTIFICATION_CHANNELS=email,sms,webhook
SMTP_SERVER=your-smtp-server.com
SMTP_PORT=587
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password

# Deployment Information
DEPLOYMENT_VERSION=2.0.0
DEPLOYMENT_DATE=2024-01-01T00:00:00Z
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_REGION=us-east-1
