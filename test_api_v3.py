#!/usr/bin/env python3
"""
Quick test of API v3 authentication with the bot's implementation
"""
import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv

load_dotenv()

# Import API v3
from api_v3.config import get_api_v3_config_from_env
from api_v3.services import APIV3BrowseService


async def test_api_v3():
    """Test API v3 authentication and browsing"""
    print("=" * 70)
    print("🧪 Testing API v3 Implementation")
    print("=" * 70)
    print()

    # Get configuration
    config = get_api_v3_config_from_env()

    if not config:
        print("❌ Error: Could not load API v3 configuration from environment")
        return False

    print("📋 Configuration:")
    print(f"   Base URL: {config.base_url}")
    print(f"   Username: {config.username}")
    print(f"   Use SOCKS Proxy: {config.use_socks_proxy}")
    print(f"   SOCKS URL: {config.socks_url}")
    print(f"   Timeout: {config.timeout}s")
    print()

    # Create browse service
    print("🔧 Creating APIV3BrowseService...")
    service = APIV3BrowseService(
        base_url=config.base_url,
        username=config.username,
        password=config.password,
        use_socks_proxy=config.use_socks_proxy,
        socks_url=config.socks_url,
    )

    print("✅ Service created")
    print()

    try:
        # Test authentication by fetching shop page
        print("-" * 70)
        print("🔐 Testing authentication...")
        print("-" * 70)

        response = await service.list_items()

        if response.success:
            print("✅ Authentication and data fetch successful!")
            print(f"   Retrieved {len(response.data.get('data', []))} items")

            # Show first few items
            items = response.data.get("data", [])
            if items:
                print()
                print("📦 Sample items:")
                for i, item in enumerate(items[:3], 1):
                    print(
                        f"   {i}. {item.get('name', 'N/A')} - ${item.get('price', 'N/A')}"
                    )

            return True
        else:
            print(f"❌ Failed: {response.error}")
            print(f"   Error Message: {response.error}")
            return False

    except Exception as e:
        print(f"❌ Exception occurred: {type(e).__name__}: {e}")
        import traceback

        traceback.print_exc()
        return False
    finally:
        # Clean up
        await service.close()
        print()
        print("🔒 Service closed")


def main():
    """Main entry point"""
    try:
        success = asyncio.run(test_api_v3())

        print()
        print("=" * 70)
        print("📊 Test Result")
        print("=" * 70)

        if success:
            print("✅ API v3 is working correctly!")
            print()
            print("🎉 You can now use the bot with API v3 enabled.")
            return 0
        else:
            print("❌ API v3 test failed")
            print()
            print("🔧 Please check the error messages above for details.")
            return 1

    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
