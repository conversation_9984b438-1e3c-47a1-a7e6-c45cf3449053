"""
API v3 Integration Tests - Fixed

Tests for API v3 browse service and adapter functionality.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from api_v3.services.browse_service import (
    APIV3BrowseService,
    APIV3BrowseParams,
    APIV3BrowseResponse,
)
from api_v3.adapter import APIV3Adapter
from api_v3.models.card_model import APIV3Card, APIV3CardList


@pytest.fixture
def sample_list_response():
    """Sample list response from API v3"""
    return {
        "payload": {"_token": "test_token"},
        "headers": [
            "",
            "BIN",
            "Expiry",
            "Base",
            "F. Name",
            "Country/Ethnicity/Continent",
            "Scheme/Type/Level",
            "Address/Phone/DOB",
            "Price",
        ],
        "rows": [
            [
                {
                    "text": "checkbox",
                    "input_type": "checkbox",
                    "input_name": "checked[]",
                    "input_value": "test_card_id_1",
                    "input_checked": False,
                },
                {"text": "555426"},
                {"text": "10/25"},
                {"text": "TEST-BASE-1"},
                {"text": "John D."},
                {"text": "UNITED STATES,North America"},
                {"text": "MASTERCARD DEBIT PREPAID"},
                {"text": "123 Main St, City, 12345, StatePhone : +1..."},
                {"text": "8.5$"},
            ],
            [
                {
                    "text": "checkbox",
                    "input_type": "checkbox",
                    "input_name": "checked[]",
                    "input_value": "test_card_id_2",
                    "input_checked": False,
                },
                {"text": "411111"},
                {"text": "12/26"},
                {"text": "TEST-BASE-2"},
                {"text": "Jane S."},
                {"text": "CANADA,North America"},
                {"text": "VISA CREDIT PLATINUM"},
                {"text": "No addressPhone : +1...DOB: YES"},
                {"text": "12.5$"},
            ],
        ],
    }


@pytest.fixture
def sample_filter_response():
    """Sample filter response from API v3"""
    return [
        {
            "name": "continent[]",
            "options": [
                {"label": "Africa", "value": "Africa", "selected": False},
                {"label": "Asia", "value": "Asia", "selected": False},
                {"label": "North America", "value": "North America", "selected": False},
            ],
        },
        {
            "name": "country[]",
            "options": [
                {"label": "UNITED STATES", "value": "UNITED STATES", "selected": False},
                {"label": "CANADA", "value": "CANADA", "selected": False},
            ],
        },
        {
            "name": "scheme[]",
            "options": [
                {"label": "VISA", "value": "VISA", "selected": False},
                {"label": "MASTERCARD", "value": "MASTERCARD", "selected": False},
            ],
        },
    ]


class TestAPIV3Card:
    """Tests for APIV3Card model"""

    def test_card_from_table_row(self, sample_list_response):
        """Test creating card from table row"""
        headers = sample_list_response["headers"]
        row = sample_list_response["rows"][0]

        card = APIV3Card.from_table_row(row, headers)

        assert card is not None
        assert card.id == "test_card_id_1"
        assert card.bin == "555426"
        assert card.expiry == "10/25"
        assert card.name == "John D."
        assert card.country == "UNITED STATES"
        assert card.continent == "North America"
        assert card.brand == "MASTERCARD"
        assert card.type == "DEBIT"

    def test_card_parsing(self):
        """Test card field parsing"""
        card = APIV3Card(
            _id="test_id",
            bin="555426",
            country="UNITED STATES",
            continent="North America",
            scheme_type_level="MASTERCARD DEBIT PREPAID",
            address_phone_dob="123 Main St, City, 12345Phone : +1234567890DOB: YES",
            price="8.5",
        )

        assert card.id == "test_id"
        assert card.country == "UNITED STATES"
        assert card.continent == "North America"
        assert card.price == "8.5"

    def test_card_to_dict(self, sample_list_response):
        """Test converting card to dictionary"""
        headers = sample_list_response["headers"]
        row = sample_list_response["rows"][0]

        card = APIV3Card.from_table_row(row, headers)
        card_dict = card.to_dict()

        assert card_dict["_id"] == "test_card_id_1"
        assert card_dict["bin"] == "555426"
        assert card_dict["country"] == "UNITED STATES"


class TestAPIV3CardList:
    """Tests for APIV3CardList model"""

    def test_card_list_from_table_data(self, sample_list_response):
        """Test creating card list from table data"""
        card_list = APIV3CardList.from_table_data(sample_list_response)

        assert len(card_list.cards) == 2
        assert card_list.cards[0].id == "test_card_id_1"
        assert card_list.cards[1].id == "test_card_id_2"

    def test_card_list_to_dict_list(self, sample_list_response):
        """Test converting card list to dictionary list"""
        card_list = APIV3CardList.from_table_data(sample_list_response)
        dict_list = card_list.to_dict_list()

        assert isinstance(dict_list, list)
        assert len(dict_list) == 2
        assert dict_list[0]["_id"] == "test_card_id_1"
        assert dict_list[1]["_id"] == "test_card_id_2"


class TestAPIV3BrowseParams:
    """Tests for APIV3BrowseParams"""

    def test_params_from_standard_filters(self):
        """Test converting from standard filters"""
        standard_filters = {
            "bin": "555426",
            "country": "UNITED STATES",
            "brand": "MASTERCARD",
            "phone": True,
        }

        params = APIV3BrowseParams.from_standard_filters(standard_filters)

        assert params.bins == "555426"
        assert params.country == "UNITED STATES"
        assert params.scheme == "MASTERCARD"
        assert params.with_phone == "true"

    def test_params_creation(self):
        """Test direct params creation"""
        params = APIV3BrowseParams(
            bins="555426",
            country="UNITED STATES",
            scheme="MASTERCARD",
            with_phone="true",
            page=1,
            limit=50,
        )

        assert params.bins == "555426"
        assert params.country == "UNITED STATES"
        assert params.scheme == "MASTERCARD"
        assert params.with_phone == "true"
        assert params.page == 1
        assert params.limit == 50


@pytest.mark.asyncio
class TestAPIV3BrowseService:
    """Tests for APIV3BrowseService (live tests)"""

    async def test_service_initialization(self):
        """Test service can be initialized"""
        # Skip if no config
        from api_v3.config import get_api_v3_config_from_env

        config = get_api_v3_config_from_env()
        if not config:
            pytest.skip("No API v3 configuration available")

        service = APIV3BrowseService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )

        assert service is not None
        await service.close()


class TestAPIV3Adapter:
    """Tests for APIV3Adapter"""

    def test_adapter_with_mock_service(self):
        """Test adapter with mock service"""
        mock_service = MagicMock()
        adapter = APIV3Adapter(browse_service=mock_service)

        assert adapter is not None
        assert adapter.browse_service == mock_service

    def test_filter_conversion(self):
        """Test filter conversion"""
        # Test standard filter to API v3 params conversion
        standard_filters = {
            "country": "UNITED STATES",
            "brand": "VISA",
            "phone": True,
        }

        params = APIV3BrowseParams.from_standard_filters(standard_filters)

        assert params.country == "UNITED STATES"
        assert params.scheme == "VISA"
        assert params.with_phone == "true"


# Integration tests that require actual connection
@pytest.mark.integration
@pytest.mark.asyncio
class TestAPIV3Integration:
    """Integration tests that require actual API v3 connection"""

    async def test_live_browse(self):
        """Test actual browsing (requires Tor and valid config)"""
        from api_v3.config import get_api_v3_config_from_env

        config = get_api_v3_config_from_env()
        if not config:
            pytest.skip("No API v3 configuration available")

        service = APIV3BrowseService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )

        try:
            response = await service.list_items()

            assert response.success is True
            assert "data" in response.data
            assert isinstance(response.data["data"], list)

        finally:
            await service.close()

    async def test_live_adapter(self):
        """Test adapter with live connection"""
        from api_v3 import get_api_v3_browse_service
        from api_v3.config import get_api_v3_config_from_env

        config = get_api_v3_config_from_env()
        if not config:
            pytest.skip("No API v3 configuration available")

        service = get_api_v3_browse_service(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )

        adapter = APIV3Adapter(browse_service=service)

        try:
            # Test browse
            result = await adapter.browse_cards({})

            assert result is not None
            assert "data" in result

        finally:
            await service.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
