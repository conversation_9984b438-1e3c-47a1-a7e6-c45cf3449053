import sys
from pathlib import Path

import pytest

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from services.external_api_service import (
    ExternalAPIService,
    APIResponse,
    APIOperation,
)


class DummyConfig:
    base_url = "https://example.test"
    login_token = ""
    session_cookies = {}
    headers = {}


@pytest.mark.asyncio
async def test_add_to_cart_routes_non_numeric_to_v3(monkeypatch):
    service = ExternalAPIService(api_version="v2")

    async def fake_add_to_cart_v3(self, item_id: str) -> APIResponse:
        assert item_id == "hash-id"
        return APIResponse(
            success=True,
            data={"success": True},
            status_code=200,
            operation=APIOperation.ADD_TO_CART,
        )

    async def fail_config(*args, **kwargs):
        raise AssertionError("v2 config should not be used for hashed IDs")

    monkeypatch.setattr(ExternalAPIService, "_add_to_cart_v3", fake_add_to_cart_v3)
    monkeypatch.setattr(service, "_get_api_config", fail_config)

    response = await service.add_to_cart("hash-id")

    assert response.success


@pytest.mark.asyncio
async def test_add_to_cart_numeric_uses_v2(monkeypatch):
    service = ExternalAPIService(api_version="v2")

    async def fake_get_api_config(self):
        return DummyConfig()

    async def fake_make_request(
        self,
        method: str,
        url: str,
        headers,
        cookies,
        operation,
        json_data=None,
        max_retries=3,
        user_id=None,
    ) -> APIResponse:
        return APIResponse(
            success=True,
            data={"success": True},
            status_code=200,
            operation=APIOperation.ADD_TO_CART,
        )

    monkeypatch.setattr(ExternalAPIService, "_get_api_config", fake_get_api_config)
    monkeypatch.setattr(ExternalAPIService, "_make_request", fake_make_request)

    response = await service.add_to_cart(12345)

    assert response.success
