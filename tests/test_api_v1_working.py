#!/usr/bin/env python3
"""
Test if API v1 external service is working to compare with API v2
"""

import asyncio
import logging

from services.external_api_service import get_external_api_service, ListItemsParams

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_api_v1():
    """Test API v1 external service"""
    print("============================================================")
    print("API v1 External Service Test")
    print("============================================================")
    
    try:
        service = get_external_api_service()
        params = ListItemsParams(page=1, limit=3)
        
        print("Making API v1 request...")
        result = await service.list_items(params, user_id="test_user")
        
        if result.success:
            data = result.data
            if isinstance(data, dict):
                cards = data.get("data", [])
                print(f"✅ API v1 working! Found {len(cards)} cards")
                return True
            else:
                print(f"✅ API v1 working! Result: {result}")
                return True
        else:
            print(f"❌ API v1 failed: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_api_v1())
    if success:
        print("🎉 API v1 test PASSED!")
    else:
        print("💥 API v1 test FAILED!")
