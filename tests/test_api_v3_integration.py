"""
Test API v3 Integration

Simple test to verify API v3 is properly integrated with the external API service.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from api_v3.config.api_config import get_api_v3_config_from_env, APIV3Config
from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams
from services.external_api_service import ExternalAPIService, ListItemsParams


async def test_api_v3_config():
    """Test API v3 configuration loading"""
    print("\n=== Testing API v3 Configuration ===")
    
    # Try to load from environment
    config = get_api_v3_config_from_env()
    
    if config:
        print(f"✓ API v3 config loaded from environment")
        print(f"  Base URL: {config.base_url}")
        print(f"  Username: {config.username}")
        print(f"  Use SOCKS Proxy: {config.use_socks_proxy}")
        print(f"  SOCKS URL: {config.socks_url}")
        return True
    else:
        print("✗ API v3 config not found in environment")
        print("  Set EXTERNAL_V3_BASE_URL, EXTERNAL_V3_USERNAME, EXTERNAL_V3_PASSWORD")
        return False


async def test_api_v3_service_direct():
    """Test API v3 service directly"""
    print("\n=== Testing API v3 Service (Direct) ===")
    
    config = get_api_v3_config_from_env()
    if not config:
        print("⊘ Skipping - no configuration available")
        return False
    
    try:
        # Create service
        service = APIV3BrowseService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )
        
        print("✓ API v3 service created")
        
        # Test list_items
        params = APIV3BrowseParams(page=1, limit=5)
        response = await service.list_items(params=params, user_id="test_user")
        
        if response.success:
            print(f"✓ list_items succeeded")
            data = response.data or {}
            cards = data.get("data", [])  # Changed from "cards" to "data"
            print(f"  Retrieved {len(cards)} cards")
            if cards:
                print(f"  First card: {cards[0]}")
            return True
        else:
            print(f"✗ list_items failed: {response.error}")
            return False
    
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            await service.close()
        except:
            pass


async def test_external_api_service_routing():
    """Test external API service routing to API v3"""
    print("\n=== Testing External API Service Routing ===")
    
    # Check if API version is set to v3
    api_version = os.getenv("EXTERNAL_API_VERSION", "v2")
    print(f"Current API version: {api_version}")
    
    if api_version != "v3":
        print("⊘ Skipping - EXTERNAL_API_VERSION is not set to 'v3'")
        print("  Set EXTERNAL_API_VERSION=v3 to test routing")
        return False
    
    try:
        # Create external API service
        service = ExternalAPIService()
        
        print(f"✓ External API service created (version: {service.api_version})")
        
        # Test list_items
        params = ListItemsParams(page=1, limit=5)
        response = await service.list_items(params=params, user_id="test_user")
        
        if response.success:
            print(f"✓ list_items succeeded via routing")
            data = response.data or {}
            cards = data.get("data", [])  # Changed from "cards" to "data"
            print(f"  Retrieved {len(cards)} cards")
            return True
        else:
            print(f"✗ list_items failed: {response.error}")
            return False
    
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            await service.close()
        except:
            pass


async def test_api_switching():
    """Test switching between API versions"""
    print("\n=== Testing API Version Switching ===")
    
    original_version = os.getenv("EXTERNAL_API_VERSION", "v2")
    print(f"Original API version: {original_version}")
    
    # Test with v2
    os.environ["EXTERNAL_API_VERSION"] = "v2"
    service_v2 = ExternalAPIService()
    print(f"✓ Created service with API v2 (actual: {service_v2.api_version})")
    await service_v2.close()
    
    # Test with v3
    os.environ["EXTERNAL_API_VERSION"] = "v3"
    service_v3 = ExternalAPIService()
    print(f"✓ Created service with API v3 (actual: {service_v3.api_version})")
    await service_v3.close()
    
    # Restore original
    os.environ["EXTERNAL_API_VERSION"] = original_version
    
    print("✓ API version switching works")
    return True


async def main():
    """Run all tests"""
    print("=" * 60)
    print("API v3 Integration Tests")
    print("=" * 60)
    
    results = []
    
    # Test 1: Configuration
    results.append(("Configuration", await test_api_v3_config()))
    
    # Test 2: Direct service
    results.append(("Direct Service", await test_api_v3_service_direct()))
    
    # Test 3: Routing
    results.append(("Service Routing", await test_external_api_service_routing()))
    
    # Test 4: Switching
    results.append(("API Switching", await test_api_switching()))
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    
    for name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:10} {name}")
    
    passed = sum(1 for _, r in results if r)
    total = len(results)
    print(f"\nPassed: {passed}/{total}")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

