"""
API v3 Integration Tests

Tests for API v3 browse service and adapter functionality.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from api_v3.services.browse_service import (
    APIV3BrowseService,
    APIV3BrowseParams,
    APIV3BrowseResponse,
)
from api_v3.adapter import APIV3Adapter
from api_v3.models.card_model import APIV3Card, APIV3CardList


@pytest.fixture
def sample_list_response():
    """Sample list response from API v3"""
    return {
        "payload": {"_token": "test_token"},
        "headers": [
            "",
            "BIN",
            "Expiry",
            "Base",
            "F. Name",
            "Country/Ethnicity/Continent",
            "Scheme/Type/Level",
            "Address/Phone/DOB",
            "Price",
        ],
        "rows": [
            [
                {
                    "text": "checkbox",
                    "input_type": "checkbox",
                    "input_name": "checked[]",
                    "input_value": "test_card_id_1",
                    "input_checked": False,
                },
                {"text": "555426"},
                {"text": "10/25"},
                {"text": "TEST-BASE-1"},
                {"text": "John D."},
                {"text": "UNITED STATES,North America"},
                {"text": "MASTERCARD DEBIT PREPAID"},
                {"text": "123 Main St, City, 12345, StatePhone : +1..."},
                {"text": "8.5$"},
            ],
            [
                {
                    "text": "checkbox",
                    "input_type": "checkbox",
                    "input_name": "checked[]",
                    "input_value": "test_card_id_2",
                    "input_checked": False,
                },
                {"text": "411111"},
                {"text": "12/26"},
                {"text": "TEST-BASE-2"},
                {"text": "Jane S."},
                {"text": "CANADA,North America"},
                {"text": "VISA CREDIT PLATINUM"},
                {"text": "No addressPhone : +1...DOB: YES"},
                {"text": "12.5$"},
            ],
        ],
    }


@pytest.fixture
def sample_filter_response():
    """Sample filter response from API v3"""
    return [
        {
            "name": "continent[]",
            "options": [
                {"label": "Africa", "value": "Africa", "selected": False},
                {"label": "Asia", "value": "Asia", "selected": False},
                {"label": "North America", "value": "North America", "selected": False},
            ],
        },
        {
            "name": "country[]",
            "options": [
                {"label": "UNITED STATES", "value": "UNITED STATES", "selected": False},
                {"label": "CANADA", "value": "CANADA", "selected": False},
            ],
        },
        {
            "name": "scheme[]",
            "options": [
                {"label": "VISA", "value": "VISA", "selected": False},
                {"label": "MASTERCARD", "value": "MASTERCARD", "selected": False},
            ],
        },
    ]


class TestAPIV3Card:
    """Tests for APIV3Card model"""

    def test_card_from_row(self, sample_list_response):
        """Test creating card from table row"""
        headers = sample_list_response["headers"]
        row = sample_list_response["rows"][0]

        card = APIV3Card.from_row(row, headers)

        assert card.id == "test_card_id_1"
        assert card.bin == "555426"
        assert card.expiry == "10/25"
        assert card.f_name == "John D."
        assert card.country == "UNITED STATES"
        assert card.continent == "North America"
        assert card.scheme == "MASTERCARD"
        assert card.card_type == "DEBIT"

    def test_card_parsing(self):
        """Test card field parsing"""
        card = APIV3Card(
            _id="test_id",
            bin="555426",
            country="UNITED STATES",
            continent="North America",
            scheme_type_level="MASTERCARD DEBIT PREPAID",
            address_phone_dob="123 Main St, City, 12345Phone : +1234567890DOB: YES",
            price="8.5",
        )

        assert card.country == "UNITED STATES"
        assert card.continent == "North America"
        assert card.scheme == "MASTERCARD"
        assert card.card_type == "DEBIT"
        assert card.phone == "+1234567890"
        assert card.dob == "YES"
        assert card.price == "8.5"

    def test_card_to_dict(self, sample_list_response):
        """Test converting card to dictionary"""
        headers = sample_list_response["headers"]
        row = sample_list_response["rows"][0]

        card = APIV3Card.from_table_row(row, headers)
        card_dict = card.to_dict()

        assert card_dict["id"] == "test_card_id_1"
        assert card_dict["bin"] == "555426"
        assert card_dict["country"] == "UNITED STATES"
        assert card_dict["scheme"] == "MASTERCARD"


class TestAPIV3CardList:
    """Tests for APIV3CardList model"""

    def test_card_list_from_response(self, sample_list_response):
        """Test creating card list from response"""
        card_list = APIV3CardList.from_table_data(sample_list_response)

        assert len(card_list.cards) == 2
        assert card_list.total_count == 2
        assert card_list.cards[0].id == "test_card_id_1"
        assert card_list.cards[1].id == "test_card_id_2"

    def test_card_list_to_dict(self, sample_list_response):
        """Test converting card list to dictionary"""
        card_list = APIV3CardList.from_table_data(sample_list_response)
        list_dict = card_list.to_dict()

        assert len(list_dict["data"]) == 2
        assert list_dict["totalCount"] == 2
        assert list_dict["data"][0]["id"] == "test_card_id_1"


class TestAPIV3BrowseParams:
    """Tests for APIV3BrowseParams"""

    def test_params_to_form_data(self):
        """Test converting params to form data"""
        params = APIV3BrowseParams(
            bins="555426",
            country=["UNITED STATES"],
            scheme=["MASTERCARD"],
            with_phone="true",
        )

        form_data = params.to_form_data()

        assert form_data["bins"] == "555426"
        assert form_data["country[]"] == "UNITED STATES"
        assert form_data["scheme[]"] == "MASTERCARD"
        assert form_data["with_phone"] == "true"

    def test_params_to_query_params(self):
        """Test converting params to query parameters"""
        params = APIV3BrowseParams(
            bins="555426", country=["UNITED STATES", "CANADA"], scheme=["MASTERCARD"]
        )

        query_params = params.to_query_params()

        assert query_params["bins"] == "555426"
        assert query_params["country[]"] == ["UNITED STATES", "CANADA"]
        assert query_params["scheme[]"] == ["MASTERCARD"]


@pytest.mark.asyncio
class TestAPIV3BrowseService:
    """Tests for APIV3BrowseService"""

    @patch("api_v3.services.browse_service.api_registry")
    async def test_list_items_success(self, mock_registry, sample_list_response):
        """Test successful list items request"""
        # Setup mock client
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(return_value=sample_list_response)
        mock_registry.get_client.return_value = mock_client

        # Create service
        service = APIV3BrowseService(registry=mock_registry)

        # Make request
        params = APIV3BrowseParams(bins="555426")
        response = await service.list_items(params, user_id="test_user")

        assert response.success is True
        assert "data" in response.data
        assert len(response.data["data"]) == 2

    @patch("api_v3.services.browse_service.api_registry")
    async def test_get_filters_success(self, mock_registry, sample_filter_response):
        """Test successful get filters request"""
        # Setup mock client
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(return_value=sample_filter_response)
        mock_registry.get_client.return_value = mock_client

        # Create service
        service = APIV3BrowseService(registry=mock_registry)

        # Make request
        response = await service.get_filters(user_id="test_user")

        assert response.success is True
        assert "filters" in response.data
        assert len(response.data["filters"]) == 3


@pytest.mark.asyncio
class TestAPIV3Adapter:
    """Tests for APIV3Adapter"""

    @patch("api_v3.adapter.get_api_v3_browse_service")
    async def test_browse_cards(self, mock_get_service, sample_list_response):
        """Test browsing cards through adapter"""
        # Setup mock service
        mock_service = AsyncMock()

        # Create processed response data (what the browse service would return after conversion)
        processed_data = {
            "data": [
                {
                    "_id": "test_card_id_1",
                    "bin": "555426",
                    "expiry": "10/25",
                    "country": "UNITED STATES",
                    "brand": "MASTERCARD",
                    "type": "DEBIT",
                    "price": "8.5",
                },
                {
                    "_id": "test_card_id_2",
                    "bin": "411111",
                    "expiry": "12/26",
                    "country": "CANADA",
                    "brand": "VISA",
                    "type": "CREDIT",
                    "price": "12.0",
                },
            ],
            "totalCount": 2,
            "page": 1,
            "limit": 50,
        }

        mock_response = APIV3BrowseResponse(success=True, data=processed_data)
        mock_service.list_items = AsyncMock(return_value=mock_response)
        mock_get_service.return_value = mock_service

        # Create adapter
        adapter = APIV3Adapter(browse_service=mock_service)

        # Browse cards
        result = await adapter.browse_cards(
            filters={"country": "UNITED STATES", "brand": "MASTERCARD"},
            user_id="test_user",
        )

        assert result["success"] is True
        assert len(result["data"]) == 2
        assert result["data"][0]["_id"] == "test_card_id_1"

    @patch("api_v3.adapter.get_api_v3_browse_service")
    async def test_get_filters(self, mock_get_service, sample_filter_response):
        """Test getting filters through adapter"""
        # Setup mock service
        mock_service = AsyncMock()
        mock_response = APIV3BrowseResponse(
            success=True, data={"filters": sample_filter_response}
        )
        mock_service.get_filters = AsyncMock(return_value=mock_response)
        mock_get_service.return_value = mock_service

        # Create adapter
        adapter = APIV3Adapter(browse_service=mock_service)

        # Get filters
        result = await adapter.get_filters(user_id="test_user")

        assert result["success"] is True
        assert len(result["filters"]) > 0

    def test_convert_filters_to_params(self):
        """Test filter conversion"""
        adapter = APIV3Adapter()

        filters = {
            "country": "UNITED STATES",
            "brand": "MASTERCARD",
            "bin": "555426",
            "phone": True,
        }

        params = adapter._convert_filters_to_params(filters)

        assert params.country == ["UNITED STATES"]
        assert params.scheme == ["MASTERCARD"]
        assert params.bins == "555426"
        assert params.with_phone == "true"
