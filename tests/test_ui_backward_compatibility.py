"""
Backward compatibility tests for enhanced card catalogue UI
Ensures that existing functionality still works after enhancements
"""

import pytest
from unittest.mock import Mock, AsyncMock
from typing import Dict, List, Any

from utils.product_display import ProductDisplayFormatter
from utils.enhanced_keyboards import <PERSON>hancedKeyboardBuilder
from utils.ui_components import <PERSON><PERSON><PERSON><PERSON><PERSON>, DataFormatter
from handlers.catalog_handlers import CatalogHandlers


class TestBackwardCompatibility:
    """Test that enhanced UI maintains backward compatibility"""
    
    def setup_method(self):
        self.formatter = ProductDisplayFormatter()
        self.legacy_card = {
            "_id": "legacy123",
            "bin": "424242",
            "bank": "Legacy Bank",
            "country": "US",
            "price": "5.99"
        }
    
    def test_legacy_card_formatting_still_works(self):
        """Test that legacy card data still formats correctly"""
        # Test compact format (original method signature)
        result = self.formatter.format_compact_card(self.legacy_card)
        
        # Should still contain basic information
        assert "424242" in result
        assert "Legacy Bank" in result
        assert "US" in result
        
        # Should not crash on missing fields
        assert result is not None
        assert len(result) > 0
    
    def test_legacy_detailed_formatting_still_works(self):
        """Test that legacy detailed formatting still works"""
        result = self.formatter.format_detailed_card(self.legacy_card)
        
        # Should contain card information
        assert "424242" in result
        assert "Legacy Bank" in result
        
        # Should handle missing fields gracefully
        assert result is not None
        assert len(result) > 0
    
    def test_original_card_grid_method_works(self):
        """Test that original format_card_grid method still works"""
        cards = [self.legacy_card]
        
        # Original method signature
        result = self.formatter.format_card_grid(cards, "compact", 5)
        
        assert result is not None
        assert "424242" in result
        assert "Legacy Bank" in result
    
    def test_original_cards_with_filters_method_works(self):
        """Test that original format_cards_with_filters method still works"""
        cards = [self.legacy_card]
        filters = {"country": "US"}
        
        # Original method signature
        result = self.formatter.format_cards_with_filters(
            cards, filters, page=1, total_count=1, display_mode="compact"
        )
        
        assert result is not None
        assert "Cards Catalog" in result
        assert "424242" in result
    
    def test_original_keyboard_creation_works(self):
        """Test that original keyboard creation still works"""
        cards = [self.legacy_card]
        
        # Original method signature
        keyboard = self.formatter.create_enhanced_card_keyboard(
            cards, page=1, total_count=1, has_filters=False
        )
        
        assert keyboard is not None
        assert keyboard.inline_keyboard is not None
        assert len(keyboard.inline_keyboard) > 0
        
        # Should have add to cart button
        buttons_text = [btn.text for row in keyboard.inline_keyboard for btn in row]
        assert any("424242" in text for text in buttons_text)
    
    def test_missing_fields_handled_gracefully(self):
        """Test that missing fields in card data are handled gracefully"""
        minimal_card = {"_id": "minimal123"}
        
        # Should not crash with minimal data
        compact_result = self.formatter.format_compact_card(minimal_card)
        detailed_result = self.formatter.format_detailed_card(minimal_card)
        
        assert compact_result is not None
        assert detailed_result is not None
        assert "minimal123" in compact_result or "Unknown" in compact_result
    
    def test_legacy_status_indicators_work(self):
        """Test that legacy status indicator methods still work"""
        # Original method signatures
        api_status = StatusIndicator.format_api_status("active", "Test API")
        health_status = StatusIndicator.format_health_status(True, "All systems operational")
        
        assert "Test API" in api_status
        assert "active" in api_status.lower()
        assert "Healthy" in health_status
        assert "All systems operational" in health_status
    
    def test_legacy_data_formatter_methods_work(self):
        """Test that legacy data formatter methods still work"""
        # Original method signatures
        currency = DataFormatter.format_currency(5.99, "USD")
        large_number = DataFormatter.format_large_number(1500)
        
        assert "$5.99" in currency
        assert "1.5K" in large_number
    
    def test_enhanced_keyboard_builder_backward_compatibility(self):
        """Test that enhanced keyboard builder maintains original functionality"""
        builder = EnhancedKeyboardBuilder()
        
        # Original method usage
        keyboard = (builder
                   .add_button("Test Button", "test:callback")
                   .add_navigation_row()
                   .build())
        
        assert keyboard is not None
        assert len(keyboard.inline_keyboard) >= 2  # Button row + navigation row
        
        # Check button text
        buttons_text = [btn.text for row in keyboard.inline_keyboard for btn in row]
        assert "Test Button" in buttons_text
        assert any("Back" in text for text in buttons_text)
    
    def test_empty_data_handling(self):
        """Test that empty data is handled gracefully"""
        # Empty card list
        empty_result = self.formatter.format_card_grid([], "compact", 5)
        assert "No Cards Available" in empty_result
        
        # Empty filters
        result_with_empty_filters = self.formatter.format_cards_with_filters(
            [self.legacy_card], {}, page=1, total_count=1
        )
        assert result_with_empty_filters is not None
        assert "424242" in result_with_empty_filters
    
    def test_none_values_handling(self):
        """Test that None values in card data are handled properly"""
        card_with_nones = {
            "_id": "test123",
            "bin": "424242",
            "bank": None,
            "country": None,
            "price": None
        }
        
        # Should not crash with None values
        result = self.formatter.format_compact_card(card_with_nones)
        assert result is not None
        assert "424242" in result
    
    def test_original_method_signatures_preserved(self):
        """Test that original method signatures are preserved"""
        import inspect
        
        # Check that original methods exist with expected signatures
        compact_sig = inspect.signature(self.formatter.format_compact_card)
        detailed_sig = inspect.signature(self.formatter.format_detailed_card)
        grid_sig = inspect.signature(self.formatter.format_card_grid)
        
        # Should have expected parameters
        assert 'card' in compact_sig.parameters
        assert 'index' in compact_sig.parameters
        
        assert 'card' in detailed_sig.parameters
        assert 'index' in detailed_sig.parameters
        
        assert 'cards' in grid_sig.parameters
        assert 'display_mode' in grid_sig.parameters
        assert 'max_per_page' in grid_sig.parameters
    
    def test_existing_constants_preserved(self):
        """Test that existing constants are still available"""
        # Check that original constants exist
        assert hasattr(self.formatter, 'SECTION_SEPARATOR')
        assert hasattr(self.formatter, 'ITEM_SEPARATOR')
        assert hasattr(self.formatter, 'FIELD_EMOJIS')
        assert hasattr(self.formatter, 'FIELD_LABELS')
        
        # Check values are reasonable
        assert len(self.formatter.SECTION_SEPARATOR) > 0
        assert len(self.formatter.FIELD_EMOJIS) > 0
    
    def test_catalog_handlers_integration(self):
        """Test that catalog handlers can still use enhanced components"""
        # This would test integration with existing handlers
        # Mock the necessary dependencies
        
        handlers = CatalogHandlers()
        
        # Test that handlers can access the formatter
        assert hasattr(handlers, '_render_cards_page')
        
        # The method should exist and be callable
        assert callable(getattr(handlers, '_render_cards_page', None))


class TestPerformanceRegression:
    """Test that enhancements don't significantly impact performance"""
    
    def setup_method(self):
        self.formatter = ProductDisplayFormatter()
        
        # Create test data
        self.large_card_set = []
        for i in range(100):
            self.large_card_set.append({
                "_id": f"card_{i}",
                "bin": f"42424{i:03d}",
                "bank": f"Bank {i}",
                "country": "US",
                "price": f"{i % 20}.99"
            })
    
    def test_large_card_set_performance(self):
        """Test that large card sets are handled efficiently"""
        import time
        
        start_time = time.time()
        
        # Format a large set of cards
        result = self.formatter.format_card_grid(
            self.large_card_set[:50], "compact", 50
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        assert processing_time < 1.0  # Less than 1 second
        assert result is not None
        assert len(result) > 0
    
    def test_keyboard_creation_performance(self):
        """Test that keyboard creation is still efficient"""
        import time
        
        start_time = time.time()
        
        # Create keyboard for many cards
        keyboard = self.formatter.create_enhanced_card_keyboard(
            self.large_card_set[:20], page=1, total_count=100
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should complete quickly
        assert processing_time < 0.5  # Less than 0.5 seconds
        assert keyboard is not None
        assert len(keyboard.inline_keyboard) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
