"""
Comprehensive tests for the enhanced card catalogue UI components
Tests visual design improvements, responsive features, and user experience enhancements
"""

import sys
from pathlib import Path

import pytest
from unittest.mock import Mock, patch
from typing import Dict, List, Any

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from utils.product_display import ProductDisplayFormatter
from utils.enhanced_keyboards import SmartKeyboardLayouts, EnhancedKeyboardBuilder
from utils.ui_components import StatusIndicator, DataFormatter, UITheme
from utils.ui_manager import UIManager, LoadingState


class TestEnhancedCardDisplay:
    """Test enhanced card display formatting with new improvements"""

    def setup_method(self):
        self.formatter = ProductDisplayFormatter()
        self.sample_card = {
            "_id": "test123",
            "bin": "424242",
            "bank": "Test Bank",
            "brand": "VISA",
            "country": "US",
            "type": "CREDIT",
            "level": "PLATINUM",
            "price": "5.99",
            "expiry": "12/25",
            "quality": "HIGH",
            "refundable": True,
            "address": "123 Test St",
            "phone": "555-0123",
            # Fields that should be filtered out
            "discount_code": "SAVE20",
            "contact_email": "<EMAIL>",
            "support_phone": "1-800-SUPPORT",
            "promo_offer": "Special deal",
            "internal_id": "abc123xyz789"
        }
    
    def test_enhanced_compact_card_formatting(self):
        """Test enhanced compact card display with improved visual hierarchy"""
        result = self.formatter.format_compact_card(self.sample_card, 1)

        # Check for enhanced modern format structure
        assert "💳" in result and "424242" in result  # Card emoji and BIN
        assert "Exp:" in result  # Expiry information
        assert "🏦" in result  # Bank emoji
        assert "📍" in result  # Location emoji
        assert "✅" in result  # Verification emoji
        assert "💰" in result  # Price emoji
        assert "♻️" in result  # Refundable emoji

        # Check for enhanced styling
        assert "<b>" in result  # Bold formatting
        
    def test_enhanced_detailed_card_formatting(self):
        """Test enhanced detailed card display with visual card styling"""
        result = self.formatter.format_detailed_card(self.sample_card, 1)
        
        # Check for visual card border
        assert "╭─" in result  # Top border
        assert "╰─" in result  # Bottom border
        assert "│" in result   # Side borders
        
        # Check for modern layout content within borders
        assert "💳" in result  # Card emoji
        assert "🏦" in result  # Bank emoji
        assert "📍" in result  # Location emoji
        assert "✅" in result or "💎" in result  # Verification or quality emoji
        
    def test_enhanced_price_display(self):
        """Test enhanced price display with categorization"""
        # Test free card
        free_card = {**self.sample_card, "price": "0"}
        result = self.formatter._create_enhanced_price_display("0")
        assert "🎁" in result[0] and "FREE" in result[0]
        
        # Test budget card
        result = self.formatter._create_enhanced_price_display("0.99")
        assert "💸" in result[0] and "Budget" in result[0]
        
        # Test standard card
        result = self.formatter._create_enhanced_price_display("5.99")
        assert "💰" in result[0] and "Standard" in result[0]
        
        # Test premium card
        result = self.formatter._create_enhanced_price_display("15.99")
        assert "💎" in result[0] and "Premium" in result[0]
    
    def test_responsive_card_grid(self):
        """Test responsive card grid formatting"""
        cards = [self.sample_card] * 3
        
        # Test mobile formatting
        mobile_result = self.formatter.format_responsive_card_grid(
            cards, "compact", 5, "mobile"
        )
        assert len(mobile_result.split("┄" * 35)) <= 4  # Max 3 cards + 1 for split
        
        # Test desktop formatting
        desktop_result = self.formatter.format_responsive_card_grid(
            cards, "compact", 5, "desktop"
        )
        assert "—" * 25 in desktop_result  # Desktop separator
    
    def test_empty_state_messages(self):
        """Test enhanced empty state messages"""
        mobile_empty = self.formatter._get_responsive_empty_message("mobile")
        desktop_empty = self.formatter._get_responsive_empty_message("desktop")

        assert "📭" in mobile_empty
        assert "📭" in desktop_empty
        assert len(mobile_empty) < len(desktop_empty)  # Mobile should be shorter

    def test_text_wrapping_optimization(self):
        """Test intelligent text wrapping for different device types"""
        long_text = "This is a very long line of text that should be wrapped intelligently based on device type and maximum line length settings for optimal readability"

        # Test mobile wrapping (60 chars max)
        mobile_wrapped = self.formatter._wrap_text_intelligently(long_text, 60, "mobile")
        assert len(mobile_wrapped) > 1  # Should be wrapped into multiple lines
        assert all(len(line) <= 60 for line in mobile_wrapped)

        # Test desktop (no wrapping by default)
        desktop_wrapped = self.formatter._wrap_text_intelligently(long_text, 80, "desktop")
        assert len(desktop_wrapped) == 1  # Should not wrap on desktop

        # Test tablet wrapping (70 chars max)
        tablet_wrapped = self.formatter._wrap_text_intelligently(long_text, 70, "tablet")
        assert len(tablet_wrapped) > 1  # Should be wrapped
        assert all(len(line) <= 70 for line in tablet_wrapped)

    def test_field_filtering(self):
        """Test selective field filtering functionality"""
        # Test that filtered fields are excluded
        result = self.formatter.format_compact_card(self.sample_card, device_type="mobile")

        # Should not contain filtered fields
        assert "discount_code" not in result
        assert "SAVE20" not in result
        assert "<EMAIL>" not in result
        assert "1-800-SUPPORT" not in result
        assert "Special deal" not in result
        assert "abc123xyz789" not in result

        # Should still contain valid fields
        assert "424242" in result
        assert "Test Bank" in result
        assert "CREDIT" in result

    def test_strategic_detail_line(self):
        """Test strategic additional detail line functionality"""
        result = self.formatter.format_compact_card(self.sample_card, device_type="mobile")

        # Should contain strategic detail line with type and level
        assert "📋" in result  # Card type/level indicator
        assert "CREDIT" in result
        assert "PLATINUM" in result

        # Test detailed view also has strategic line
        detailed_result = self.formatter.format_detailed_card(self.sample_card, device_type="mobile")
        assert "📋" in detailed_result

    def test_device_responsive_formatting(self):
        """Test device-specific responsive formatting"""
        # Test mobile formatting
        mobile_result = self.formatter.format_compact_card(self.sample_card, device_type="mobile")
        mobile_lines = mobile_result.split('\n')

        # Test tablet formatting
        tablet_result = self.formatter.format_compact_card(self.sample_card, device_type="tablet")

        # Test desktop formatting
        desktop_result = self.formatter.format_compact_card(self.sample_card, device_type="desktop")

        # Mobile should have more line breaks due to wrapping
        assert len(mobile_lines) >= len(tablet_result.split('\n'))

        # All should contain core information
        for result in [mobile_result, tablet_result, desktop_result]:
            assert "424242" in result
            assert "Test Bank" in result


class TestEnhancedKeyboards:
    """Test enhanced keyboard layouts and functionality"""
    
    def test_enhanced_card_keyboard(self):
        """Test enhanced card keyboard with better organization"""
        formatter = ProductDisplayFormatter()
        cards = [
            {"_id": "1", "bin": "424242", "price": "0"},      # Free
            {"_id": "2", "bin": "555555", "price": "5.99"},   # Standard
            {"_id": "3", "bin": "666666", "price": "15.99"},  # Premium
        ]
        
        keyboard = formatter.create_enhanced_card_keyboard(
            cards, page=1, total_count=10, has_filters=True
        )
        
        # Check for enhanced quick actions
        buttons_text = [btn.text for row in keyboard.inline_keyboard for btn in row]
        
        # Should have quick actions
        assert any("🛒+ Add All" in text for text in buttons_text)
        assert any("🔄 Refresh" in text for text in buttons_text)
        assert any("📋 Details" in text for text in buttons_text)
        
        # Should have price indicators
        assert any("🎁" in text for text in buttons_text)  # Free card
        assert any("💰" in text for text in buttons_text)  # Standard card
        assert any("💎" in text for text in buttons_text)  # Premium card
    
    def test_responsive_keyboard(self):
        """Test responsive keyboard layouts"""
        formatter = ProductDisplayFormatter()
        cards = [{"_id": "1", "bin": "424242", "price": "5.99"}]
        
        # Test mobile keyboard
        mobile_kb = formatter.create_responsive_card_keyboard(
            cards, device_type="mobile"
        )
        mobile_buttons = [btn.text for row in mobile_kb.inline_keyboard for btn in row]
        
        # Mobile should have shorter button text
        assert any("🔄" in text and len(text) < 15 for text in mobile_buttons)
        
        # Test desktop keyboard
        desktop_kb = formatter.create_responsive_card_keyboard(
            cards, device_type="desktop"
        )
        desktop_buttons = [btn.text for row in desktop_kb.inline_keyboard for btn in row]
        
        # Desktop should have more detailed button text
        assert any("🔄 Refresh List" in text for text in desktop_buttons)
    
    def test_smart_filter_keyboard(self):
        """Test smart filter keyboard with suggestions"""
        active_filters = {"location": True, "card": False}
        suggested_filters = [
            {"name": "US Cards", "callback": "filter:quick:country:US"},
            {"name": "Premium Only", "callback": "filter:quick:level:PREMIUM"}
        ]
        quick_presets = [
            {"name": "Budget Cards", "callback": "filter:preset:budget", "emoji": "💸"}
        ]
        
        keyboard = SmartKeyboardLayouts.create_smart_filter_keyboard(
            active_filters, suggested_filters, quick_presets
        )
        
        buttons_text = [btn.text for row in keyboard.inline_keyboard for btn in row]
        
        # Check for quick presets
        assert any("💸 Budget Cards" in text for text in buttons_text)
        
        # Check for suggestions
        assert any("🔍 US Cards" in text for text in buttons_text)
        assert any("🔍 Premium Only" in text for text in buttons_text)
        
        # Check for active filter indicators
        assert any("✅" in text and "Location" in text for text in buttons_text)
    
    def test_quick_actions_keyboard(self):
        """Test quick actions keyboard for power users"""
        user_preferences = {
            "frequent_country": "US",
            "preferred_price_range": "$5-$10"
        }
        
        keyboard = SmartKeyboardLayouts.create_quick_actions_keyboard(
            "catalog", user_preferences
        )
        
        buttons_text = [btn.text for row in keyboard.inline_keyboard for btn in row]
        
        # Check for personalized shortcuts
        assert any("🌍 US Cards" in text for text in buttons_text)
        assert any("💰 $5-$10" in text for text in buttons_text)
        
        # Check for power user features
        assert any("📊 Analytics" in text for text in buttons_text)
        assert any("🎯 Smart Recommendations" in text for text in buttons_text)


class TestStatusIndicators:
    """Test enhanced status indicators"""
    
    def test_card_availability_formatting(self):
        """Test card availability status formatting"""
        # High stock
        result = StatusIndicator.format_card_availability(True, "high")
        assert "🔥" in result and "In Stock" in result
        
        # Limited stock
        result = StatusIndicator.format_card_availability(True, "medium")
        assert "⚠️" in result and "Limited Stock" in result
        
        # Few left
        result = StatusIndicator.format_card_availability(True, "low")
        assert "⚡" in result and "Few Left" in result
        
        # Out of stock
        result = StatusIndicator.format_card_availability(False)
        assert "❌" in result and "Out of Stock" in result
    
    def test_price_trend_formatting(self):
        """Test price trend indicator formatting"""
        # Rising price
        result = StatusIndicator.format_price_trend("up", 5.2)
        assert "📈" in result and "Rising" in result and "****%" in result
        
        # Falling price
        result = StatusIndicator.format_price_trend("down", -3.1)
        assert "📉" in result and "Falling" in result and "-3.1%" in result
        
        # Stable price
        result = StatusIndicator.format_price_trend("stable")
        assert "➡️" in result and "Stable" in result
    
    def test_quality_score_formatting(self):
        """Test quality score formatting"""
        # Excellent
        result = StatusIndicator.format_quality_score(9.5, 10.0)
        assert "💎" in result and "Excellent" in result
        
        # Good
        result = StatusIndicator.format_quality_score(7.5, 10.0)
        assert "✅" in result and "Good" in result
        
        # Poor
        result = StatusIndicator.format_quality_score(4.0, 10.0)
        assert "🔴" in result and "Poor" in result
    
    def test_verification_level_formatting(self):
        """Test verification level formatting"""
        verified_fields = ["Address", "Phone", "Email"]
        
        # Full verification
        result = StatusIndicator.format_verification_level("full", verified_fields)
        assert "🔐✨" in result and "Fully Verified" in result
        assert "Address, Phone, Email" in result
        
        # Partial verification with many fields
        many_fields = ["Address", "Phone", "Email", "DOB", "SSN"]
        result = StatusIndicator.format_verification_level("partial", many_fields)
        assert "+3 more" in result  # Should truncate long lists


class TestDataFormatter:
    """Test enhanced data formatting"""
    
    def test_enhanced_currency_formatting(self):
        """Test enhanced currency formatting with visual indicators"""
        # Free
        result = DataFormatter.format_currency(0.0)
        assert "🎁" in result and "FREE" in result
        
        # Budget
        result = DataFormatter.format_currency(0.99)
        assert "💸" in result
        
        # Standard
        result = DataFormatter.format_currency(5.99)
        assert "💰" in result
        
        # Premium
        result = DataFormatter.format_currency(15.99)
        assert "💎" in result
    
    def test_percentage_formatting(self):
        """Test percentage formatting with visual indicators"""
        # Positive
        result = DataFormatter.format_percentage(5.2)
        assert "📈" in result and "****%" in result
        
        # Negative
        result = DataFormatter.format_percentage(-3.1)
        assert "📉" in result and "-3.1%" in result
        
        # Zero
        result = DataFormatter.format_percentage(0.0)
        assert "➡️" in result and "0.0%" in result
    
    def test_analytics_summary_formatting(self):
        """Test analytics summary formatting"""
        data = {
            "total_cards": 15420,
            "avg_price": 8.75,
            "top_country": "US",
            "success_rate": 94.2
        }
        
        result = DataFormatter.format_analytics_summary(data)
        
        assert "📊" in result and "15.4K" in result  # Total cards
        assert "💰" in result and "$8.75" in result  # Avg price
        assert "🌍" in result and "US" in result     # Top country
        assert "✅" in result and "94.2%" in result  # Success rate


class TestUIManager:
    """Test enhanced UI manager functionality"""

    def test_enhanced_loading_states(self):
        """Test enhanced loading states with sophisticated animations"""
        loading_state = LoadingState("Processing cards...", show_progress=True)
        loading_state.update_progress(50, 100)

        # Test progress bar enhancement
        progress_bar = loading_state.get_progress_bar()
        assert "⚡" in progress_bar  # Should show lightning for 50%

        # Test complete state
        loading_state.update_progress(100, 100)
        progress_bar = loading_state.get_progress_bar()
        assert "✅" in progress_bar and "Complete" in progress_bar

    def test_multi_stage_loading(self):
        """Test multi-stage loading functionality"""
        stages = ["Fetching cards...", "Processing data...", "Rendering display..."]
        loading_state = LoadingState("Processing request...", show_progress=True, stages=stages)

        # Test initial stage
        stage_indicator = loading_state.get_stage_indicator()
        assert "Fetching cards..." in stage_indicator
        assert "(1/3)" in stage_indicator

        # Test stage advancement
        loading_state.advance_stage("Processing data...")
        stage_indicator = loading_state.get_stage_indicator()
        assert "Processing data..." in stage_indicator
        assert "(2/3)" in stage_indicator

        # Test final stage
        loading_state.advance_stage("Rendering display...")
        stage_indicator = loading_state.get_stage_indicator()
        assert "Rendering display..." in stage_indicator
        assert "(3/3)" in stage_indicator

    def test_time_estimation(self):
        """Test time estimation functionality"""
        import time

        stages = ["Stage 1", "Stage 2", "Stage 3"]
        loading_state = LoadingState("Test", show_progress=True, stages=stages)

        # Simulate stage completion
        time.sleep(0.1)  # Small delay to simulate work
        loading_state.advance_stage()

        # Should have some stage duration recorded
        assert len(loading_state.stage_durations) == 1
        assert loading_state.stage_durations[0] > 0

        # Test estimation (should return a value after first stage)
        estimated = loading_state.estimate_remaining_time()
        assert estimated is None or estimated >= 0  # Should be None or positive

    def test_enhanced_progress_bar_styling(self):
        """Test enhanced progress bar with different styling levels"""
        loading_state = LoadingState("Test", show_progress=True)

        # Test different progress levels
        test_cases = [
            (10, "⏳"),   # Low progress
            (30, "🔄"),   # Medium-low progress
            (60, "⚡"),   # Medium progress
            (80, "🔥"),   # High progress
            (95, "🎯"),   # Very high progress
            (100, "✅")   # Complete
        ]

        for progress, expected_indicator in test_cases:
            loading_state.update_progress(progress, 100)
            progress_bar = loading_state.get_progress_bar()
            assert expected_indicator in progress_bar

    def test_loading_message_formatting(self):
        """Test enhanced loading message formatting with stages"""
        stages = ["Processing...", "Finalizing..."]
        loading_state = LoadingState("Loading cards...", show_progress=True, stages=stages)

        # Test without mocking - just check basic functionality
        message = loading_state.format_message()
        assert "<b>Loading cards...</b>" in message
        assert any(indicator in message for indicator in ["⏳", "⏰", "⏱️"])

        # Test with progress and stages
        loading_state.update_progress(50, 100)
        message_with_progress = loading_state.format_message()
        assert "Processing..." in message_with_progress  # Should show current stage
        assert "⚡" in message_with_progress or "Loading cards..." in message_with_progress

    def test_create_multi_stage_loader(self):
        """Test multi-stage loader creation"""
        # Test creating multi-stage loader directly
        stages = ["Connecting...", "Fetching...", "Processing..."]

        loader = LoadingState("Processing request...", show_progress=True, stages=stages)

        assert loader.stages == stages
        assert loader.show_progress == True
        assert loader.message == "Processing request..."


class TestNewEnhancements:
    """Test the latest specific enhancements to the card catalogue UI"""

    def setup_method(self):
        self.formatter = ProductDisplayFormatter()
        self.test_card = {
            "_id": "enhanced_test",
            "bin": "555555",
            "bank": "Enhanced Bank",
            "brand": "MASTERCARD",
            "country": "CA",
            "type": "DEBIT",
            "level": "GOLD",
            "price": "12.99",
            "quality": "PREMIUM",
            "stock": "15",
            # Filtered fields
            "discount_rate": "10%",
            "admin_email": "<EMAIL>",
            "support_contact": "1-800-HELP",
            "internal_token": "xyz789abc123def456"
        }

    def test_comprehensive_field_filtering(self):
        """Test comprehensive field filtering with all blacklist categories"""
        # Test that all types of filtered fields are excluded
        result = self.formatter.format_compact_card(self.test_card, device_type="mobile")

        # Should not contain any filtered content
        filtered_content = [
            "discount_rate", "10%", "admin_email", "<EMAIL>",
            "support_contact", "1-800-HELP", "internal_token", "xyz789abc123def456"
        ]

        for content in filtered_content:
            assert content not in result, f"Filtered content '{content}' found in result"

        # Should contain valid content
        valid_content = ["555555", "Enhanced Bank", "DEBIT", "GOLD"]
        for content in valid_content:
            assert content in result, f"Valid content '{content}' not found in result"

    def test_configurable_field_filtering(self):
        """Test configurable field filtering"""
        # Add custom filtering rules
        self.formatter.configure_field_filtering(
            additional_keywords=["custom", "special"],
            additional_patterns={"test_patterns": ["test_", "demo_"]}
        )

        # Test that custom patterns are applied
        assert "custom" in self.formatter.field_blacklist["keywords"]
        assert "test_patterns" in self.formatter.field_blacklist

        # Test filtering with custom patterns
        test_card_with_custom = {
            **self.test_card,
            "custom_field": "should be filtered",
            "test_value": "should also be filtered"
        }

        result = self.formatter.format_compact_card(test_card_with_custom, device_type="mobile")
        assert "custom_field" not in result
        assert "test_value" not in result

    def test_line_length_optimization_edge_cases(self):
        """Test line length optimization with edge cases"""
        # Test very long field values
        long_card = {
            **self.test_card,
            "bank": "This is an extremely long bank name that should be wrapped intelligently based on device settings"
        }

        mobile_result = self.formatter.format_compact_card(long_card, device_type="mobile")
        mobile_lines = mobile_result.split('\n')

        # Check that no line exceeds mobile limit (with some tolerance for formatting)
        for line in mobile_lines:
            # Remove HTML tags for length calculation
            clean_line = line.replace('<b>', '').replace('</b>', '').replace('<i>', '').replace('</i>', '')
            assert len(clean_line) <= 80, f"Line too long: {clean_line}"

    def test_strategic_detail_line_priority(self):
        """Test strategic detail line priority logic"""
        # Test with type and level (highest priority)
        result1 = self.formatter._create_strategic_detail_line(self.test_card, {}, set())
        assert "📋" in result1
        assert "DEBIT" in result1
        assert "GOLD" in result1

        # Test without type/level but with verification
        card_no_type = {k: v for k, v in self.test_card.items() if k not in ["type", "level"]}
        card_no_type["address"] = "123 Test St"  # Add verified field

        result2 = self.formatter._create_strategic_detail_line(card_no_type, {}, set())
        # Should show verification status since no type/level
        assert "🔐" in result2 or "🔒" in result2 or "📦" in result2  # Verification or availability

        # Test with stock availability
        stock_card = {"stock": "5"}
        result3 = self.formatter._create_strategic_detail_line(stock_card, {}, set())
        assert "📦" in result3
        assert "Limited Stock" in result3

    def test_enhanced_visual_borders(self):
        """Test enhanced visual borders and separators"""
        detailed_result = self.formatter.format_detailed_card(self.test_card, device_type="desktop")

        # Should contain enhanced border elements
        assert "╭─" in detailed_result  # Top corner
        assert "╰─" in detailed_result  # Bottom corner
        assert "┈┈┈" in detailed_result  # Section break

        # Should have proper card structure
        lines = detailed_result.split('\n')
        assert lines[0].startswith("╭")  # Starts with top border
        assert lines[-1].startswith("╰")  # Ends with bottom border

    def test_backward_compatibility_with_new_parameters(self):
        """Test that new device_type parameter maintains backward compatibility"""
        # Test that methods work without device_type parameter (should default to mobile)
        compact_result = self.formatter.format_compact_card(self.test_card)
        detailed_result = self.formatter.format_detailed_card(self.test_card)

        assert compact_result is not None
        assert detailed_result is not None
        assert "555555" in compact_result
        assert "555555" in detailed_result

        # Test with explicit device_type
        mobile_result = self.formatter.format_compact_card(self.test_card, device_type="mobile")
        assert mobile_result is not None
        assert "555555" in mobile_result

    def test_modern_emoji_based_layout(self):
        """Test the new modern emoji-based card layout format"""
        test_card = {
            "bin": "497856",
            "expiry": "09/25",
            "bank": "CNCE",
            "type": "CREDIT",
            "level": "CLASSIC",
            "address": "123 Test St",
            "ip": "***********",
            "email": "<EMAIL>",
            "phone": "555-1234",
            "quality": "Premium",
            "price": "7.99",
            "refundable": "Yes",
            "city": "Albert",
            "country": "FR",
            "zip": "80300"
        }

        # Test compact format
        compact_result = self.formatter.format_compact_card(test_card, index=1, device_type="desktop")

        # Verify enhanced emoji-based format structure
        assert "💳" in compact_result  # Card emoji
        assert "🏦" in compact_result  # Bank emoji
        assert "📋" in compact_result  # Type emoji
        assert "📍" in compact_result  # Location emoji
        assert "✅" in compact_result  # Verification emoji
        assert "💰" in compact_result  # Price emoji
        assert "♻️" in compact_result  # Refund emoji

        # Verify specific content
        assert "497856" in compact_result
        assert "09/25" in compact_result
        assert "CNCE" in compact_result
        assert "CREDIT" in compact_result and "CLASSIC" in compact_result
        assert "Albert" in compact_result and "FR" in compact_result and "80300" in compact_result
        assert "$7.99" in compact_result

        # Check that verified fields are displayed correctly
        assert ("Addr" in compact_result or "Address" in compact_result)
        assert "IP" in compact_result
        assert "Email" in compact_result
        assert "Phone" in compact_result

        # Test detailed format
        detailed_result = self.formatter.format_detailed_card(test_card, index=1, device_type="desktop")

        # Should contain the same modern layout within borders
        assert "╭─" in detailed_result
        assert "╰─" in detailed_result
        assert "💳" in detailed_result  # Card emoji
        assert "🏦" in detailed_result  # Bank emoji

    def test_enhanced_ui_polish_improvements(self):
        """Test the enhanced UI polish improvements"""
        test_card = {
            "bin": "424242",
            "expiry": "1225",  # Test MMYY format conversion
            "bank": "Test Bank",
            "type": "CREDIT",
            "level": "PLATINUM",
            "address": "123 Test St",
            "phone": "555-1234",
            "quality": "Premium",
            "price": "5.99",
            "refundable": "Yes",
            "country": "US"
        }

        # Test compact format with enhancements
        compact_result = self.formatter.format_compact_card(test_card, index=1, device_type="desktop")

        # Verify BIN and expiry are on same line with proper formatting
        lines = compact_result.split('\n')
        header_line = lines[0]
        assert "💳" in header_line
        assert "424242" in header_line
        assert "Exp: <b>12/25</b>" in header_line  # Should convert MMYY to MM/YY
        assert "•" in header_line  # Should use bullet separator

        # Verify enhanced formatting throughout
        assert "<b>424242</b>" in compact_result  # Bold BIN
        assert "<b>Test Bank</b>" in compact_result  # Bold bank name
        assert "<b>CREDIT</b>" in compact_result  # Bold card type
        assert "<i>(PLATINUM)</i>" in compact_result  # Italic level
        assert "💎 <i>Premium</i>" in compact_result  # Quality with emoji and italic
        assert "💰 <b>$5.99</b>" in compact_result  # Bold price
        assert "♻️ <b>Yes</b>" in compact_result  # Bold refund status

        # Test detailed format with optimized borders
        detailed_result = self.formatter.format_detailed_card(test_card, index=1, device_type="desktop")

        # Verify optimized border sizes (should be shorter than before)
        border_lines = [line for line in detailed_result.split('\n') if '─' in line]
        for border_line in border_lines:
            # Borders should be shorter and more proportional
            assert len(border_line) < 40, f"Border too long: {border_line}"

        # Verify section break is also optimized
        assert "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈" in detailed_result  # Shorter section break

    def test_expiry_format_consistency(self):
        """Test expiry format consistency across different input formats"""
        test_cases = [
            ("1225", "12/25"),      # MMYY format
            ("12/25", "12/25"),     # Already MM/YY
            ("122025", "12/25"),    # MMYYYY format
            ("9/25", "09/25"),      # M/YY format (should pad month)
            ("12/2025", "12/25"),   # MM/YYYY format
        ]

        for input_expiry, expected_output in test_cases:
            test_card = {"bin": "424242", "expiry": input_expiry}
            result = self.formatter.format_compact_card(test_card, device_type="desktop")
            assert f"Exp: <b>{expected_output}</b>" in result, f"Failed for input {input_expiry}, expected {expected_output}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
