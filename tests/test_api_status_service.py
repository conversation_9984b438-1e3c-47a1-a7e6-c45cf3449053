"""
Test API Status Service

Tests the API status monitoring functionality.
"""

import asyncio
import sys
from pathlib import Path
import pytest

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.api_status_service import get_api_status_service, APIStatus


def test_status_tracking():
    """Test basic status tracking"""
    print("=" * 70)
    print("API Status Service Test")
    print("=" * 70)
    
    service = get_api_status_service()
    
    # Test 1: Initial status (should be UNKNOWN)
    print("\n1. Initial Status")
    print("-" * 70)
    status = service.get_status("v3")
    print(f"Initial status: {status.value}")
    assert status == APIStatus.UNKNOWN, "Initial status should be UNKNOWN"
    print("✓ Initial status is UNKNOWN")
    
    # Test 2: Record successes
    print("\n2. Recording Successes")
    print("-" * 70)
    for i in range(5):
        service.record_success("v3", response_time=0.5 + i * 0.1, status_code=200)
        print(f"  Success {i+1} recorded")
    
    status = service.get_status("v3")
    print(f"Status after 5 successes: {status.value}")
    assert status == APIStatus.ONLINE, "Status should be ONLINE after successes"
    print("✓ Status is ONLINE")
    
    # Test 3: Get status message
    print("\n3. Status Message")
    print("-" * 70)
    message = service.get_status_message("v3")
    print(f"Status message: {message}")
    assert "🟢" in message, "Message should contain green emoji"
    assert "Online" in message, "Message should contain 'Online'"
    print("✓ Status message correct")
    
    # Test 4: Get detailed status
    print("\n4. Detailed Status")
    print("-" * 70)
    details = service.get_detailed_status("v3")
    print(f"Total requests: {details['metrics']['total_requests']}")
    print(f"Success rate: {details['metrics']['success_rate']}%")
    print(f"Avg response time: {details['metrics']['average_response_time']}s")
    assert details['metrics']['total_requests'] == 5, "Should have 5 requests"
    assert details['metrics']['success_rate'] == 100.0, "Success rate should be 100%"
    print("✓ Detailed status correct")
    
    # Test 5: Record failures
    print("\n5. Recording Failures")
    print("-" * 70)
    service.record_failure("v3", "Connection timeout", 408)
    service.record_failure("v3", "Connection refused", 502)
    
    status = service.get_status("v3")
    print(f"Status after 2 failures: {status.value}")
    assert status == APIStatus.DEGRADED, "Status should be DEGRADED after 2 consecutive failures"
    print("✓ Status is DEGRADED")
    
    # Test 6: More failures -> OFFLINE
    print("\n6. More Failures -> OFFLINE")
    print("-" * 70)
    service.record_failure("v3", "Connection refused", 502)
    
    status = service.get_status("v3")
    print(f"Status after 3 failures: {status.value}")
    assert status == APIStatus.OFFLINE, "Status should be OFFLINE after 3 consecutive failures"
    print("✓ Status is OFFLINE")
    
    message = service.get_status_message("v3")
    print(f"Status message: {message}")
    assert "🔴" in message, "Message should contain red emoji"
    assert "Offline" in message, "Message should contain 'Offline'"
    print("✓ Offline message correct")
    
    # Test 7: Recovery
    print("\n7. Recovery")
    print("-" * 70)
    service.record_success("v3", response_time=0.8, status_code=200)
    
    status = service.get_status("v3")
    print(f"Status after success: {status.value}")
    assert status == APIStatus.ONLINE, "Status should be ONLINE after success"
    print("✓ Status recovered to ONLINE")
    
    # Test 8: All statuses
    print("\n8. All API Statuses")
    print("-" * 70)
    
    # Add some data for v1 and v2
    service.record_success("v1", 0.3, 200)
    service.record_success("v1", 0.4, 200)
    service.record_failure("v2", "Not configured", 404)
    
    all_statuses = service.get_all_statuses()
    print(f"v1: {all_statuses['v1']['status_text']}")
    print(f"v2: {all_statuses['v2']['status_text']}")
    print(f"v3: {all_statuses['v3']['status_text']}")
    print("✓ All statuses retrieved")
    
    # Test 9: Reset metrics
    print("\n9. Reset Metrics")
    print("-" * 70)
    service.reset_metrics("v3")
    
    status = service.get_status("v3")
    print(f"Status after reset: {status.value}")
    assert status == APIStatus.UNKNOWN, "Status should be UNKNOWN after reset"
    print("✓ Metrics reset successfully")
    
    print("\n" + "=" * 70)
    print("✅ All tests passed!")
    print("=" * 70)


@pytest.mark.asyncio
async def test_with_card_service():
    """Test integration with CardService"""
    print("\n" + "=" * 70)
    print("CardService Integration Test")
    print("=" * 70)
    
    from dotenv import load_dotenv
    load_dotenv(project_root / ".env", override=True)
    
    from services.card_service import CardService
    
    service = CardService()
    
    print("\n1. Fetching cards (will track status)...")
    result = await service.fetch_cards(page=1, limit=5)
    
    print(f"   Result: {result.get('success', False)}")
    print(f"   Cards: {len(result.get('data', []))}")
    
    print("\n2. Getting API status...")
    status_msg = service.get_api_status_message()
    print(f"   {status_msg}")
    
    print("\n3. Getting detailed status...")
    details = service.get_api_status_details()
    print(f"   Status: {details['status_text']}")
    print(f"   Requests: {details['metrics']['total_requests']}")
    print(f"   Success Rate: {details['metrics']['success_rate']}%")
    
    print("\n✅ CardService integration working!")


if __name__ == "__main__":
    # Run basic tests
    test_status_tracking()
    
    # Run integration test
    print("\n")
    asyncio.run(test_with_card_service())

