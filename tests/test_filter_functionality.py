#!/usr/bin/env python3
"""
Test filter functionality for both API v1 and API v2
"""

import asyncio
import logging

from services.card_service import CardService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_filter_functionality():
    """Test filter functionality for both APIs"""
    print("============================================================")
    print("Filter Functionality Test (API v1 vs API v2)")
    print("============================================================")
    
    results = {"api_v1": False, "api_v2": False}
    
    # Test API v2 filters
    print("\n🔹 Testing API v2 filters...")
    try:
        card_service_v2 = CardService(use_api_v2=True)
        
        # Test common filter names
        filter_names = ["country", "bank", "bin", "level"]
        
        for filter_name in filter_names:
            print(f"   Testing {filter_name} filter...")
            result = await card_service_v2.fetch_filter_options(
                filter_name=filter_name,
                filters={},
                user_id="test_user"
            )
            
            if result.get("success", False):
                data = result.get("data", [])
                if isinstance(data, list):
                    print(f"   ✅ {filter_name}: Found {len(data)} options")
                elif isinstance(data, dict):
                    filters_data = data.get("filters", [])
                    print(f"   ✅ {filter_name}: Found {len(filters_data)} options")
                results["api_v2"] = True
                break  # At least one filter worked
            else:
                error = result.get("error", "Unknown error")
                print(f"   ❌ {filter_name}: {error}")
        
        if not results["api_v2"]:
            print("   ❌ No API v2 filters working")
            
    except Exception as e:
        print(f"   ❌ API v2 filter error: {e}")
    
    # Test API v1 filters
    print("\n🔹 Testing API v1 filters...")
    try:
        card_service_v1 = CardService(use_api_v2=False)
        
        # Test common filter names
        filter_names = ["country", "bank", "bin", "level"]
        
        for filter_name in filter_names:
            print(f"   Testing {filter_name} filter...")
            result = await card_service_v1.fetch_filter_options(
                filter_name=filter_name,
                filters={},
                user_id="test_user"
            )
            
            if result.get("success", False):
                data = result.get("data", [])
                print(f"   ✅ {filter_name}: Found {len(data)} options")
                results["api_v1"] = True
                break  # At least one filter worked
            else:
                error = result.get("error", "Unknown error")
                print(f"   ❌ {filter_name}: {error}")
        
        if not results["api_v1"]:
            print("   ❌ No API v1 filters working")
            
    except Exception as e:
        print(f"   ❌ API v1 filter error: {e}")
    
    # Summary
    print("\n============================================================")
    print("📊 Filter Test Results:")
    print(f"   API v1 Filters: {'✅ WORKING' if results['api_v1'] else '❌ FAILED'}")
    print(f"   API v2 Filters: {'✅ WORKING' if results['api_v2'] else '❌ FAILED'}")
    
    if results["api_v1"] and results["api_v2"]:
        print("\n🎉 SUCCESS: Both API v1 and API v2 filters are working!")
        print("   - Filter NoneType errors should be resolved")
        return True
    elif results["api_v2"]:
        print("\n✅ API v2 filters working (primary goal achieved)")
        print("❌ API v1 filters have issues")
        return True
    else:
        print("\n💥 FAILURE: Filter functionality still broken")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_filter_functionality())
    print(f"\nOverall result: {'PASS' if success else 'FAIL'}")
