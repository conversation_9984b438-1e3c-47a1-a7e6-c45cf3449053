#!/usr/bin/env python3
"""
Simple test to verify API v2 authentication is working
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

from api_v2.services.browse_service import APIV2BrowseService, APIV2BrowseParams

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_api_v2_auth():
    """Test API v2 authentication"""
    print("============================================================")
    print("API v2 Authentication Test")
    print("============================================================")
    
    try:
        # Load environment variables
        load_dotenv()
        login_token = os.getenv("EXTERNAL_LOGIN_TOKEN")
        
        if not login_token:
            print("❌ No EXTERNAL_LOGIN_TOKEN found in environment")
            return False
            
        # Prepare session cookies
        session_cookies = {
            "loginToken": login_token,
            "__ddg8_": os.getenv("EXTERNAL_DDG8", ""),
            "__ddg9_": os.getenv("EXTERNAL_DDG9", ""),
            "__ddg10_": os.getenv("EXTERNAL_DDG10", ""),
            "testcookie": "1",
        }
        session_cookies = {k: v for k, v in session_cookies.items() if v}
        
        # Create API v1 config for inheritance
        api1_config_dict = {
            "authentication": {"type": "bearer_token", "bearer_token": login_token},
            "credentials": {
                "login_token": login_token,
                "session_cookies": session_cookies,
                "headers": {"Authorization": f"Bearer {login_token}"}
            },
            "shared_config": {
                "authentication": {"bearer_token": login_token},
                "default_headers": {"Authorization": f"Bearer {login_token}"},
                "session_cookies": session_cookies
            }
        }
        
        # Create API v2 service with authentication
        config_kwargs = {
            "inherit_auth_from_api1": True,
            "api1_config": api1_config_dict,
            "login_token": login_token,
            "session_cookies": session_cookies,
        }
        
        service = APIV2BrowseService(config_kwargs=config_kwargs)
        params = APIV2BrowseParams(page=1, limit=3)
        
        print("Making API v2 request...")
        result = await service.list_items(params, user_id="test_user")
        
        if isinstance(result, dict) and result.get("success", False):
            cards = result.get("data", [])
            print(f"✅ API v2 authentication working! Found {len(cards)} cards")
            return True
        else:
            print(f"❌ API v2 request failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_api_v2_auth())
    if success:
        print("🎉 Authentication test PASSED!")
    else:
        print("💥 Authentication test FAILED!")
