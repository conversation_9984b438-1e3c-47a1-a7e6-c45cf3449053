#!/usr/bin/env python3
"""
Test card service with API v1 fallback
"""

import asyncio
import logging

from services.card_service import CardService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_card_service_api_v1():
    """Test card service with API v1 fallback"""
    print("============================================================")
    print("Card Service API v1 Fallback Test")
    print("============================================================")
    
    try:
        # Create card service with API v1 (use_api_v2=False)
        card_service = CardService(use_api_v2=False)
        
        print("Testing card service with API v1...")
        result = await card_service.fetch_cards(page=1, limit=3, user_id='test_user')
        
        if result.get("success", False):
            cards = result.get("data", [])
            print(f"✅ Card service API v1 working! Found {len(cards)} cards")
            for i, card in enumerate(cards[:2], 1):  # Show first 2 cards
                bin_num = card.get("bin", "N/A")
                country = card.get("country", "N/A") 
                price = card.get("price", "N/A")
                print(f"  {i}. Card: {bin_num} | {country} | ${price}")
            print("============================================================")
            print("🎉 Card service API v1 test PASSED!")
            return True
        else:
            error = result.get("error", "Unknown error")
            print(f"❌ Card service API v1 failed: {error}")
            print("============================================================")
            print("💥 Card service API v1 test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Card service error: {e}")
        import traceback
        traceback.print_exc()
        print("============================================================")
        print("💥 Card service API v1 test FAILED!")
        return False

if __name__ == "__main__":
    asyncio.run(test_card_service_api_v1())
