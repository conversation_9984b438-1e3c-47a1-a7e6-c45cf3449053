"""
Product and API selection models for multi-product bot architecture
"""

from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

from models.base import BaseDocument, SoftDeleteMixin, now_utc


class ProductType(str, Enum):
    """Available product types"""

    BIN = "bin"
    DUMP = "dump"


class APIStatus(str, Enum):
    """API availability status"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    ERROR = "error"


class APIInfo(BaseModel):
    """Information about an API endpoint"""

    id: str = Field(description="Unique API identifier")
    name: str = Field(description="Display name for the API")
    description: str = Field(description="API description")
    status: APIStatus = Field(
        default=APIStatus.ACTIVE, description="Current API status"
    )
    config_name: str = Field(description="Configuration name in API config system")
    is_default: bool = Field(
        default=False, description="Whether this is the default API for the product"
    )
    order: int = Field(default=0, description="Display order in menus")

    # Feature flags
    supports_filters: bool = Field(
        default=True, description="Whether API supports filtering"
    )
    supports_cart: bool = Field(
        default=True, description="Whether API supports cart operations"
    )
    supports_checkout: bool = Field(
        default=True, description="Whether API supports checkout"
    )

    # Metadata
    created_at: datetime = Field(default_factory=now_utc)
    updated_at: datetime = Field(default_factory=now_utc)


class ProductInfo(BaseModel):
    """Information about a product category"""

    type: ProductType = Field(description="Product type")
    name: str = Field(description="Display name for the product")
    description: str = Field(description="Product description")
    icon: str = Field(description="Emoji icon for the product")
    apis: List[APIInfo] = Field(
        default_factory=list, description="Available APIs for this product"
    )
    is_active: bool = Field(
        default=True, description="Whether product is currently available"
    )
    order: int = Field(default=0, description="Display order in main menu")

    # Metadata
    created_at: datetime = Field(default_factory=now_utc)
    updated_at: datetime = Field(default_factory=now_utc)

    def get_active_apis(self) -> List[APIInfo]:
        """Get list of active APIs for this product"""
        return [api for api in self.apis if api.status == APIStatus.ACTIVE]

    def get_default_api(self) -> Optional[APIInfo]:
        """Get the default API for this product"""
        active_apis = self.get_active_apis()
        if not active_apis:
            return None

        # Return the marked default API, or first active API
        for api in active_apis:
            if api.is_default:
                return api
        return active_apis[0]


class UserProductPreference(BaseDocument, SoftDeleteMixin):
    """User's product and API preferences"""

    user_id: str = Field(description="User's database ID")
    telegram_id: int = Field(description="User's Telegram ID")

    # Current selections
    selected_product: Optional[ProductType] = Field(
        default=None, description="Currently selected product"
    )
    selected_api: Optional[str] = Field(
        default=None, description="Currently selected API ID"
    )

    # Preferences
    preferred_product: Optional[ProductType] = Field(
        default=None, description="User's preferred product"
    )
    preferred_apis: Dict[str, str] = Field(
        default_factory=dict, description="Preferred API per product type"
    )

    # Usage tracking
    last_product_change: Optional[datetime] = Field(
        default=None, description="When user last changed product"
    )
    last_api_change: Optional[datetime] = Field(
        default=None, description="When user last changed API"
    )
    product_usage_count: Dict[str, int] = Field(
        default_factory=dict, description="Usage count per product"
    )
    api_usage_count: Dict[str, int] = Field(
        default_factory=dict, description="Usage count per API"
    )

    def update_selection(self, product: ProductType, api_id: str) -> None:
        """Update user's current selection"""
        self.selected_product = product
        self.selected_api = api_id
        self.last_product_change = now_utc()
        self.last_api_change = now_utc()

        # Update usage counters
        product_key = product.value
        self.product_usage_count[product_key] = (
            self.product_usage_count.get(product_key, 0) + 1
        )
        self.api_usage_count[api_id] = self.api_usage_count.get(api_id, 0) + 1

        # Update preferences based on usage
        self.preferred_product = product
        self.preferred_apis[product_key] = api_id


class ProductConfiguration(BaseDocument, SoftDeleteMixin):
    """System-wide product configuration"""

    name: str = Field(description="Configuration name")
    products: List[ProductInfo] = Field(
        default_factory=list, description="Available products"
    )

    # Global settings
    show_wallet_on_start: bool = Field(
        default=True, description="Show wallet button prominently on start"
    )
    allow_product_switching: bool = Field(
        default=True, description="Allow users to switch between products"
    )
    remember_user_preferences: bool = Field(
        default=True, description="Remember user's product/API choices"
    )

    # UI settings
    show_api_status: bool = Field(
        default=True, description="Show API status indicators"
    )
    show_breadcrumbs: bool = Field(
        default=True, description="Show navigation breadcrumbs"
    )
    enable_quick_access: bool = Field(
        default=True, description="Enable quick access to preferred APIs"
    )

    # Metadata
    version: str = Field(default="1.0", description="Configuration version")
    created_by: str = Field(description="Who created this configuration")
    is_active: bool = Field(
        default=True, description="Whether this configuration is active"
    )

    def get_active_products(self) -> List[ProductInfo]:
        """Get list of active products"""
        return [product for product in self.products if product.is_active]

    def get_product_by_type(self, product_type: ProductType) -> Optional[ProductInfo]:
        """Get product by type"""
        for product in self.products:
            if product.type == product_type and product.is_active:
                return product
        return None

    def get_api_by_id(self, api_id: str) -> Optional[APIInfo]:
        """Get API by ID across all products"""
        for product in self.products:
            for api in product.apis:
                if api.id == api_id:
                    return api
        return None


# Default product configuration
DEFAULT_PRODUCT_CONFIG = ProductConfiguration(
    name="default_multi_product_config",
    created_by="system",
    products=[
        ProductInfo(
            type=ProductType.BIN,
            name="BIN Cards",
            description="Bank Identification Number cards with various data points",
            icon="💳",
            order=1,
            apis=[
                APIInfo(
                    id="bin_base_1",
                    name="BASE 1",
                    description="Primary BIN API (Currently Active)",
                    config_name="api1",  # Maps to existing BASE 1 config
                    is_default=True,
                    order=1,
                    status=APIStatus.ACTIVE,
                ),
                APIInfo(
                    id="bin_base_2",
                    name="BASE 2",
                    description="Secondary BIN API (VHQ Endpoint)",
                    config_name="api2",
                    order=2,
                    status=APIStatus.ACTIVE,
                ),
                APIInfo(
                    id="bin_base_3",
                    name="BASE 3",
                    description="Tor HTML integration (API v3)",
                    config_name="api3",
                    order=3,
                    status=APIStatus.ACTIVE,
                    supports_filters=True,
                    supports_cart=True,
                    supports_checkout=True,
                ),
            ],
        ),
        ProductInfo(
            type=ProductType.DUMP,
            name="DUMP Cards",
            description="Full card dumps with complete data sets",
            icon="🗂️",
            order=2,
            apis=[
                APIInfo(
                    id="dump_base_1",
                    name="DUMPS v1",
                    description="Full card dumps with cart system",
                    config_name="dump_api1",
                    is_default=True,
                    order=1,
                    status=APIStatus.ACTIVE,
                    supports_filters=True,
                    supports_cart=True,
                    supports_checkout=True,
                ),
                APIInfo(
                    id="dump_base_2",
                    name="VDUMPS v2",
                    description="Virtual dumps with direct purchase",
                    config_name="dump_api2",
                    order=2,
                    status=APIStatus.ACTIVE,
                    supports_filters=True,
                    supports_cart=True,
                    supports_checkout=True,
                ),
            ],
        ),
    ],
)
