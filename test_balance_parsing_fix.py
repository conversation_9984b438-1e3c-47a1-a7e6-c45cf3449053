#!/usr/bin/env python3
"""
Test and Fix Balance Parsing Issue

The previous test revealed that the balance IS present in the API response,
but it's nested inside the 'user' object. The balance is '51.64244' which
is more than sufficient, so the "insufficient balance" error is due to
incorrect balance parsing, not an actual lack of funds.
"""

import asyncio
import sys
import os
import json
sys.path.insert(0, '.')

from services.external_api_service import ExternalAPIService
from config.settings import get_settings

async def analyze_user_info_response():
    """Analyze the actual structure of the user info response"""
    print("🔍 Analyzing User Info Response Structure")
    print("=" * 60)
    
    try:
        api_service = ExternalAPIService()
        response = await api_service.get_user_info()
        
        if response.success:
            print("✅ Successfully retrieved user info")
            
            # Parse the response data
            data = response.data
            print(f"\n📊 Response Structure Analysis:")
            print(f"   Response type: {type(data).__name__}")
            print(f"   Top-level keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if isinstance(data, dict):
                # Check if 'user' key exists
                if 'user' in data:
                    user_data = data['user']
                    print(f"\n👤 User Object Found:")
                    print(f"   User data type: {type(user_data).__name__}")
                    
                    if isinstance(user_data, dict):
                        print(f"   User fields: {list(user_data.keys())}")
                        
                        # Check for balance field
                        if 'balance' in user_data:
                            balance_value = user_data['balance']
                            print(f"\n💰 BALANCE FOUND!")
                            print(f"   Raw balance value: {balance_value}")
                            print(f"   Balance type: {type(balance_value).__name__}")
                            
                            # Try to parse as float
                            try:
                                numeric_balance = float(balance_value)
                                print(f"   Parsed balance: {numeric_balance}")
                                
                                if numeric_balance > 0:
                                    print(f"✅ Balance is sufficient: ${numeric_balance:.2f}")
                                    print("   The 'insufficient balance' error is NOT due to lack of funds!")
                                    print("   The issue is in the balance parsing logic.")
                                else:
                                    print(f"❌ Balance is insufficient: ${numeric_balance:.2f}")
                                    
                            except (ValueError, TypeError) as e:
                                print(f"❌ Could not parse balance as number: {e}")
                        else:
                            print("❌ No 'balance' field found in user object")
                            print(f"   Available user fields: {list(user_data.keys())}")
                    else:
                        print(f"⚠️ User data is not a dictionary: {user_data}")
                else:
                    print("❌ No 'user' key found in response")
                    
                # Show full response for debugging
                print(f"\n📋 Full Response Data:")
                print(json.dumps(data, indent=2))
                
            return data
            
        else:
            print(f"❌ Failed to get user info: {response.error}")
            return None
            
    except Exception as e:
        print(f"❌ Exception during analysis: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_balance_extraction_logic():
    """Test different approaches to extract balance from the response"""
    print(f"\n🔧 Testing Balance Extraction Logic")
    print("=" * 60)
    
    # Sample response based on the actual API response
    sample_response = {
        "success": True,
        "user": {
            "_id": 197870,
            "email": "<EMAIL>",
            "telegram_username": None,
            "username": "Cosmicgod",
            "rank": "newcomer",
            "role": "user",
            "status": "active",
            "referral_id": "wgpB4Pgz",
            "two_fa_enabled": 0,
            "refferalEarning": "0.0000",
            "balance": "51.64244",
            "wallet": None,
            "earning": "0.0000",
            "createdAt": "2025-08-28T10:04:48.000Z",
            "cartnumber": 1,
            "inbox": 0,
            "ticket": 1,
            "reports": None
        }
    }
    
    print("🧪 Testing different balance extraction methods:")
    
    # Method 1: Direct access (WRONG - this is what's currently failing)
    print("\n1. Direct access to response.data['balance']:")
    try:
        balance = sample_response.get('balance')
        print(f"   Result: {balance}")
        print("   ❌ This method fails - balance is not at top level")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Method 2: Nested access (CORRECT)
    print("\n2. Nested access to response.data['user']['balance']:")
    try:
        balance = sample_response.get('user', {}).get('balance')
        if balance:
            numeric_balance = float(balance)
            print(f"   Result: {balance} -> {numeric_balance}")
            print("   ✅ This method works correctly!")
        else:
            print("   ❌ Balance not found")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Method 3: Robust extraction function
    print("\n3. Robust extraction function:")
    
    def extract_balance_from_response(response_data):
        """Extract balance from API response with multiple fallback strategies"""
        if not isinstance(response_data, dict):
            return None
        
        # Strategy 1: Check user.balance (most likely for /user/getme)
        user_data = response_data.get('user', {})
        if isinstance(user_data, dict) and 'balance' in user_data:
            try:
                return float(user_data['balance'])
            except (ValueError, TypeError):
                pass
        
        # Strategy 2: Check top-level balance
        if 'balance' in response_data:
            try:
                return float(response_data['balance'])
            except (ValueError, TypeError):
                pass
        
        # Strategy 3: Check other common balance field names
        balance_fields = ['credits', 'funds', 'money', 'wallet_balance', 'account_balance']
        for field in balance_fields:
            if field in response_data:
                try:
                    return float(response_data[field])
                except (ValueError, TypeError):
                    pass
            
            # Also check in user object
            if isinstance(user_data, dict) and field in user_data:
                try:
                    return float(user_data[field])
                except (ValueError, TypeError):
                    pass
        
        return None
    
    try:
        extracted_balance = extract_balance_from_response(sample_response)
        if extracted_balance is not None:
            print(f"   Result: {extracted_balance}")
            print("   ✅ Robust extraction works!")
        else:
            print("   ❌ Could not extract balance")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return extract_balance_from_response

async def test_real_balance_extraction():
    """Test balance extraction with real API response"""
    print(f"\n🚀 Testing Real Balance Extraction")
    print("=" * 60)
    
    try:
        api_service = ExternalAPIService()
        response = await api_service.get_user_info()
        
        if response.success:
            print("✅ Got real API response")
            
            # Use the robust extraction function
            def extract_balance_from_response(response_data):
                """Extract balance from API response with multiple fallback strategies"""
                if not isinstance(response_data, dict):
                    return None
                
                # Strategy 1: Check user.balance (most likely for /user/getme)
                user_data = response_data.get('user', {})
                if isinstance(user_data, dict) and 'balance' in user_data:
                    try:
                        return float(user_data['balance'])
                    except (ValueError, TypeError):
                        pass
                
                # Strategy 2: Check top-level balance
                if 'balance' in response_data:
                    try:
                        return float(response_data['balance'])
                    except (ValueError, TypeError):
                        pass
                
                # Strategy 3: Check other common balance field names
                balance_fields = ['credits', 'funds', 'money', 'wallet_balance', 'account_balance']
                for field in balance_fields:
                    if field in response_data:
                        try:
                            return float(response_data[field])
                        except (ValueError, TypeError):
                            pass
                    
                    # Also check in user object
                    if isinstance(user_data, dict) and field in user_data:
                        try:
                            return float(user_data[field])
                        except (ValueError, TypeError):
                            pass
                
                return None
            
            balance = extract_balance_from_response(response.data)
            
            if balance is not None:
                print(f"💰 Successfully extracted balance: ${balance:.2f}")
                
                if balance > 0:
                    print("✅ Account has sufficient funds!")
                    print("🔍 The 'insufficient balance' error is due to incorrect parsing")
                    print("💡 Need to fix the balance extraction logic in the checkout process")
                else:
                    print("❌ Account balance is zero or negative")
                    print("💡 Need to add funds to the account")
                    
                return balance
            else:
                print("❌ Could not extract balance from response")
                print("🔍 Response structure may have changed")
                return None
                
        else:
            print(f"❌ Failed to get user info: {response.error}")
            return None
            
    except Exception as e:
        print(f"❌ Exception during real balance extraction: {e}")
        import traceback
        traceback.print_exc()
        return None

def identify_fix_locations():
    """Identify where the balance parsing logic needs to be fixed"""
    print(f"\n🔧 Identifying Fix Locations")
    print("=" * 60)
    
    locations_to_fix = [
        {
            "file": "services/checkout_queue_service.py",
            "method": "Balance validation in checkout process",
            "description": "Checkout process likely checks balance before proceeding",
            "fix": "Update balance extraction to use response.data['user']['balance']"
        },
        {
            "file": "services/external_api_service.py", 
            "method": "get_user_info() response parsing",
            "description": "May need to normalize the response format",
            "fix": "Add balance extraction helper to the response"
        },
        {
            "file": "handlers/wallet_handlers.py",
            "method": "Balance display in wallet handlers",
            "description": "Wallet handlers may show incorrect balance",
            "fix": "Update balance extraction logic"
        },
        {
            "file": "services/cart_service.py",
            "method": "Cart operations balance checking",
            "description": "Cart service may validate balance before operations",
            "fix": "Use correct balance extraction method"
        }
    ]
    
    print("📋 Locations that likely need balance parsing fixes:")
    print()
    
    for i, location in enumerate(locations_to_fix, 1):
        print(f"{i}. {location['file']}")
        print(f"   Method: {location['method']}")
        print(f"   Description: {location['description']}")
        print(f"   Fix: {location['fix']}")
        print()
    
    print("🎯 Priority Fix:")
    print("1. Find where 'insufficient balance' error is generated")
    print("2. Update that location to use correct balance extraction")
    print("3. Test checkout process with corrected balance parsing")

async def main():
    """Run balance parsing analysis and tests"""
    try:
        print("🔍 BALANCE PARSING ISSUE ANALYSIS")
        print("=" * 60)
        
        # Analyze the response structure
        response_data = await analyze_user_info_response()
        
        # Test extraction methods
        extract_function = test_balance_extraction_logic()
        
        # Test with real data
        real_balance = await test_real_balance_extraction()
        
        # Identify fix locations
        identify_fix_locations()
        
        print("\n" + "=" * 60)
        print("🎯 CONCLUSION")
        print("=" * 60)
        
        if real_balance and real_balance > 0:
            print("✅ ISSUE IDENTIFIED: Balance parsing is incorrect")
            print(f"💰 Account has sufficient funds: ${real_balance:.2f}")
            print("🔍 The balance is in response.data['user']['balance'], not response.data['balance']")
            print("🔧 Need to fix balance extraction logic in checkout process")
            print("\n📋 Next Steps:")
            print("1. Find where 'insufficient balance' error is generated")
            print("2. Update balance extraction to use response.data['user']['balance']")
            print("3. Test checkout process with corrected parsing")
        else:
            print("❌ Could not determine balance or balance is insufficient")
            print("🔍 May need to investigate further or add funds to account")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
