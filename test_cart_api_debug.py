#!/usr/bin/env python3
"""
Debug script to test the cart API operations and see what's happening
with the add_to_cart and view_cart operations.
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv
load_dotenv()

from api_v3.config import get_api_v3_config_from_env
from api_v3.services.cart_service import APIV3CartService
from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_cart_operations():
    """Test cart operations to debug the issue."""
    logger.info("🧪 Testing Cart API Operations")
    logger.info("=" * 50)
    
    try:
        # Get API v3 configuration
        config = get_api_v3_config_from_env()
        if not config:
            logger.error("❌ API v3 configuration not found")
            return False
        
        # Initialize cart service
        cart_service = APIV3CartService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )
        
        # Initialize browse service to get a test card
        browse_service = APIV3BrowseService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )
        
        # Get a test card
        logger.info("🔍 Getting test card...")
        browse_result = await browse_service.list_items(
            APIV3BrowseParams(limit=1), "test_user"
        )
        
        if not browse_result.success:
            logger.error(f"❌ Failed to browse items: {browse_result.error}")
            return False
        
        items = browse_result.data.get("data", [])
        if not items:
            logger.error("❌ No items found")
            return False
        
        test_card_id = items[0].get("_id")
        if not test_card_id:
            logger.error("❌ Test card has no ID")
            return False
        
        logger.info(f"✅ Got test card ID: {test_card_id}")
        
        # Step 1: View cart before adding (should be empty)
        logger.info("\n📋 Step 1: View cart before adding...")
        view_result_before = await cart_service.view_cart("test_user")
        logger.info(f"Cart before adding: {json.dumps(view_result_before, indent=2)}")
        
        # Step 2: Add item to cart
        logger.info(f"\n➕ Step 2: Adding card {test_card_id} to cart...")
        add_result = await cart_service.add_to_cart([test_card_id], "test_user")
        logger.info(f"Add to cart result: {json.dumps(add_result, indent=2)}")
        
        if not add_result.get("success"):
            logger.error(f"❌ Failed to add to cart: {add_result.get('error')}")
            return False
        
        # Step 3: View cart after adding
        logger.info(f"\n📋 Step 3: View cart after adding...")
        view_result_after = await cart_service.view_cart("test_user")
        logger.info(f"Cart after adding: {json.dumps(view_result_after, indent=2)}")
        
        # Step 4: Check if item is in cart
        cart_items = view_result_after.get("items", [])
        logger.info(f"\n🔍 Step 4: Cart analysis...")
        logger.info(f"Number of items in cart: {len(cart_items)}")
        
        if cart_items:
            logger.info("Cart items:")
            for i, item in enumerate(cart_items):
                logger.info(f"  Item {i+1}: {json.dumps(item, indent=4)}")
                
                # Check if this item matches our test card
                item_id = (
                    item.get("id") or 
                    item.get("_id") or 
                    item.get("product_id") or 
                    item.get("card_id")
                )
                
                if item_id == test_card_id:
                    logger.info(f"✅ Found our test card in cart: {item_id}")
                else:
                    logger.info(f"🔍 Item ID: {item_id} (doesn't match test card: {test_card_id})")
        else:
            logger.warning("⚠️  Cart appears to be empty after adding item")
        
        # Step 5: Check raw cart data
        logger.info(f"\n🔍 Step 5: Raw cart data analysis...")
        raw_data = view_result_after.get("raw_data", {})
        logger.info(f"Raw cart data: {json.dumps(raw_data, indent=2)}")
        
        # Clean up
        await cart_service.close()
        await browse_service.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def main():
    """Run the cart debug test."""
    logger.info("🚀 Starting Cart API Debug Test")
    logger.info("=" * 60)
    
    success = await test_cart_operations()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 DEBUG TEST RESULTS")
    logger.info("=" * 60)
    
    if success:
        logger.info("✅ Debug test completed successfully")
        logger.info("   Check the logs above to understand cart behavior")
    else:
        logger.error("❌ Debug test failed")
        logger.error("   Check the error messages above")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
