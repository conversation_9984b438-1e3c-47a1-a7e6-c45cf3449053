#!/usr/bin/env python3
"""
Verify Check Order API Fix

This script verifies that the check_order method now has proper API version routing.
"""

import asyncio
import sys
import os
from typing import Dict, Any
sys.path.insert(0, '.')

async def test_check_order_api_routing():
    """Test that check_order now has proper API version routing"""
    print("🔍 TESTING CHECK ORDER API VERSION ROUTING")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        import inspect
        
        # Test API v1 routing
        print("📋 Testing API v1 routing:")
        external_api_v1 = ExternalAPIService(api_version="v1")
        print(f"   • Service initialized with API {external_api_v1.api_version}")
        
        # Check if check_order method now has version routing
        check_order_source = inspect.getsource(external_api_v1.check_order)
        
        has_v3_routing = "use_v3" in check_order_source and "api_version" in check_order_source
        has_routing_logic = "Routing check_order to API" in check_order_source
        has_v3_method_call = "_check_order_v3" in check_order_source
        
        print(f"   • Has API version routing: {'✅' if has_v3_routing else '❌'}")
        print(f"   • Has routing logic: {'✅' if has_routing_logic else '❌'}")
        print(f"   • Has v3 method call: {'✅' if has_v3_method_call else '❌'}")
        
        # Test API v3 routing
        print(f"\n📋 Testing API v3 routing:")
        external_api_v3 = ExternalAPIService(api_version="v3")
        print(f"   • Service initialized with API {external_api_v3.api_version}")
        
        # Check if _check_order_v3 method exists
        has_v3_method = hasattr(external_api_v3, '_check_order_v3')
        print(f"   • Has _check_order_v3 method: {'✅' if has_v3_method else '❌'}")
        
        if has_v3_method:
            v3_method_source = inspect.getsource(external_api_v3._check_order_v3)
            uses_order_service = "APIV3OrderService" in v3_method_source
            uses_check_card = "check_card" in v3_method_source
            print(f"   • Uses APIV3OrderService: {'✅' if uses_order_service else '❌'}")
            print(f"   • Uses check_card method: {'✅' if uses_check_card else '❌'}")
        
        return has_v3_routing and has_v3_method
        
    except Exception as e:
        print(f"❌ Error testing API routing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_method_signature_consistency():
    """Test that check_order signature is consistent with other methods"""
    print(f"\n📋 TESTING METHOD SIGNATURE CONSISTENCY")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        import inspect
        
        external_api = ExternalAPIService()
        
        # Get method signatures
        methods_to_check = ["list_items", "add_to_cart", "view_cart", "check_order"]
        
        print("📋 Method signatures:")
        for method_name in methods_to_check:
            method = getattr(external_api, method_name)
            signature = inspect.signature(method)
            print(f"   • {method_name}: {signature}")
        
        # Check if check_order follows the same pattern as other methods
        check_order_source = inspect.getsource(external_api.check_order)
        
        patterns_to_check = [
            ("API version routing", "use_v3 = self.api_version"),
            ("Routing log message", "Routing check_order to API"),
            ("V3 method call", "return await self._check_order_v3"),
            ("Legacy implementation", "config = await self._get_api_config"),
        ]
        
        print(f"\n📋 Pattern consistency checks:")
        all_patterns_found = True
        for pattern_name, pattern in patterns_to_check:
            found = pattern in check_order_source
            status = "✅" if found else "❌"
            print(f"   {status} {pattern_name}")
            if not found:
                all_patterns_found = False
        
        return all_patterns_found
        
    except Exception as e:
        print(f"❌ Error testing method consistency: {e}")
        return False

async def test_api_v1_implementation():
    """Test that API v1 implementation is preserved"""
    print(f"\n🔧 TESTING API V1 IMPLEMENTATION")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        import inspect
        
        external_api = ExternalAPIService(api_version="v1")
        check_order_source = inspect.getsource(external_api.check_order)
        
        # Check that API v1 implementation details are preserved
        v1_patterns = [
            ("URL construction", "/cards/hq/check"),
            ("POST method", 'method="POST"'),
            ("Payload format", '{"id": int(order_id)}'),
            ("Headers building", "_build_headers"),
            ("Cookies building", "_build_cookies"),
        ]
        
        print("📋 API v1 implementation checks:")
        all_v1_patterns = True
        for pattern_name, pattern in v1_patterns:
            found = pattern in check_order_source
            status = "✅" if found else "❌"
            print(f"   {status} {pattern_name}: {pattern}")
            if not found:
                all_v1_patterns = False
        
        # Check that the demo reference is followed
        print(f"\n📋 Demo reference compliance:")
        demo_compliance = [
            ("Demo URL match", "cards/hq/check"),
            ("Demo method match", "POST"),
            ("Demo payload match", '"id"'),
        ]
        
        for check_name, pattern in demo_compliance:
            found = pattern in check_order_source
            status = "✅" if found else "❌"
            print(f"   {status} {check_name}")
        
        return all_v1_patterns
        
    except Exception as e:
        print(f"❌ Error testing API v1 implementation: {e}")
        return False

async def test_error_handling():
    """Test that error handling is preserved and enhanced"""
    print(f"\n🛡️ TESTING ERROR HANDLING")
    print("=" * 60)
    
    try:
        from services.external_api_service import ExternalAPIService
        import inspect
        
        external_api = ExternalAPIService()
        
        # Check main method error handling
        check_order_source = inspect.getsource(external_api.check_order)
        
        error_patterns = [
            ("Try-catch block", "try:" and "except Exception as e:"),
            ("Configuration check", "API configuration not available"),
            ("Success logging", "Check order completed successfully"),
            ("Error logging", "Check order failed"),
            ("APIResponse error", "APIResponse(success=False"),
        ]
        
        print("📋 Error handling checks:")
        for pattern_name, pattern in error_patterns:
            if isinstance(pattern, tuple):
                found = all(p in check_order_source for p in pattern)
            else:
                found = pattern in check_order_source
            status = "✅" if found else "❌"
            print(f"   {status} {pattern_name}")
        
        # Check v3 method error handling
        if hasattr(external_api, '_check_order_v3'):
            v3_source = inspect.getsource(external_api._check_order_v3)
            
            print(f"\n📋 API v3 error handling:")
            v3_error_patterns = [
                ("V3 config check", "API v3 configuration not available"),
                ("V3 try-catch", "try:" and "except Exception as e:"),
                ("V3 success response", "APIResponse(success=True"),
                ("V3 error response", "APIResponse(success=False"),
            ]
            
            for pattern_name, pattern in v3_error_patterns:
                if isinstance(pattern, tuple):
                    found = all(p in v3_source for p in pattern)
                else:
                    found = pattern in v3_source
                status = "✅" if found else "❌"
                print(f"   {status} {pattern_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing error handling: {e}")
        return False

async def main():
    """Run comprehensive fix verification"""
    print("🔍 CHECK ORDER FIX VERIFICATION")
    print("=" * 60)
    
    tests = [
        ("API Version Routing", test_check_order_api_routing),
        ("Method Signature Consistency", test_method_signature_consistency),
        ("API v1 Implementation", test_api_v1_implementation),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FIX VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = [name for name, success in results if success]
    failed = [name for name, success in results if not success]
    
    print(f"✅ PASSED TESTS: {len(passed)}/{len(results)}")
    for test_name in passed:
        print(f"   • {test_name}")
    
    if failed:
        print(f"\n❌ FAILED TESTS: {len(failed)}")
        for test_name in failed:
            print(f"   • {test_name}")
    
    success_rate = len(passed) / len(results) * 100
    print(f"\n📊 SUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"\n🎉 CHECK ORDER FIX VERIFICATION: ✅ PASSED")
        print(f"✅ API version routing implemented successfully")
        print(f"✅ API v1 implementation preserved")
        print(f"✅ API v3 implementation added")
        print(f"✅ Error handling maintained")
    else:
        print(f"\n❌ CHECK ORDER FIX VERIFICATION: FAILED")
        print(f"❌ Some critical components are missing")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
