# API v3 Quick Reference Guide

## Import Services

```python
from api_v3.services import (
    APIV3BrowseService,
    APIV3CartService,
    APIV3OrderService,
)
from api_v3.services.browse_service import APIV3BrowseParams
```

## Initialize Services

```python
# Configuration
base_url = "http://your-onion.onion"
username = "your_username"
password = "your_password"
use_socks = True
socks_url = "socks5h://127.0.0.1:9150"

# Create services (they all share the same session)
browse = APIV3BrowseService(base_url, username, password, use_socks, socks_url)
cart = APIV3CartService(base_url, username, password, use_socks, socks_url)
order = APIV3OrderService(base_url, username, password, use_socks, socks_url)
```

## Browse Cards

```python
# List all items (first page)
params = APIV3BrowseParams(page=1)
result = await browse.list_items(params)

if result.success:
    for item in result.items:
        print(f"{item.id}: {item.bin} - {item.price}")

# Filter by BIN
params = APIV3BrowseParams(page=1, bin="123456")
result = await browse.list_items(params)

# Filter by bank and country
params = APIV3BrowseParams(page=1, bank="Chase", country="US")
result = await browse.list_items(params)

# Get available filters
filters = await browse.get_filters()
print(filters.get("bins", []))
print(filters.get("banks", []))
```

## Cart Operations

```python
# Add items to cart
card_ids = ["item_id_1", "item_id_2"]
result = await cart.add_to_cart(card_ids, user_id="user123")

if result["success"]:
    print(f"Added {len(card_ids)} items to cart")

# View cart
result = await cart.view_cart(user_id="user123")

if result["success"]:
    for item in result["items"]:
        print(f"{item['id']}: {item['bin']} - {item['price']}")
    print(f"Total items: {result['item_count']}")

# Clear cart
result = await cart.clear_cart(user_id="user123")
```

## Order Operations

```python
# Create order from cart
result = await order.create_order(refund=False, user_id="user123")

if result["success"]:
    order_id = result["order_id"]
    print(f"Order created: {order_id}")

# View order details
result = await order.view_order(order_id, user_id="user123")

if result["success"]:
    for item in result["items"]:
        print(f"{item['id']}: {item['card_number']} (masked)")

# Check card validity
cc_id = "card_item_id"
result = await order.check_card(order_id, cc_id, user_id="user123")

if result["success"]:
    print("Card check completed")

# Unmask items
item_ids = ["item_id_1", "item_id_2"]
result = await order.unmask_items(order_id, item_ids, user_id="user123")

if result["success"]:
    print(f"Unmasked {result['unmasked_count']} items")
```

## Complete Workflow

```python
import asyncio

async def main():
    # Initialize
    browse = APIV3BrowseService(base_url, username, password)
    cart = APIV3CartService(base_url, username, password)
    order = APIV3OrderService(base_url, username, password)

    try:
        # 1. Browse
        params = APIV3BrowseParams(page=1)
        items_result = await browse.list_items(params)

        # 2. Add to cart
        card_ids = [items_result.items[0].id, items_result.items[1].id]
        await cart.add_to_cart(card_ids)

        # 3. Create order
        order_result = await order.create_order(refund=False)
        order_id = order_result["order_id"]

        # 4. View order
        order_details = await order.view_order(order_id)

        # 5. Unmask
        item_ids = [item["id"] for item in order_details["items"]]
        await order.unmask_items(order_id, item_ids)

        print("✅ Complete workflow executed successfully!")

    finally:
        await browse.close()
        await cart.close()
        await order.close()

asyncio.run(main())
```

## Error Handling

```python
result = await cart.add_to_cart(card_ids)

if result["success"]:
    print("Success:", result["message"])
else:
    print("Error:", result["error"])
    # Handle error appropriately
```

## Browse Parameters

```python
params = APIV3BrowseParams(
    page=1,              # Page number
    bin="123456",        # Filter by BIN
    bank="Chase",        # Filter by bank name
    country="US",        # Filter by country code
    card_type="VISA",    # Filter by card type
    # ... other filters as needed
)
```

## Configuration (.env)

```env
API_V3_BASE_URL=http://your-onion.onion
API_V3_USERNAME=your_username
API_V3_PASSWORD=your_password
API_V3_USE_SOCKS_PROXY=true
SOCKS_URL=socks5h://127.0.0.1:9150
```

## Session Management

**All services automatically share the same session:**

- ✅ Single login for all operations
- ✅ Session cached for 5 minutes
- ✅ Automatic re-authentication
- ✅ CSRF tokens handled automatically

**No manual session management needed!**

## Testing

```bash
# Validate structure
python validate_api_v3_structure.py

# Run unit tests
pytest tests/test_api_v3_fixed.py

# Run integration tests (requires Tor)
python test_all_api_v3_endpoints.py
```

## Common Patterns

### Filter by Multiple Criteria

```python
params = APIV3BrowseParams(
    page=1,
    bin="123456",
    country="US",
    card_type="VISA"
)
result = await browse.list_items(params)
```

### Add Multiple Items at Once

```python
# Get first 5 items
items = result.items[:5]
card_ids = [item.id for item in items]
await cart.add_to_cart(card_ids)
```

### Check All Cards in Order

```python
order_details = await order.view_order(order_id)

for item in order_details["items"]:
    check_result = await order.check_card(order_id, item["id"])
    if check_result["success"]:
        print(f"Card {item['id']}: Valid")
```

### Selective Unmask

```python
# Only unmask first 2 items
item_ids = [item["id"] for item in order_details["items"][:2]]
await order.unmask_items(order_id, item_ids)
```

## Tips

1. **Always close services** when done to free resources
2. **Check success field** in all responses before accessing data
3. **Use try-finally** to ensure cleanup even on errors
4. **Tor must be running** before making requests
5. **Session is shared** - no need to login multiple times
6. **CSRF tokens** are handled automatically - no manual extraction needed

## Troubleshooting

**Connection timeout?**

- Ensure Tor Browser is running
- Verify port 9150 is open: `python test_tor_connection.py`

**Authentication failed?**

- Check credentials in .env
- Verify SOCKS proxy is correct
- Check if onion service is accessible

**Method not found?**

- Import correct service class
- Check method name spelling
- Ensure using async/await

---

**All endpoints working correctly with session reuse! 🎉**
