#!/usr/bin/env python3
"""
Comprehensive Virtual Cart and Checkout Test Suite

This test suite validates:
1. Virtual cart functionality (add, remove, clear)
2. Checkout process (virtual to API cart transfer)
3. Data validation between virtual and actual carts
4. Edge cases and error handling
5. Complete end-to-end workflow
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv
load_dotenv()

from api_v3.services.virtual_cart import VirtualCart, VirtualCartItem, get_virtual_cart
from api_v3.services.checkout_service import APIV3CheckoutService
from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams
from api_v3.config import get_api_v3_config_from_env

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VirtualCartTestSuite:
    """Comprehensive test suite for virtual cart functionality."""
    
    def __init__(self):
        self.config = get_api_v3_config_from_env()
        if not self.config:
            raise ValueError("API v3 configuration not found")
        
        self.browse_service = None
        self.checkout_service = None
        self.test_user_id = "test_user_virtual_cart"
        self.test_results = {}
    
    async def setup(self):
        """Setup test services."""
        logger.info("🔧 Setting up test services")
        
        self.browse_service = APIV3BrowseService(
            base_url=self.config.base_url,
            username=self.config.username,
            password=self.config.password,
            use_socks_proxy=self.config.use_socks_proxy,
            socks_url=self.config.socks_url,
        )
        
        self.checkout_service = APIV3CheckoutService(
            base_url=self.config.base_url,
            username=self.config.username,
            password=self.config.password,
            use_socks_proxy=self.config.use_socks_proxy,
            socks_url=self.config.socks_url,
        )
        
        logger.info("✅ Test services initialized")
    
    async def cleanup(self):
        """Cleanup test services."""
        if self.browse_service:
            await self.browse_service.close()
        if self.checkout_service:
            await self.checkout_service.close()
        logger.info("🧹 Test cleanup completed")
    
    async def test_virtual_cart_basic_operations(self) -> bool:
        """Test basic virtual cart operations."""
        logger.info("\n" + "="*80)
        logger.info("TEST 1: Virtual Cart Basic Operations")
        logger.info("="*80)
        
        try:
            # Get sample items from API
            browse_result = await self.browse_service.list_items(
                APIV3BrowseParams(limit=5), self.test_user_id
            )
            
            if not browse_result.success:
                logger.error(f"❌ Failed to get sample items: {browse_result.error}")
                return False
            
            sample_items = browse_result.data.get("data", [])[:3]
            if len(sample_items) < 3:
                logger.error("❌ Not enough sample items available")
                return False
            
            # Test virtual cart operations
            virtual_cart = get_virtual_cart(self.test_user_id)
            virtual_cart.clear()  # Start fresh
            
            # Test 1.1: Add items
            logger.info("📝 Testing add items to virtual cart")
            for i, item in enumerate(sample_items):
                success = virtual_cart.add_item(item, quantity=i+1)
                if not success:
                    logger.error(f"❌ Failed to add item {i+1}")
                    return False
            
            # Verify items were added
            cart_items = virtual_cart.get_items()
            if len(cart_items) != 3:
                logger.error(f"❌ Expected 3 items, got {len(cart_items)}")
                return False
            
            total_quantity = virtual_cart.get_item_count()
            expected_quantity = 1 + 2 + 3  # 6 total items
            if total_quantity != expected_quantity:
                logger.error(f"❌ Expected {expected_quantity} total items, got {total_quantity}")
                return False
            
            logger.info(f"✅ Added {len(cart_items)} unique items ({total_quantity} total)")
            
            # Test 1.2: Update quantity
            logger.info("📝 Testing quantity updates")
            first_item_id = sample_items[0].get("_id")
            virtual_cart.add_item(sample_items[0], quantity=2)  # Add 2 more
            
            updated_item = virtual_cart.items[first_item_id]
            if updated_item.quantity != 3:  # 1 + 2 = 3
                logger.error(f"❌ Expected quantity 3, got {updated_item.quantity}")
                return False
            
            logger.info("✅ Quantity update successful")
            
            # Test 1.3: Remove items
            logger.info("📝 Testing item removal")
            second_item_id = sample_items[1].get("_id")
            virtual_cart.remove_item(second_item_id, quantity=1)  # Remove 1 of 2
            
            if virtual_cart.items[second_item_id].quantity != 1:
                logger.error("❌ Partial removal failed")
                return False
            
            virtual_cart.remove_item(second_item_id)  # Remove completely
            if second_item_id in virtual_cart.items:
                logger.error("❌ Complete removal failed")
                return False
            
            logger.info("✅ Item removal successful")
            
            # Test 1.4: Cart summary
            logger.info("📝 Testing cart summary")
            summary = virtual_cart.get_summary()
            
            logger.info(f"📊 Cart Summary:")
            logger.info(f"   Unique items: {summary.unique_items}")
            logger.info(f"   Total items: {summary.total_items}")
            logger.info(f"   Total price: ${summary.total_price:.2f}")
            logger.info(f"   Average price: ${summary.average_price:.2f}")
            
            if summary.unique_items != 2:  # Should have 2 items left
                logger.error(f"❌ Expected 2 unique items, got {summary.unique_items}")
                return False
            
            logger.info("✅ Virtual cart basic operations test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Virtual cart test failed: {e}", exc_info=True)
            return False
    
    async def test_checkout_process(self) -> bool:
        """Test the complete checkout process."""
        logger.info("\n" + "="*80)
        logger.info("TEST 2: Checkout Process")
        logger.info("="*80)
        
        try:
            # Setup virtual cart with items
            virtual_cart = get_virtual_cart(self.test_user_id)
            virtual_cart.clear()
            
            # Get fresh items
            browse_result = await self.browse_service.list_items(
                APIV3BrowseParams(limit=3), self.test_user_id
            )
            
            if not browse_result.success:
                logger.error(f"❌ Failed to get items for checkout test")
                return False
            
            items = browse_result.data.get("data", [])[:2]
            
            # Add items to virtual cart
            for item in items:
                virtual_cart.add_item(item, quantity=1)
            
            logger.info(f"📦 Virtual cart prepared with {len(items)} items")
            
            # Perform checkout
            logger.info("🛒 Starting checkout process")
            checkout_result = await self.checkout_service.checkout(
                user_id=self.test_user_id,
                create_order=False,  # Don't create order for test
                clear_virtual_cart=False,  # Keep for validation
            )
            
            # Analyze checkout results
            logger.info("📊 Checkout Results:")
            logger.info(f"   Success: {'✅' if checkout_result.success else '❌'}")
            
            if checkout_result.success:
                validation = checkout_result.validation_result
                logger.info(f"   Virtual items: {len(validation.virtual_items)}")
                logger.info(f"   API items: {len(validation.api_items)}")
                logger.info(f"   Price match: {'✅' if validation.price_match else '❌'}")
                logger.info(f"   Count match: {'✅' if validation.item_count_match else '❌'}")
                logger.info(f"   IDs match: {'✅' if validation.item_ids_match else '❌'}")
                
                if validation.discrepancies:
                    logger.warning(f"   Discrepancies: {validation.discrepancies}")
                
                logger.info("✅ Checkout process test passed")
                return True
            else:
                logger.error(f"❌ Checkout failed: {checkout_result.error}")
                if checkout_result.validation_result.discrepancies:
                    logger.error(f"   Discrepancies: {checkout_result.validation_result.discrepancies}")
                return False
            
        except Exception as e:
            logger.error(f"❌ Checkout test failed: {e}", exc_info=True)
            return False
    
    async def test_edge_cases(self) -> bool:
        """Test edge cases and error handling."""
        logger.info("\n" + "="*80)
        logger.info("TEST 3: Edge Cases and Error Handling")
        logger.info("="*80)
        
        try:
            # Test 3.1: Empty cart checkout
            logger.info("📝 Testing empty cart checkout")
            empty_cart = get_virtual_cart("empty_test_user")
            empty_cart.clear()
            
            empty_checkout = await self.checkout_service.checkout("empty_test_user")
            if empty_checkout.success:
                logger.error("❌ Empty cart checkout should fail")
                return False
            
            logger.info("✅ Empty cart checkout correctly failed")
            
            # Test 3.2: Invalid item handling
            logger.info("📝 Testing invalid item handling")
            test_cart = get_virtual_cart("invalid_test_user")
            test_cart.clear()
            
            # Try to add invalid item
            invalid_item = {"_id": "", "bin": "invalid", "price": "not_a_number"}
            success = test_cart.add_item(invalid_item)
            if success:
                logger.error("❌ Invalid item should not be added")
                return False
            
            logger.info("✅ Invalid item correctly rejected")
            
            # Test 3.3: Large quantity handling
            logger.info("📝 Testing large quantity handling")
            browse_result = await self.browse_service.list_items(
                APIV3BrowseParams(limit=1), self.test_user_id
            )
            
            if browse_result.success and browse_result.data.get("data"):
                large_qty_cart = get_virtual_cart("large_qty_test")
                large_qty_cart.clear()
                
                item = browse_result.data["data"][0]
                large_qty_cart.add_item(item, quantity=100)
                
                summary = large_qty_cart.get_summary()
                if summary.total_items != 100:
                    logger.error(f"❌ Large quantity not handled correctly")
                    return False
                
                logger.info("✅ Large quantity handled correctly")
            
            logger.info("✅ Edge cases test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Edge cases test failed: {e}", exc_info=True)
            return False
    
    async def test_data_persistence(self) -> bool:
        """Test virtual cart data persistence."""
        logger.info("\n" + "="*80)
        logger.info("TEST 4: Data Persistence")
        logger.info("="*80)
        
        try:
            # Create cart with items
            persistence_user = "persistence_test_user"
            cart1 = get_virtual_cart(persistence_user)
            cart1.clear()
            
            # Get sample item
            browse_result = await self.browse_service.list_items(
                APIV3BrowseParams(limit=1), self.test_user_id
            )
            
            if not browse_result.success or not browse_result.data.get("data"):
                logger.warning("⚠️  Skipping persistence test - no sample items")
                return True
            
            item = browse_result.data["data"][0]
            cart1.add_item(item, quantity=3)
            
            original_count = cart1.get_item_count()
            original_price = cart1.get_total_price()
            
            # Get new cart instance (should load from storage)
            cart2 = get_virtual_cart(persistence_user)
            
            if cart2.get_item_count() != original_count:
                logger.error(f"❌ Persistence failed: count mismatch")
                return False
            
            if abs(cart2.get_total_price() - original_price) > 0.01:
                logger.error(f"❌ Persistence failed: price mismatch")
                return False
            
            logger.info("✅ Data persistence test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Persistence test failed: {e}", exc_info=True)
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all test suites."""
        logger.info("🚀 Starting Comprehensive Virtual Cart Test Suite")
        logger.info("="*80)
        
        await self.setup()
        
        tests = [
            ("Virtual Cart Basic Operations", self.test_virtual_cart_basic_operations),
            ("Checkout Process", self.test_checkout_process),
            ("Edge Cases", self.test_edge_cases),
            ("Data Persistence", self.test_data_persistence),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASSED" if result else "❌ FAILED"
                logger.info(f"\n{test_name}: {status}")
            except Exception as e:
                results[test_name] = False
                logger.error(f"\n{test_name}: ❌ FAILED with exception: {e}")
        
        await self.cleanup()
        
        # Summary
        logger.info("\n" + "="*80)
        logger.info("📊 TEST SUITE SUMMARY")
        logger.info("="*80)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"  {test_name}: {status}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Virtual cart system is working correctly.")
        else:
            logger.error(f"❌ {total - passed} tests failed. Please review the issues above.")
        
        return results


async def main():
    """Main test execution."""
    test_suite = VirtualCartTestSuite()
    results = await test_suite.run_all_tests()
    
    # Return appropriate exit code
    all_passed = all(results.values())
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
