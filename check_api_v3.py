#!/usr/bin/env python3
"""
Quick API v3 Status Check

Run this script to quickly verify if API v3 is working correctly.
"""
import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

load_dotenv()


def check_port(port: int) -> bool:
    """Quick check if port is open"""
    import socket

    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(("127.0.0.1", port))
        sock.close()
        return result == 0
    except:
        return False


async def quick_test():
    """Quick test of API v3"""
    from api_v3.config import get_api_v3_config_from_env
    from api_v3.services import APIV3BrowseService

    config = get_api_v3_config_from_env()
    if not config:
        return False, "No configuration found"

    service = APIV3BrowseService(
        base_url=config.base_url,
        username=config.username,
        password=config.password,
        use_socks_proxy=config.use_socks_proxy,
        socks_url=config.socks_url,
    )

    try:
        response = await service.list_items()
        await service.close()

        if response.success:
            return True, f"Retrieved {len(response.data.get('data', []))} items"
        else:
            return False, response.error
    except Exception as e:
        await service.close()
        return False, str(e)


def main():
    print("🔍 Quick API v3 Status Check")
    print("=" * 50)

    # Check Tor ports
    print("\n📡 Tor Status:")
    tor_9150 = check_port(9150)
    tor_9050 = check_port(9050)

    print(f"  Port 9150 (Tor Browser): {'✅ Open' if tor_9150 else '❌ Closed'}")
    print(f"  Port 9050 (System Tor):  {'✅ Open' if tor_9050 else '❌ Closed'}")

    if not tor_9150 and not tor_9050:
        print("\n❌ No Tor service detected!")
        print("   Please start Tor Browser or system Tor")
        return 1

    # Get configured port
    socks_url = os.getenv("SOCKS_URL", "")
    configured_port = "9150" if "9150" in socks_url else "9050"
    configured_open = check_port(int(configured_port))

    print(f"\n⚙️  Configuration:")
    print(f"  Configured port: {configured_port}")
    print(f"  Status: {'✅ Available' if configured_open else '❌ Not available'}")

    if not configured_open:
        available_port = "9150" if tor_9150 else "9050"
        print(f"\n⚠️  Mismatch detected!")
        print(
            f"   Your .env uses port {configured_port}, but Tor is on port {available_port}"
        )
        print(f"   Update .env with: SOCKS_URL=socks5h://127.0.0.1:{available_port}")
        return 1

    # Test API v3
    print(f"\n🧪 Testing API v3...")
    try:
        success, message = asyncio.run(quick_test())

        if success:
            print(f"✅ API v3 is working!")
            print(f"   {message}")
            print("\n🎉 All systems operational")
            return 0
        else:
            print(f"❌ API v3 test failed")
            print(f"   Error: {message}")
            return 1
    except Exception as e:
        print(f"❌ Test error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
