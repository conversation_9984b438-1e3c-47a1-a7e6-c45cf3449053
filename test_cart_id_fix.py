#!/usr/bin/env python3
"""
Test script to verify that the cart ID conversion fix is working correctly.

This script tests that card IDs (which are string hashes) are properly handled
without being incorrectly converted to integers.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv
load_dotenv()

from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams
from api_v3.services.virtual_cart import get_virtual_cart
from api_v3.config import get_api_v3_config_from_env

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_card_id_handling():
    """Test that card IDs are properly handled as strings."""
    logger.info("🧪 Testing Card ID Handling Fix")
    logger.info("=" * 50)
    
    try:
        # Get API v3 configuration
        config = get_api_v3_config_from_env()
        if not config:
            logger.error("❌ API v3 configuration not found")
            return False
        
        # Initialize browse service
        browse_service = APIV3BrowseService(
            base_url=config.base_url,
            username=config.username,
            password=config.password,
            use_socks_proxy=config.use_socks_proxy,
            socks_url=config.socks_url,
        )
        
        # Get some sample items
        logger.info("🔍 Fetching sample items...")
        browse_result = await browse_service.list_items(
            APIV3BrowseParams(limit=3), "test_user"
        )
        
        if not browse_result.success:
            logger.error(f"❌ Failed to browse items: {browse_result.error}")
            return False
        
        items = browse_result.data.get("data", [])
        if not items:
            logger.error("❌ No items found")
            return False
        
        logger.info(f"✅ Found {len(items)} items")
        
        # Test virtual cart with string card IDs
        logger.info("🛒 Testing virtual cart with string card IDs...")
        virtual_cart = get_virtual_cart("test_user_id_fix")
        virtual_cart.clear()
        
        # Add items to virtual cart
        for i, item in enumerate(items[:2]):
            card_id = item.get("_id")
            if not card_id:
                logger.warning(f"⚠️  Item {i+1} has no ID, skipping")
                continue
            
            logger.info(f"📝 Adding item with ID: {card_id} (type: {type(card_id).__name__})")
            
            # Verify card ID is a string
            if not isinstance(card_id, str):
                logger.error(f"❌ Card ID should be string, got {type(card_id).__name__}")
                return False
            
            # Verify card ID looks like a hash (40 characters, hexadecimal)
            if len(card_id) != 40 or not all(c in '0123456789abcdef' for c in card_id.lower()):
                logger.warning(f"⚠️  Card ID doesn't look like expected hash format: {card_id}")
            
            # Add to virtual cart
            success = virtual_cart.add_item(item, quantity=1)
            if not success:
                logger.error(f"❌ Failed to add item {card_id} to virtual cart")
                return False
            
            logger.info(f"✅ Successfully added item {card_id}")
        
        # Test cart operations
        cart_items = virtual_cart.get_items()
        logger.info(f"🛒 Virtual cart contains {len(cart_items)} items")
        
        for item in cart_items:
            logger.info(f"   • {item.item_id} - {item.name} (${item.price})")
        
        # Test cart summary
        summary = virtual_cart.get_summary()
        logger.info(f"📊 Cart Summary: {summary.total_items} items, ${summary.total_price:.2f} total")
        
        # Clean up
        await browse_service.close()
        
        logger.info("✅ All card ID handling tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def test_string_vs_int_conversion():
    """Test that string card IDs don't cause integer conversion errors."""
    logger.info("\n🔢 Testing String vs Integer Conversion")
    logger.info("=" * 50)
    
    # Test card ID that would cause the original error
    problematic_card_id = "0415a6e7e2d8f45e716f204d1403220eacb3e75f"
    
    logger.info(f"🧪 Testing problematic card ID: {problematic_card_id}")
    
    try:
        # This should NOT raise an exception anymore
        # (Previously this would fail with: invalid literal for int() with base 10)
        
        # Test that we can handle it as a string
        logger.info(f"   Card ID as string: {problematic_card_id}")
        logger.info(f"   Length: {len(problematic_card_id)}")
        logger.info(f"   Type: {type(problematic_card_id).__name__}")
        
        # Test that trying to convert to int would fail (as expected)
        try:
            int_conversion = int(problematic_card_id)
            logger.error(f"❌ Unexpected: card ID converted to int: {int_conversion}")
            return False
        except ValueError as e:
            logger.info(f"✅ Expected: int conversion fails with ValueError: {e}")
        
        # Test virtual cart with this ID
        virtual_cart = get_virtual_cart("test_string_conversion")
        virtual_cart.clear()
        
        # Create a mock item with this card ID
        mock_item = {
            "_id": problematic_card_id,
            "bin": "123456",
            "name": "Test Card",
            "country": "Test Country",
            "price": 10.0,
            "brand": "Test Brand",
            "type": "Test Type",
            "level": "Test Level",
            "quality": "Test Quality",
            "expiry": "12/25"
        }
        
        # This should work without errors
        success = virtual_cart.add_item(mock_item, quantity=1)
        if not success:
            logger.error("❌ Failed to add mock item to virtual cart")
            return False
        
        logger.info("✅ Successfully added item with problematic card ID")
        
        # Verify the item is in the cart
        cart_items = virtual_cart.get_items()
        if len(cart_items) != 1:
            logger.error(f"❌ Expected 1 item in cart, got {len(cart_items)}")
            return False
        
        cart_item = cart_items[0]
        if cart_item.item_id != problematic_card_id:
            logger.error(f"❌ Card ID mismatch: expected {problematic_card_id}, got {cart_item.item_id}")
            return False
        
        logger.info("✅ String vs integer conversion test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ String conversion test failed: {e}", exc_info=True)
        return False


async def main():
    """Run all tests."""
    logger.info("🚀 Starting Card ID Fix Tests")
    logger.info("=" * 60)
    
    test1_result = await test_card_id_handling()
    test2_result = await test_string_vs_int_conversion()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"Card ID Handling Test: {'✅ PASSED' if test1_result else '❌ FAILED'}")
    logger.info(f"String vs Int Test: {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    overall_success = test1_result and test2_result
    logger.info(f"\nOverall Result: {'🎉 ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("\n✅ The card ID conversion fix is working correctly!")
        logger.info("   Card IDs are now properly handled as string hashes.")
        logger.info("   The 'invalid literal for int()' error should be resolved.")
    else:
        logger.error("\n❌ Some tests failed. Please review the errors above.")
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
