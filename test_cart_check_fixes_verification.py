#!/usr/bin/env python3
"""
Verify <PERSON><PERSON> and Check Card Fixes

This script verifies that both cart display and check card button issues are fixed.
"""

import asyncio
import sys
import os
from typing import Dict, Any
sys.path.insert(0, '.')

def test_uiformatter_import_fix():
    """Test that UIFormatter import issue is fixed"""
    print("🔍 TESTING UIFORMATTER IMPORT FIX")
    print("=" * 60)
    
    try:
        # Read the orders handlers file
        with open('handlers/orders_handlers.py', 'r') as f:
            orders_code = f.read()
        
        # Check if UIFormatter import is removed
        has_uiformatter_import = 'from utils.ui_components import UIFormatter' in orders_code
        print(f"📋 UIFormatter import check:")
        print(f"   • Has UIFormatter import: {'❌' if has_uiformatter_import else '✅'}")
        
        if has_uiformatter_import:
            print(f"   ❌ UIFormatter import still present - this will cause errors")
            return False
        else:
            print(f"   ✅ UIFormatter import removed - error should be fixed")
        
        # Check if _format_order_details method still has proper structure
        format_start = orders_code.find('def _format_order_details(')
        if format_start != -1:
            format_end = orders_code.find('\n    def', format_start)
            if format_end == -1:
                format_end = orders_code.find('\n    async def', format_start)
            
            if format_end != -1:
                format_method = orders_code[format_start:format_end]
                
                has_try_catch = 'try:' in format_method and 'except Exception as e:' in format_method
                has_error_return = 'Error loading card details' in format_method
                has_order_access = 'order.get(' in format_method
                
                print(f"\n📋 _format_order_details method check:")
                print(f"   • Has try-catch: {'✅' if has_try_catch else '❌'}")
                print(f"   • Has error return: {'✅' if has_error_return else '❌'}")
                print(f"   • Accesses order data: {'✅' if has_order_access else '❌'}")
                
                return has_try_catch and has_error_return and has_order_access
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing UIFormatter fix: {e}")
        return False

def test_order_details_formatting():
    """Test order details formatting without database"""
    print(f"\n🔧 TESTING ORDER DETAILS FORMATTING")
    print("=" * 60)
    
    try:
        # Import the orders handlers
        from handlers.orders_handlers import OrdersHandlers
        
        # Create a test order object
        test_order = {
            "_id": "test_order_123",
            "product_id": "card_456",
            "status": "active",
            "price": 5.50,
            "bank": "WELLS FARGO BANK, N.A.",
            "brand": "VISA",
            "type": "CREDIT",
            "level": "CLASSIC",
            "country": "US",
            "state": "CA",
            "city": "San Francisco",
            "zip": "94102",
            "createdAt": "2024-01-15T10:30:00Z",
            "start_Date": "2024-01-15T10:30:00Z",
            "refundAt": None
        }
        
        print("📋 Testing order details formatting:")
        
        # Create orders handler instance
        orders_handler = OrdersHandlers()
        
        # Test the formatting method
        formatted_details = orders_handler._format_order_details(test_order)
        
        print(f"   • Formatting successful: ✅")
        print(f"   • Output length: {len(formatted_details)} characters")
        
        # Check for error message
        if "Error loading card details" in formatted_details:
            print(f"   ❌ Still contains error message")
            print(f"   📋 Output preview: {formatted_details[:200]}...")
            return False
        else:
            print(f"   ✅ No error message in output")
        
        # Check for expected content
        expected_content = [
            "Status:",
            "Card Information",
            "Financial Details",
            "Order Timeline",
            "Technical Info"
        ]
        
        content_checks = []
        for content in expected_content:
            present = content in formatted_details
            content_checks.append(present)
            status = "✅" if present else "❌"
            print(f"   {status} Contains '{content}'")
        
        success_rate = sum(content_checks) / len(content_checks)
        print(f"\n📊 Content check success rate: {success_rate*100:.1f}%")
        
        return success_rate >= 0.8  # 80% of expected content should be present
        
    except Exception as e:
        print(f"❌ Error testing order details formatting: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_check_card_callback_structure():
    """Test check card callback structure"""
    print(f"\n🔄 TESTING CHECK CARD CALLBACK STRUCTURE")
    print("=" * 60)
    
    try:
        # Read orders handlers code
        with open('handlers/orders_handlers.py', 'r') as f:
            orders_code = f.read()
        
        print("📋 Check card callback analysis:")
        
        # Check cb_check_card method structure
        if 'async def cb_check_card(' in orders_code:
            check_start = orders_code.find('async def cb_check_card(')
            check_end = orders_code.find('\n    async def', check_start)
            if check_end == -1:
                check_end = orders_code.find('\n    def', check_start)
            
            if check_end != -1:
                check_method = orders_code[check_start:check_end]
                
                # Check key components
                components = [
                    ('Callback data parsing', 'callback.data or "").split(":"'),
                    ('Parts validation', 'len(parts) not in (4, 5)'),
                    ('Expiry check', 'now_ts >= expiry_ts'),
                    ('Order ID resolution', '_resolve_external_order_id'),
                    ('API call', 'check_order('),
                    ('Success handling', 'getattr(resp, "success"'),
                    ('Error handling', 'except Exception as e:'),
                    ('Timeout handling', 'timeout'),
                    ('Not found handling', 'not found'),
                ]
                
                success_count = 0
                for component_name, pattern in components:
                    present = pattern in check_method
                    status = "✅" if present else "❌"
                    print(f"   {status} {component_name}")
                    if present:
                        success_count += 1
                
                success_rate = success_count / len(components)
                print(f"\n📊 Component check success rate: {success_rate*100:.1f}%")
                
                return success_rate >= 0.8
        
        return False
        
    except Exception as e:
        print(f"❌ Error testing check card callback: {e}")
        return False

def test_router_registration():
    """Test router registration for check card callback"""
    print(f"\n📋 TESTING ROUTER REGISTRATION")
    print("=" * 60)
    
    try:
        # Read orders handlers code
        with open('handlers/orders_handlers.py', 'r') as f:
            orders_code = f.read()
        
        print("📋 Router registration analysis:")
        
        # Check get_orders_router function
        if 'def get_orders_router(' in orders_code:
            router_start = orders_code.find('def get_orders_router(')
            router_end = orders_code.find('\n\n', router_start)
            if router_end == -1:
                router_end = len(orders_code)
            
            router_function = orders_code[router_start:router_end]
            
            # Check registrations
            registrations = [
                ('View card callback', 'cb_view_purchased_card', 'orders:view_card:'),
                ('Check card callback', 'cb_check_card', 'orders:check:'),
                ('Orders menu callback', 'cb_orders_menu', 'menu:orders'),
            ]
            
            success_count = 0
            for reg_name, handler_name, callback_pattern in registrations:
                has_handler = handler_name in router_function
                has_pattern = callback_pattern in router_function
                registered = has_handler and has_pattern
                
                status = "✅" if registered else "❌"
                print(f"   {status} {reg_name}")
                if not registered:
                    print(f"      • Handler present: {'✅' if has_handler else '❌'}")
                    print(f"      • Pattern present: {'✅' if has_pattern else '❌'}")
                
                if registered:
                    success_count += 1
            
            success_rate = success_count / len(registrations)
            print(f"\n📊 Registration success rate: {success_rate*100:.1f}%")
            
            return success_rate >= 0.8
        
        return False
        
    except Exception as e:
        print(f"❌ Error testing router registration: {e}")
        return False

def main():
    """Run comprehensive fix verification"""
    print("🔍 CART AND CHECK CARD FIXES VERIFICATION")
    print("=" * 60)
    
    tests = [
        ("UIFormatter Import Fix", test_uiformatter_import_fix),
        ("Order Details Formatting", test_order_details_formatting),
        ("Check Card Callback Structure", test_check_card_callback_structure),
        ("Router Registration", test_router_registration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FIXES VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = [name for name, success in results if success]
    failed = [name for name, success in results if not success]
    
    print(f"✅ PASSED TESTS: {len(passed)}/{len(results)}")
    for test_name in passed:
        print(f"   • {test_name}")
    
    if failed:
        print(f"\n❌ FAILED TESTS: {len(failed)}")
        for test_name in failed:
            print(f"   • {test_name}")
    
    success_rate = len(passed) / len(results) * 100
    print(f"\n📊 SUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"\n🎉 FIXES VERIFICATION: ✅ PASSED")
        print(f"\n📋 WHAT WAS FIXED:")
        print(f"✅ Removed UIFormatter import causing 'Error loading card details'")
        print(f"✅ Verified check card callback structure is correct")
        print(f"✅ Confirmed router registration is working")
        print(f"✅ Order details formatting now works without errors")
        
        print(f"\n📋 EXPECTED RESULTS:")
        print(f"✅ No more 'Error loading card details' in order views")
        print(f"✅ Check Card Status button should work correctly")
        print(f"✅ Cart display should show proper card information")
        print(f"✅ All error handling should work as expected")
    else:
        print(f"\n❌ FIXES VERIFICATION: FAILED")
        print(f"❌ Some issues remain unresolved")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
