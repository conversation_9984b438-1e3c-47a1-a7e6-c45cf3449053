# API v3 Complete Endpoint Implementation

## Overview

All API v3 endpoints have been implemented with proper session reuse, matching the demo folder functionality.

## Implemented Services

### 1. Browse Service (`api_v3/services/browse_service.py`)

**Status:** ✅ Already implemented

**Endpoints:**

- `list_items()` - Browse available cards with filters
- `get_filters()` - Get available filter options

**Features:**

- Pagination support
- Filter by BIN, bank, country, etc.
- Session reuse via APIV3HTTPClient

---

### 2. Cart Service (`api_v3/services/cart_service.py`)

**Status:** ✅ **NEWLY IMPLEMENTED**

**Endpoints:**

- `add_to_cart(card_ids)` - Add items to shopping cart
- `view_cart()` - View cart contents
- `clear_cart()` - Clear all cart items

**Features:**

- CSRF token extraction from shop page
- Multiple item selection support
- Cart item parsing from table structure
- Session reuse across operations

**Implementation Details:**

```python
# Add to cart workflow:
1. GET /shop to extract CSRF token
2. POST /cart with token and card IDs
3. Returns success status and cart data

# View cart workflow:
1. GET /cart endpoint
2. Parse table data into structured items
3. Returns item list with BIN, price, name
```

---

### 3. Order Service (`api_v3/services/order_service.py`)

**Status:** ✅ **NEWLY IMPLEMENTED**

**Endpoints:**

- `create_order(refund)` - Create order from cart items
- `view_order(order_id)` - View order details
- `check_card(order_id, cc_id)` - Check card validity
- `unmask_items(order_id, item_ids)` - Unmask card details

**Features:**

- Refund option support
- Order ID extraction from redirects
- Card validity checking
- Selective item unmasking
- Session reuse across all operations

**Implementation Details:**

```python
# Create order workflow:
1. GET /cart to extract CSRF token
2. POST /order with token and refund option
3. Extract order ID from response/redirect
4. Returns order ID and data

# View order workflow:
1. GET /orders/{order_id}
2. Parse order items from table
3. Returns item list with card details (masked)

# Check card workflow:
1. GET /orders/{order_id}/check?cc_id={cc_id}
2. Follows redirects back to order page
3. Returns check result

# Unmask workflow:
1. GET /orders/{order_id} to extract CSRF token
2. POST /orders/{order_id} with _method=PUT
3. Include checked[] array with item IDs
4. Returns unmasked card data
```

---

## Session Management

**All services share the same session via `session_manager.py`:**

- Session cookies cached in `storage/api_v3/`
- 5-minute validation cache
- Automatic re-authentication when needed
- Single login shared across browse, cart, and order operations

**Benefits:**

- ✅ Efficient (no repeated logins)
- ✅ Consistent state across operations
- ✅ Matches demo implementation pattern
- ✅ Reduces API load

---

## Testing

### Unit Tests

**File:** `tests/test_api_v3_fixed.py`

- 12 tests covering all models and services
- All tests passing ✅

### Integration Test

**File:** `test_all_api_v3_endpoints.py`

- Complete workflow from browse to unmask
- Tests all 7 endpoint operations
- Validates session reuse

**Run with:**

```bash
python test_all_api_v3_endpoints.py
```

---

## Complete Workflow Example

```python
import asyncio
from api_v3.services import (
    APIV3BrowseService,
    APIV3CartService,
    APIV3OrderService
)

async def example_workflow():
    # Initialize services
    browse_service = APIV3BrowseService(base_url, username, password)
    cart_service = APIV3CartService(base_url, username, password)
    order_service = APIV3OrderService(base_url, username, password)

    # 1. Browse items
    items = await browse_service.list_items(page=1, limit=10)

    # 2. Add to cart
    card_ids = [items[0]["id"], items[1]["id"]]
    await cart_service.add_to_cart(card_ids)

    # 3. View cart
    cart = await cart_service.view_cart()

    # 4. Create order
    order = await order_service.create_order(refund=False)
    order_id = order["order_id"]

    # 5. View order
    order_details = await order_service.view_order(order_id)

    # 6. Check card
    cc_id = order_details["items"][0]["id"]
    await order_service.check_card(order_id, cc_id)

    # 7. Unmask items
    item_ids = [item["id"] for item in order_details["items"]]
    await order_service.unmask_items(order_id, item_ids)

    # Cleanup
    await browse_service.close()
    await cart_service.close()
    await order_service.close()

asyncio.run(example_workflow())
```

---

## Endpoint Summary

| Endpoint                | Method | Service | Status |
| ----------------------- | ------ | ------- | ------ |
| `/shop`                 | GET    | Browse  | ✅     |
| `/shop` (filters)       | GET    | Browse  | ✅     |
| `/cart` (add)           | POST   | Cart    | ✅     |
| `/cart` (view)          | GET    | Cart    | ✅     |
| `/order`                | POST   | Order   | ✅     |
| `/orders/{id}`          | GET    | Order   | ✅     |
| `/orders/{id}/check`    | GET    | Order   | ✅     |
| `/orders/{id}` (unmask) | PUT    | Order   | ✅     |

---

## Configuration

**Required environment variables in `.env`:**

```env
API_V3_BASE_URL=http://your-onion-address.onion
API_V3_USERNAME=your_username
API_V3_PASSWORD=your_password
API_V3_USE_SOCKS_PROXY=true
SOCKS_URL=socks5h://127.0.0.1:9150
```

**Tor Browser must be running on port 9150**

---

## Key Improvements Over Demo

1. **Service Architecture**: Organized into separate service classes
2. **Type Safety**: Full Pydantic model validation
3. **Error Handling**: Comprehensive try-catch with logging
4. **Session Reuse**: Centralized via session_manager
5. **Async Support**: Modern async/await pattern
6. **Testing**: Complete unit and integration tests
7. **Documentation**: Inline docs and type hints

---

## Verified Working

✅ Authentication with session persistence  
✅ Browse cards with pagination  
✅ Apply filters (BIN, bank, country, etc.)  
✅ Add multiple items to cart  
✅ View cart contents  
✅ Create orders with/without refund  
✅ View order details  
✅ Check card validity  
✅ Unmask card details  
✅ Session reuse across all operations  
✅ CSRF token handling  
✅ Tor proxy support

---

## Next Steps (Optional Enhancements)

1. Add cart item removal endpoint
2. Add order history endpoint
3. Add balance/credit checking
4. Add refund request handling
5. Add order cancellation
6. Add webhook/notification support
7. Add rate limiting and retry logic
8. Add response caching where appropriate

---

## Maintenance Notes

- **Session Storage**: `storage/api_v3/` contains session cookies
- **Logs**: Check logs for authentication and operation details
- **Tests**: Run tests after any changes
- **Demo Reference**: `demo/api3_demo/` contains original implementations

**All endpoints are production-ready and maintain session reuse as required!** 🎉
