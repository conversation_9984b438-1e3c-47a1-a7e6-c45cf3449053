"""
API v3 Package

This package contains API v3 functionality based on the demo API implementation.
It includes:

- auth: Session-based authentication with form login
- config: API configuration management
- http: HTTP client with session handling
- services: Browse and filter services

This API uses form-based authentication and provides browse/filter functionality
with table-based data structure.
"""

__version__ = "3.0.0"
__author__ = "Bot v2 Team"

from .auth import LoginSession, SessionManager, get_authenticated_session
from .config import APIV3Config, create_api_v3_configuration, get_api_v3_config_from_env
from .http import APIV3HTTPClient
from .services import (
    APIV3BrowseService,
    APIV3BrowseParams,
    APIV3BrowseResponse,
    get_api_v3_browse_service,
)
from .adapter import APIV3Adapter, get_api_v3_adapter

__all__ = [
    # Auth
    "LoginSession",
    "SessionManager",
    "get_authenticated_session",
    # Config
    "APIV3Config",
    "create_api_v3_configuration",
    "get_api_v3_config_from_env",
    # HTTP
    "APIV3HTTPClient",
    # Services
    "APIV3BrowseService",
    "APIV3BrowseParams",
    "APIV3BrowseResponse",
    "get_api_v3_browse_service",
    # Adapter
    "APIV3Adapter",
    "get_api_v3_adapter",
]
