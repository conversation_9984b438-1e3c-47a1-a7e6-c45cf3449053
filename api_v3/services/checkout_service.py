"""
Checkout Service for API v3

Handles the checkout process that transfers virtual cart items to the actual API cart
and performs comprehensive validation between virtual and actual cart data.
"""

from __future__ import annotations

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .virtual_cart import <PERSON>Cart, VirtualCartItem, get_virtual_cart
from .cart_service import APIV3CartService
from .order_service import APIV3OrderService

logger = logging.getLogger(__name__)


@dataclass
class CheckoutValidationResult:
    """Result of checkout validation."""
    
    success: bool
    virtual_items: List[VirtualCartItem]
    api_items: List[Dict[str, Any]]
    total_virtual_price: float
    total_api_price: float
    price_match: bool
    item_count_match: bool
    item_ids_match: bool
    discrepancies: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for display."""
        return {
            "success": self.success,
            "virtual_cart": {
                "item_count": len(self.virtual_items),
                "total_price": self.total_virtual_price,
                "items": [item.to_dict() for item in self.virtual_items]
            },
            "api_cart": {
                "item_count": len(self.api_items),
                "total_price": self.total_api_price,
                "items": self.api_items
            },
            "validation": {
                "price_match": self.price_match,
                "item_count_match": self.item_count_match,
                "item_ids_match": self.item_ids_match,
                "discrepancies": self.discrepancies
            }
        }


@dataclass
class CheckoutResult:
    """Result of complete checkout process."""
    
    success: bool
    virtual_cart_summary: Dict[str, Any]
    api_cart_result: Dict[str, Any]
    validation_result: CheckoutValidationResult
    order_result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for display."""
        result = {
            "success": self.success,
            "virtual_cart_summary": self.virtual_cart_summary,
            "api_cart_result": self.api_cart_result,
            "validation": self.validation_result.to_dict(),
        }
        
        if self.order_result:
            result["order"] = self.order_result
        
        if self.error:
            result["error"] = self.error
            
        return result


class APIV3CheckoutService:
    """
    Checkout service that handles the complete checkout workflow.
    
    This service:
    1. Takes items from virtual cart
    2. Adds them to actual API cart
    3. Validates the transfer was successful
    4. Optionally creates an order
    5. Provides detailed verification results
    """
    
    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        """
        Initialize checkout service.
        
        Args:
            base_url: API base URL
            username: Authentication username
            password: Authentication password
            use_socks_proxy: Whether to use SOCKS proxy
            socks_url: SOCKS proxy URL
        """
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy
        self.socks_url = socks_url
        
        # Initialize API services
        self.cart_service = APIV3CartService(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks_proxy,
            socks_url=socks_url,
        )
        
        self.order_service = APIV3OrderService(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks_proxy,
            socks_url=socks_url,
        )
        
        self.logger = logging.getLogger(f"{__name__}.APIV3CheckoutService")
    
    async def checkout(
        self,
        user_id: str,
        create_order: bool = False,
        clear_virtual_cart: bool = True,
    ) -> CheckoutResult:
        """
        Perform complete checkout process.
        
        Args:
            user_id: User identifier
            create_order: Whether to create an order after adding to cart
            clear_virtual_cart: Whether to clear virtual cart after successful checkout
            
        Returns:
            CheckoutResult with detailed validation information
        """
        try:
            self.logger.info(f"🛒 Starting checkout for user {user_id}")
            
            # Step 1: Get virtual cart
            virtual_cart = get_virtual_cart(user_id)
            virtual_items = virtual_cart.get_items()
            
            if not virtual_items:
                return CheckoutResult(
                    success=False,
                    virtual_cart_summary={},
                    api_cart_result={},
                    validation_result=CheckoutValidationResult(
                        success=False,
                        virtual_items=[],
                        api_items=[],
                        total_virtual_price=0,
                        total_api_price=0,
                        price_match=False,
                        item_count_match=False,
                        item_ids_match=False,
                        discrepancies=["Virtual cart is empty"]
                    ),
                    error="Virtual cart is empty"
                )
            
            virtual_summary = virtual_cart.get_summary()
            self.logger.info(f"📋 Virtual cart: {virtual_summary.total_items} items, ${virtual_summary.total_price:.2f}")
            
            # Step 2: Clear API cart first
            await self.cart_service.clear_cart(user_id)
            self.logger.info("🗑️  Cleared API cart")
            
            # Step 3: Add virtual cart items to API cart
            cart_item_ids = virtual_cart.get_cart_data_for_api()
            self.logger.info(f"📤 Adding {len(cart_item_ids)} items to API cart")
            
            api_cart_result = await self.cart_service.add_to_cart(cart_item_ids, user_id)
            
            if not api_cart_result.get("success"):
                return CheckoutResult(
                    success=False,
                    virtual_cart_summary=virtual_summary.to_dict(),
                    api_cart_result=api_cart_result,
                    validation_result=CheckoutValidationResult(
                        success=False,
                        virtual_items=virtual_items,
                        api_items=[],
                        total_virtual_price=virtual_summary.total_price,
                        total_api_price=0,
                        price_match=False,
                        item_count_match=False,
                        item_ids_match=False,
                        discrepancies=["Failed to add items to API cart"]
                    ),
                    error=f"Failed to add items to API cart: {api_cart_result.get('error')}"
                )
            
            # Step 4: Validate cart transfer
            validation_result = await self._validate_cart_transfer(virtual_cart, user_id)
            
            # Step 5: Create order if requested
            order_result = None
            if create_order and validation_result.success:
                self.logger.info("📦 Creating order")
                order_result = await self.order_service.create_order(refund=False, user_id=user_id)
                
                if order_result.get("success"):
                    self.logger.info(f"✅ Order created: {order_result.get('order_id')}")
                else:
                    self.logger.warning(f"⚠️  Order creation failed: {order_result.get('error')}")
            
            # Step 6: Clear virtual cart if successful
            if clear_virtual_cart and validation_result.success:
                virtual_cart.clear()
                self.logger.info("🗑️  Cleared virtual cart")
            
            checkout_success = validation_result.success
            self.logger.info(f"🏁 Checkout {'✅ successful' if checkout_success else '❌ failed'}")
            
            return CheckoutResult(
                success=checkout_success,
                virtual_cart_summary=virtual_summary.to_dict(),
                api_cart_result=api_cart_result,
                validation_result=validation_result,
                order_result=order_result,
            )
            
        except Exception as e:
            self.logger.error(f"❌ Checkout failed with exception: {e}", exc_info=True)
            return CheckoutResult(
                success=False,
                virtual_cart_summary={},
                api_cart_result={},
                validation_result=CheckoutValidationResult(
                    success=False,
                    virtual_items=[],
                    api_items=[],
                    total_virtual_price=0,
                    total_api_price=0,
                    price_match=False,
                    item_count_match=False,
                    item_ids_match=False,
                    discrepancies=[f"Checkout exception: {str(e)}"]
                ),
                error=str(e)
            )
    
    async def _validate_cart_transfer(
        self,
        virtual_cart: VirtualCart,
        user_id: str,
    ) -> CheckoutValidationResult:
        """
        Validate that virtual cart items were correctly transferred to API cart.
        
        Args:
            virtual_cart: Virtual cart instance
            user_id: User identifier
            
        Returns:
            CheckoutValidationResult with detailed comparison
        """
        try:
            self.logger.info("🔍 Validating cart transfer")
            
            # Get virtual cart data
            virtual_items = virtual_cart.get_items()
            virtual_total = virtual_cart.get_total_price()
            virtual_item_ids = set(virtual_cart.get_cart_data_for_api())
            
            # Get API cart data
            api_cart_response = await self.cart_service.view_cart(user_id)
            
            if not api_cart_response.get("success"):
                return CheckoutValidationResult(
                    success=False,
                    virtual_items=virtual_items,
                    api_items=[],
                    total_virtual_price=virtual_total,
                    total_api_price=0,
                    price_match=False,
                    item_count_match=False,
                    item_ids_match=False,
                    discrepancies=["Failed to retrieve API cart for validation"]
                )
            
            api_items = api_cart_response.get("items", [])
            api_item_ids = set(item.get("id", "") for item in api_items for _ in range(item.get("quantity", 1)))
            
            # Calculate API cart total (if price data is available)
            api_total = sum(float(item.get("price", 0)) * item.get("quantity", 1) for item in api_items)
            
            # Perform validation checks
            discrepancies = []
            
            # Check item counts
            virtual_count = len(virtual_item_ids)
            api_count = len(api_item_ids)
            item_count_match = virtual_count == api_count
            
            if not item_count_match:
                discrepancies.append(f"Item count mismatch: virtual={virtual_count}, api={api_count}")
            
            # Check item IDs
            item_ids_match = virtual_item_ids == api_item_ids
            
            if not item_ids_match:
                missing_in_api = virtual_item_ids - api_item_ids
                extra_in_api = api_item_ids - virtual_item_ids
                
                if missing_in_api:
                    discrepancies.append(f"Items missing in API cart: {list(missing_in_api)}")
                if extra_in_api:
                    discrepancies.append(f"Extra items in API cart: {list(extra_in_api)}")
            
            # Check prices (if available)
            price_match = abs(virtual_total - api_total) < 0.01  # Allow small floating point differences
            
            if not price_match and api_total > 0:
                discrepancies.append(f"Price mismatch: virtual=${virtual_total:.2f}, api=${api_total:.2f}")
            
            # Overall success
            validation_success = item_count_match and item_ids_match and (price_match or api_total == 0)
            
            if validation_success:
                self.logger.info("✅ Cart validation successful")
            else:
                self.logger.warning(f"⚠️  Cart validation failed: {discrepancies}")
            
            return CheckoutValidationResult(
                success=validation_success,
                virtual_items=virtual_items,
                api_items=api_items,
                total_virtual_price=virtual_total,
                total_api_price=api_total,
                price_match=price_match,
                item_count_match=item_count_match,
                item_ids_match=item_ids_match,
                discrepancies=discrepancies,
            )
            
        except Exception as e:
            self.logger.error(f"❌ Cart validation failed: {e}", exc_info=True)
            return CheckoutValidationResult(
                success=False,
                virtual_items=virtual_items if 'virtual_items' in locals() else [],
                api_items=[],
                total_virtual_price=virtual_total if 'virtual_total' in locals() else 0,
                total_api_price=0,
                price_match=False,
                item_count_match=False,
                item_ids_match=False,
                discrepancies=[f"Validation exception: {str(e)}"]
            )
    
    async def close(self):
        """Close the checkout service and clean up resources."""
        if hasattr(self.cart_service, 'close'):
            await self.cart_service.close()
        if hasattr(self.order_service, 'close'):
            await self.order_service.close()
