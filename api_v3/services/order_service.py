"""
API v3 Order Service

Handles order operations for API v3 (create order, view order, check cards, unmask).
Maintains session reuse for efficiency.
"""

from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional

from ..http.client import APIV3HTTPClient

logger = logging.getLogger(__name__)


class APIV3OrderService:
    """
    Order service for API v3.

    Handles order operations including:
    - Creating orders from cart
    - Viewing order details
    - Checking card validity
    - Unmasking card details
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy
        self.socks_url = socks_url

        # Create HTTP client (reuses session)
        self.client = APIV3HTTPClient(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks_proxy,
            socks_url=socks_url,
        )

        self.logger = logging.getLogger(f"{__name__}.APIV3OrderService")

    async def create_order(
        self,
        refund: bool = False,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create order from cart items.

        Args:
            refund: Whether to enable refund option
            user_id: User ID for logging

        Returns:
            Response with order ID and details
        """
        try:
            self.logger.info(f"Creating order for user {user_id} (refund={refund})")

            # First, get cart page to obtain CSRF token
            cart_response = await self.client.get(
                endpoint="cart",
                params={},
                timeout=30,
            )

            if not cart_response.get("success"):
                return {
                    "success": False,
                    "error": "Failed to get CSRF token from cart page",
                }

            # Extract token
            token = cart_response.get("data", {}).get("payload", {}).get("_token")
            if not token:
                self.logger.warning("No CSRF token found, attempting without token")

            # Prepare form data
            form_data = {
                "_token": token or "",
                "refund": "1" if refund else "0",
            }

            # POST to order endpoint
            response = await self.client.post(
                endpoint="order",
                data=form_data,
                timeout=60,
            )

            if response.get("success"):
                order_data = response.get("data", {})

                # Extract order ID from response
                order_id = self._extract_order_id(order_data)

                self.logger.info(f"Successfully created order: {order_id}")
                return {
                    "success": True,
                    "order_id": order_id,
                    "order_data": order_data,
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to create order: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error creating order: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def view_order(
        self,
        order_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        View order details.

        Args:
            order_id: Order ID to view
            user_id: User ID for logging

        Returns:
            Response with order details and items
        """
        try:
            self.logger.info(f"Viewing order {order_id} for user {user_id}")

            # GET order endpoint
            response = await self.client.get(
                endpoint=f"orders/{order_id}",
                params={},
                timeout=60,
            )

            if not response.get("success"):
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to view order: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

            # Extract order data
            order_data = response.get("data", {})

            # Parse order items
            order_items = self._parse_order_items(order_data)

            self.logger.info(f"Order {order_id} contains {len(order_items)} items")

            return {
                "success": True,
                "order_id": order_id,
                "items": order_items,
                "item_count": len(order_items),
                "raw_data": order_data,
            }

        except Exception as e:
            self.logger.error(f"Error viewing order: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def check_card(
        self,
        order_id: str,
        cc_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Check card validity.

        Args:
            order_id: Order ID
            cc_id: Card ID to check
            user_id: User ID for logging

        Returns:
            Response with card check result
        """
        try:
            self.logger.info(
                f"Checking card {cc_id} in order {order_id} for user {user_id}"
            )

            # GET check endpoint with query parameters
            response = await self.client.get(
                endpoint=f"orders/{order_id}/check",
                params={"cc_id": cc_id},
                timeout=60,
                allow_redirects=True,  # Follow redirect back to order page
            )

            if response.get("success"):
                check_data = response.get("data", {})

                self.logger.info(f"Card check completed for {cc_id}")
                return {
                    "success": True,
                    "order_id": order_id,
                    "cc_id": cc_id,
                    "check_data": check_data,
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to check card: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error checking card: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def unmask_items(
        self,
        order_id: str,
        item_ids: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Unmask card details.

        Args:
            order_id: Order ID
            item_ids: List of item IDs to unmask
            user_id: User ID for logging

        Returns:
            Response with unmasked card data
        """
        try:
            self.logger.info(
                f"Unmasking {len(item_ids)} items in order {order_id} for user {user_id}"
            )

            # First, get order page to obtain CSRF token
            order_response = await self.client.get(
                endpoint=f"orders/{order_id}",
                params={},
                timeout=30,
            )

            if not order_response.get("success"):
                return {
                    "success": False,
                    "error": "Failed to get CSRF token from order page",
                }

            # Extract token
            token = order_response.get("data", {}).get("payload", {}).get("_token")
            if not token:
                self.logger.warning("No CSRF token found, attempting without token")

            # Prepare form data for PUT request
            form_data = {
                "_token": token or "",
                "_method": "PUT",
                "checked[]": item_ids,  # Multiple IDs as array
                "target": "Unmask Selected",
            }

            # POST to order endpoint (with _method=PUT for update)
            response = await self.client.post(
                endpoint=f"orders/{order_id}",
                data=form_data,
                timeout=60,
            )

            if response.get("success"):
                unmask_data = response.get("data", {})

                self.logger.info(f"Successfully unmasked items in order {order_id}")
                return {
                    "success": True,
                    "order_id": order_id,
                    "unmasked_count": len(item_ids),
                    "unmask_data": unmask_data,
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to unmask items: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error unmasking items: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    def _extract_order_id(self, order_data: Dict[str, Any]) -> Optional[str]:
        """
        Extract order ID from order response.

        May be in URL redirect, response data, or headers.
        """
        # Check for redirect URL with order ID
        url = order_data.get("url", "")
        if "/orders/" in url:
            parts = url.split("/orders/")
            if len(parts) > 1:
                order_id = parts[1].split("/")[0].split("?")[0]
                return order_id

        # Check response data
        if "order_id" in order_data:
            return str(order_data["order_id"])

        return None

    def _parse_order_items(self, order_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Parse order items from API v3 response.

        Args:
            order_data: Raw order data from API

        Returns:
            List of order items
        """
        items = []

        # Order data might have table structure
        headers = order_data.get("headers", [])
        rows = order_data.get("rows", [])

        for row in rows:
            if not row:
                continue

            item = {}

            # Extract item ID from checkbox
            if len(row) > 0 and isinstance(row[0], dict):
                item["id"] = row[0].get("input_value", "")

            # Map columns to fields
            for col_idx, cell in enumerate(row):
                if col_idx >= len(headers):
                    break

                if not isinstance(cell, dict):
                    continue

                header = headers[col_idx].strip()
                text = cell.get("text", "").strip()

                if header == "Card Number":
                    item["card_number"] = text
                elif header == "BIN":
                    item["bin"] = text
                elif header == "Expiry":
                    item["expiry"] = text
                elif header == "CVV":
                    item["cvv"] = text
                elif header == "Status":
                    item["status"] = text

            if item:
                items.append(item)

        return items

    async def close(self):
        """Close the order service and HTTP client."""
        await self.client.close()
