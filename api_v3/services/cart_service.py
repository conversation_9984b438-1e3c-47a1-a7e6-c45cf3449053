"""
API v3 Cart Service

Handles cart operations for API v3 (add to cart, view cart).
Maintains session reuse for efficiency.
"""

from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional

from ..http.client import APIV3HTTPClient

logger = logging.getLogger(__name__)


class APIV3CartService:
    """
    Cart service for API v3.

    Handles cart operations including:
    - Adding items to cart
    - Viewing cart contents
    - Removing items from cart
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy
        self.socks_url = socks_url

        # Create HTTP client (reuses session)
        self.client = APIV3HTTPClient(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks_proxy,
            socks_url=socks_url,
        )

        self.logger = logging.getLogger(f"{__name__}.APIV3CartService")

    async def add_to_cart(
        self,
        card_ids: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Add items to cart.

        Args:
            card_ids: List of card IDs to add
            user_id: User ID for logging

        Returns:
            Response with success status and cart data
        """
        try:
            self.logger.info(f"Adding {len(card_ids)} items to cart for user {user_id}")

            # First, get the shop page to obtain CSRF token
            shop_response = await self.client.get(
                endpoint="shop",
                params={},
                timeout=30,
            )

            if not shop_response.get("success"):
                return {
                    "success": False,
                    "error": "Failed to get CSRF token from shop page",
                }

            # Extract token from shop data
            token = shop_response.get("data", {}).get("payload", {}).get("_token")
            if not token:
                self.logger.warning("No CSRF token found, attempting without token")

            # Prepare form data
            form_data = {
                "_token": token or "",
                "checked[]": card_ids,  # Multiple IDs as array
            }

            # POST to cart endpoint
            response = await self.client.post(
                endpoint="cart",
                data=form_data,
                timeout=60,
            )

            if response.get("success"):
                self.logger.info(f"Successfully added items to cart")
                return {
                    "success": True,
                    "message": f"Added {len(card_ids)} items to cart",
                    "cart_data": response.get("data", {}),
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to add to cart: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error adding to cart: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def view_cart(
        self,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        View cart contents.

        Args:
            user_id: User ID for logging

        Returns:
            Response with cart items and totals
        """
        try:
            self.logger.info(f"Viewing cart for user {user_id}")

            # GET cart endpoint
            response = await self.client.get(
                endpoint="cart",
                params={},
                timeout=60,
            )

            if not response.get("success"):
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to view cart: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

            # Extract cart data
            cart_data = response.get("data", {})

            # Parse cart items from table data if present
            cart_items = self._parse_cart_items(cart_data)

            self.logger.info(f"Cart contains {len(cart_items)} items")

            return {
                "success": True,
                "items": cart_items,
                "item_count": len(cart_items),
                "raw_data": cart_data,
            }

        except Exception as e:
            self.logger.error(f"Error viewing cart: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    def _parse_cart_items(self, cart_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Parse cart items from API v3 response.

        Args:
            cart_data: Raw cart data from API

        Returns:
            List of cart items
        """
        items = []

        # Cart data might have table structure similar to shop
        headers = cart_data.get("headers", [])
        rows = cart_data.get("rows", [])

        for row in rows:
            if not row:
                continue

            item = {}

            # Extract item ID from checkbox
            if len(row) > 0 and isinstance(row[0], dict):
                item["id"] = row[0].get("input_value", "")

            # Map columns to fields based on headers
            for col_idx, cell in enumerate(row):
                if col_idx >= len(headers):
                    break

                if not isinstance(cell, dict):
                    continue

                header = headers[col_idx].strip()
                text = cell.get("text", "").strip()

                if header == "BIN":
                    item["bin"] = text
                elif header == "Price":
                    item["price"] = text
                elif header == "F. Name":
                    item["name"] = text

            if item:
                items.append(item)

        return items

    async def clear_cart(
        self,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Clear all items from cart using API v3 empty cart endpoint.

        Based on demo/api3_demo/empty_cart.py - Uses POST request with
        _method=PUT, target="Empty cart" payload.

        Args:
            user_id: User ID for logging

        Returns:
            Response with success status
        """
        try:
            self.logger.info(f"Clearing cart for user {user_id}")

            # Get CSRF token by making a direct request to cart page
            token = await self._get_csrf_token()

            if not token:
                self.logger.warning("No CSRF token found, attempting without token")

            # Prepare form data for empty cart action
            form_data = {
                "_token": token or "",
                "_method": "PUT",
                "target": "Empty cart",
            }

            # POST to cart endpoint with empty cart action
            response = await self.client.post(
                endpoint="cart",
                data=form_data,
                timeout=60,
            )

            if response.get("success"):
                self.logger.info(f"Successfully cleared cart")
                return {
                    "success": True,
                    "message": "Cart cleared successfully",
                    "response_data": response.get("data", {}),
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to clear cart: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error clearing cart: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def remove_from_cart(
        self,
        card_ids: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Remove specific items from cart using API v3 remove selected endpoint.

        Based on demo/api3_demo/remove_selected_cart.py - Uses POST request with
        _method=PUT, target="Remove selected", checked[]=<ids> payload.

        Args:
            card_ids: List of card IDs to remove
            user_id: User ID for logging

        Returns:
            Response with success status
        """
        try:
            self.logger.info(f"Removing {len(card_ids)} items from cart for user {user_id}")
            self.logger.debug(f"Removing card IDs: {card_ids}")

            if not card_ids:
                return {
                    "success": True,
                    "message": "No items to remove",
                }

            # Get CSRF token by making a direct request to cart page
            token = await self._get_csrf_token()

            if not token:
                self.logger.warning("No CSRF token found, attempting without token")

            # Prepare form data for remove selected action
            form_data = {
                "_token": token or "",
                "_method": "PUT",
                "target": "Remove selected",
                "checked[]": card_ids,  # Multiple IDs as array
            }

            # POST to cart endpoint with remove selected action
            response = await self.client.post(
                endpoint="cart",
                data=form_data,
                timeout=60,
            )

            if response.get("success"):
                self.logger.info(f"Successfully removed items from cart")
                return {
                    "success": True,
                    "message": f"Removed {len(card_ids)} items from cart",
                    "removed_ids": card_ids,
                    "response_data": response.get("data", {}),
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to remove items from cart: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error removing items from cart: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def _get_csrf_token(self) -> Optional[str]:
        """
        Get CSRF token from cart page by making a direct request.

        Returns:
            CSRF token string or None if not found
        """
        try:
            # Make direct request to get HTML
            session = self.client.session_manager.get_session()
            url = self.client._build_url("cart")
            response = session.get(url, timeout=30)

            if response.status_code == 200:
                html = response.text

                # Extract token using regex (fastest method)
                import re
                csrf_match = re.search(r'name="_token"[^>]*value="([^"]*)"', html)
                if csrf_match:
                    token = csrf_match.group(1)
                    self.logger.debug(f"CSRF token extracted: {token[:20]}...")
                    return token

                # Fallback to BeautifulSoup if regex fails
                token = self.client._extract_csrf_token(html)
                if token:
                    self.logger.debug(f"CSRF token extracted (fallback): {token[:20]}...")
                    return token

            self.logger.warning(f"Failed to extract CSRF token from cart page (status: {response.status_code})")
            return None

        except Exception as e:
            self.logger.error(f"Error getting CSRF token: {e}")
            return None

    async def close(self):
        """Close the cart service and HTTP client."""
        await self.client.close()
