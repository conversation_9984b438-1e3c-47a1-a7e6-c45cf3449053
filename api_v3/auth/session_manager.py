"""
API v3 Session Manager

Handles session persistence and validation for API v3.
Adapted from demo/api3_demo/session_manager.py
"""

from __future__ import annotations

import json
import logging
import os
import time
from pathlib import Path
from typing import Optional
from urllib.parse import urljoin

import requests

from .login import (
    LoginSession,
    cookies_to_serializable,
    load_cookies_into_jar,
    refresh_xsrf_headers_from_cookies,
    prune_cookie_duplicates,
)

logger = logging.getLogger(__name__)

# Session validation cache time (10 minutes for better performance)
FAST_VALIDATION_CACHE_TIME = 600

# Session validation cache (global to share across all instances)
_last_validation_time = 0
_last_validation_result = False

# Global session cache (shared across all service instances)
_cached_sessions: dict[str, requests.Session] = {}
_cached_session_times: dict[str, float] = {}

# Per-session validation cache for even better performance
_session_validation_cache: dict[str, tuple[float, bool]] = {}


def get_session_cookies_path(base_url: str) -> str:
    """Get session cookies file path for a specific base URL"""
    # Create a safe filename from the base URL
    safe_name = base_url.replace("://", "_").replace("/", "_").replace(".", "_")
    storage_dir = Path("storage/api_v3")
    storage_dir.mkdir(parents=True, exist_ok=True)
    return str(storage_dir / f"session_{safe_name}.json")


def save_session_cookies(
    session: requests.Session,
    base_url: str,
    path: Optional[str] = None,
) -> Optional[str]:
    """Persist current session cookies to JSON for reuse"""
    global _last_validation_time
    try:
        out_path = path or get_session_cookies_path(base_url)
        data = {
            "cookies": cookies_to_serializable(session.cookies),
            "saved_at": time.time(),
            "validated_at": _last_validation_time,
            "base_url": base_url,
        }
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info("Saved session cookies to: %s", out_path)
        return out_path
    except Exception as e:
        logger.warning("Failed to save session cookies: %s", e)
        return None


def load_session_cookies(
    session: requests.Session,
    base_url: str,
    path: Optional[str] = None,
) -> int:
    """Load cookies from JSON into the session. Returns number of cookies loaded."""
    global _last_validation_time
    p = path or get_session_cookies_path(base_url)
    try:
        if not os.path.exists(p):
            return 0

        with open(p, "r", encoding="utf-8") as f:
            data = json.load(f) or {}

        items = data.get("cookies") or []

        # Check if session data is recent (less than 1 hour old)
        saved_at = data.get("saved_at", 0)
        if time.time() - saved_at < 3600:  # 1 hour
            _last_validation_time = data.get("validated_at", 0)
            logger.info(
                f"Loading recent session (saved {int(time.time() - saved_at)}s ago)"
            )

        loaded = load_cookies_into_jar(session.cookies, items)
        if loaded:
            refresh_xsrf_headers_from_cookies(session)
            referer = base_url.rstrip("/") + "/"
            prune_cookie_duplicates(session.cookies, "XSRF-TOKEN", base_url=referer)
            prune_cookie_duplicates(session.cookies, "bbm_session", base_url=referer)

        logger.info("Loaded %d cookies from: %s", loaded, p)
        return loaded
    except Exception as e:
        logger.warning("Failed to load session cookies: %s", e)
        return 0


def is_unauthenticated(resp: requests.Response) -> bool:
    """Check if a response indicates that the user is not authenticated"""
    try:
        url = getattr(resp, "url", "") or ""
        if "/login" in url:
            return True
        if resp.status_code in (401, 403):
            return True
        html = resp.text or ""
        # Heuristic: presence of login form fields
        if ('name="username"' in html and 'name="passwd"' in html) or (
            'name="password"' in html
        ):
            return True
    except Exception:
        pass
    return False


def validate_session(
    session: requests.Session,
    base_url: str,
    use_socks_proxy: bool = False,
    socks_url: str = "socks5h://127.0.0.1:9150",
    force_check: bool = False,
) -> bool:
    """
    Ultra-fast session validation with aggressive caching.
    Uses per-session cache for maximum performance.
    """
    global _last_validation_time, _last_validation_result, _session_validation_cache
    current_time = time.time()

    # Create session-specific cache key
    session_key = f"{base_url}:{id(session)}"

    # Check per-session cache first (most specific)
    if session_key in _session_validation_cache and not force_check:
        cache_time, cache_result = _session_validation_cache[session_key]
        if (current_time - cache_time) < FAST_VALIDATION_CACHE_TIME and cache_result:
            logger.debug(f"Using per-session cache (age: {int(current_time - cache_time)}s)")
            return cache_result

    # Check global cache as fallback
    if (
        not force_check
        and (current_time - _last_validation_time) < FAST_VALIDATION_CACHE_TIME
        and _last_validation_result
    ):
        logger.debug(f"Using global validation cache (age: {int(current_time - _last_validation_time)}s)")
        # Update per-session cache
        _session_validation_cache[session_key] = (current_time, _last_validation_result)
        return _last_validation_result

    logger.info("Performing fresh session validation...")
    try:
        # Ensure proxy settings are applied
        if use_socks_proxy and not session.proxies:
            session.proxies.update({"http": socks_url, "https": socks_url})
            logger.info(f"Applied proxy settings for validation: {socks_url}")

        # Make a lightweight request to check authentication
        shop_url = urljoin(base_url.rstrip("/") + "/", "shop")
        resp = session.get(shop_url, allow_redirects=True, timeout=15)  # Reduced timeout

        # Check if the response indicates we are unauthenticated
        is_valid = not is_unauthenticated(resp)

        # Cache the result globally and per-session
        _last_validation_time = current_time
        _last_validation_result = is_valid
        _session_validation_cache[session_key] = (current_time, is_valid)

        if is_valid:
            logger.info("✅ Session is valid")
        else:
            logger.warning("❌ Session validation failed")

        return is_valid
    except requests.RequestException as e:
        logger.error("Session validation failed due to network error: %s", e)
        # Don't cache network failures
        return False


def get_authenticated_session(
    base_url: str,
    username: str,
    password: str,
    use_socks_proxy: bool = False,
    socks_url: str = "socks5h://127.0.0.1:9150",
) -> requests.Session:
    """
    Provides a guaranteed authenticated session with ultra-optimized caching.
    Uses aggressive caching and minimal validation to maximize performance.
    """
    global _last_validation_time, _last_validation_result, _cached_sessions, _cached_session_times

    # Create cache key from base_url + username
    cache_key = f"{base_url}:{username}"

    # Check if we have a cached session that's still valid
    if cache_key in _cached_sessions:
        cached_session = _cached_sessions[cache_key]
        cache_time = _cached_session_times.get(cache_key, 0)

        # Extended cache time - if cached less than 2 hours ago, trust it more
        if time.time() - cache_time < 7200:  # 2 hours
            logger.debug(f"🔄 Reusing cached session (age: {int(time.time() - cache_time)}s)")

            # Only validate if session is older than 30 minutes
            if time.time() - cache_time < 1800:  # 30 minutes
                logger.debug("✅ Session is recent, skipping validation")
                return cached_session

            # Quick validation check for older sessions
            if validate_session(cached_session, base_url, use_socks_proxy, socks_url):
                logger.info("✅ Cached session validated successfully")
                # Update cache time to extend validity
                _cached_session_times[cache_key] = time.time()
                return cached_session
            else:
                logger.info("❌ Cached session expired, will re-authenticate")
                cached_session.cookies.clear()
        else:
            logger.info("⏰ Cached session too old, will re-authenticate")

    # Create NEW login session instance
    login_session = LoginSession(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=use_socks_proxy,
        socks_url=socks_url,
    )

    # Load cookies and try fast validation
    if load_session_cookies(login_session.session, base_url) > 0:
        if validate_session(login_session.session, base_url, use_socks_proxy, socks_url):
            logger.info("✅ Reusing valid session from cookies")
            # Cache the session
            _cached_sessions[cache_key] = login_session.session
            _cached_session_times[cache_key] = time.time()
            return login_session.session
        else:
            logger.info("❌ Loaded session is invalid, performing fresh login")
            login_session.session.cookies.clear()

    # Perform fresh login if needed
    logger.info("🔐 Performing fresh login...")
    if not login_session.login():
        logger.error("Failed to authenticate")
        raise Exception("Authentication failed")

    # Mark as validated and cache
    _last_validation_time = time.time()
    _last_validation_result = True

    # Save the new session cookies with validation timestamp
    save_session_cookies(login_session.session, base_url)

    # Cache the session for reuse across all services
    _cached_sessions[cache_key] = login_session.session
    _cached_session_times[cache_key] = time.time()

    logger.info("✅ Fresh authentication completed and cached")
    return login_session.session


class SessionManager:
    """
    Session manager for API v3.
    Handles session creation, validation, and persistence.

    Uses a global session cache so all instances with the same credentials
    share the same underlying session.
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy or ".onion" in base_url
        self.socks_url = socks_url
        self.cache_key = f"{base_url}:{username}"

    def get_session(self) -> requests.Session:
        """
        Get authenticated session (uses global cache for sharing across services).
        This ensures all services with the same credentials use the SAME session.
        """
        return get_authenticated_session(
            base_url=self.base_url,
            username=self.username,
            password=self.password,
            use_socks_proxy=self.use_socks_proxy,
            socks_url=self.socks_url,
        )

    def validate_session(self, force_check: bool = False) -> bool:
        """Validate current session"""
        session = self.get_session()
        return validate_session(
            session,
            self.base_url,
            self.use_socks_proxy,
            self.socks_url,
            force_check,
        )

    def refresh_session(self) -> requests.Session:
        """Force refresh the session by clearing cache"""
        global _cached_sessions, _cached_session_times
        # Clear from global cache
        if self.cache_key in _cached_sessions:
            del _cached_sessions[self.cache_key]
            del _cached_session_times[self.cache_key]
        # Get new session
        return self.get_session()

    def save_session(self) -> Optional[str]:
        """Save current session to disk"""
        session = self.get_session()
        return save_session_cookies(session, self.base_url)

    def close(self):
        """
        Close the session.
        Note: Since sessions are shared globally, this doesn't actually close
        the underlying session to avoid disrupting other services.
        """
        # Don't close the session as it may be used by other services
        pass


def clear_session_cache():
    """
    Clear the global session cache.
    Useful for forcing re-authentication across all services.
    """
    global _cached_sessions, _cached_session_times, _last_validation_time, _last_validation_result
    _cached_sessions.clear()
    _cached_session_times.clear()
    _last_validation_time = 0
    _last_validation_result = False
    logger.info("🧹 Cleared global session cache")
