"""
API v3 Login Module

Adapted from demo/api3_demo/login.py to work with the bot's architecture.
Provides session-based authentication for API v3.
"""

from __future__ import annotations

import logging
import os
from typing import Optional, Tuple, Dict, Any
from urllib.parse import urljoin, unquote

import requests
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)


def setup_logger(name: str = "api_v3.login") -> logging.Logger:
    """Setup basic logging"""
    log = logging.getLogger(name)
    log.setLevel(logging.INFO)
    return log


# Utility functions
def log_request(response: requests.Response) -> None:
    """Log request details"""
    logger.debug(f"Request: {response.request.method} {response.request.url}")


def log_response(response: requests.Response) -> None:
    """Log response details"""
    logger.debug(f"Response: {response.status_code} ({len(response.text)} chars)")


def refresh_xsrf_headers_from_cookies(session: requests.Session) -> None:
    """Refresh XSRF headers from cookies"""
    xsrf_token = None
    for cookie in session.cookies:
        if cookie.name == "XSRF-TOKEN":
            xsrf_token = cookie.value
            break

    if xsrf_token:
        try:
            header_value = unquote(xsrf_token)
        except Exception:
            header_value = xsrf_token
        session.headers.update(
            {
                "X-XSRF-TOKEN": header_value,
                "X-CSRF-TOKEN": header_value,
            }
        )


def cookies_to_serializable(cookies) -> list[Dict[str, Any]]:
    """Convert cookies to serializable format"""
    cookie_list = []
    for cookie in cookies:
        cookie_data = {
            "name": cookie.name,
            "value": cookie.value,
            "domain": cookie.domain,
            "path": cookie.path,
            "secure": cookie.secure,
            "expires": cookie.expires,
        }
        cookie_list.append(cookie_data)
    return cookie_list


def load_cookies_into_jar(cookie_jar, cookie_list: list[Dict[str, Any]]) -> int:
    """Load cookies from list into cookie jar"""
    count = 0
    for cookie_data in cookie_list:
        try:
            cookie_jar.set(
                name=cookie_data["name"],
                value=cookie_data["value"],
                domain=cookie_data.get("domain"),
                path=cookie_data.get("path", "/"),
            )
            count += 1
        except Exception as e:
            logger.warning(f"Failed to load cookie: {e}")
    return count


def prune_cookie_duplicates(cookie_jar, cookie_name: str, base_url: str = None) -> None:
    """Remove duplicate cookies, keeping only the most recent"""
    cookies_to_remove = []
    seen_cookies = {}

    for cookie in cookie_jar:
        if cookie.name == cookie_name:
            key = f"{cookie.domain}{cookie.path}"
            if key in seen_cookies:
                cookies_to_remove.append(seen_cookies[key])
            seen_cookies[key] = cookie

    for cookie in cookies_to_remove:
        try:
            cookie_jar.clear(domain=cookie.domain, path=cookie.path, name=cookie.name)
        except Exception:
            pass


class LoginSession:
    """Session-based login handler for API v3"""

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url.rstrip("/")
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy or ".onion" in base_url
        self.socks_url = socks_url

        # Build URLs
        self.referer = self.base_url + "/"
        self.login_url = urljoin(self.referer, "login")

        self.session = self._create_session()

    def _create_session(self) -> requests.Session:
        """Create HTTP session with proper headers"""
        session = requests.Session()
        session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Connection": "keep-alive",
                "Referer": self.referer,
            }
        )

        if self.use_socks_proxy:
            logger.info("Using SOCKS proxy: %s", self.socks_url)
            session.proxies.update({"http": self.socks_url, "https": self.socks_url})

            # Log helpful information for .onion domains
            if ".onion" in self.base_url:
                logger.info(
                    "Connecting to .onion domain - ensure Tor is running:\n"
                    "  - Tor Browser (port 9150): Should be open\n"
                    "  - System Tor (port 9050): Run 'sudo systemctl start tor'\n"
                    "  - Set SOCKS_URL environment variable to override default"
                )

        return session

    def _get_cookie_value(self, name: str) -> Optional[str]:
        """Get cookie value safely"""
        try:
            return self.session.cookies.get(name)
        except Exception:
            return None

    def _extract_csrf_token(self, html: str) -> Optional[str]:
        """Extract CSRF token from HTML"""
        soup = BeautifulSoup(html, "html.parser")

        # Check input fields
        for name in ("_token", "csrf_token", "csrf", "token"):
            token_input = soup.find("input", {"name": name})
            if token_input and token_input.get("value"):
                return token_input.get("value").strip()

        # Check meta tags
        meta_token = soup.find("meta", {"name": "csrf-token"})
        if meta_token and meta_token.get("content"):
            return meta_token.get("content").strip()

        return None

    def _build_login_form(self, html: str) -> Optional[Dict[str, str]]:
        """Build login form data from HTML"""
        soup = BeautifulSoup(html, "html.parser")

        # Find form with password field
        login_form = None
        for form in soup.find_all("form"):
            if form.find("input", {"type": "password"}):
                login_form = form
                break

        if not login_form:
            return None

        form_data = {}

        # Process all inputs
        for inp in login_form.find_all("input"):
            name = inp.get("name")
            if not name:
                continue

            input_type = inp.get("type", "text").lower()
            value = inp.get("value", "")

            if input_type in ("hidden", "text", "email"):
                form_data[name] = value
            elif input_type == "password":
                form_data[name] = self.password

        # Find and set username field
        username_field = self._find_username_field(login_form)
        if username_field:
            form_data[username_field] = self.username

        return form_data if form_data else None

    def _find_username_field(self, form) -> Optional[str]:
        """Find username field in login form"""
        for inp in form.find_all("input"):
            name = inp.get("name", "").lower()
            input_type = inp.get("type", "").lower()
            if input_type in ("text", "email") and any(
                keyword in name for keyword in ("username", "email", "login", "user")
            ):
                return inp.get("name")
        return None

    def fetch_login_page(self) -> Tuple[Optional[str], Optional[str]]:
        """Fetch login page and extract tokens"""
        logger.info("Fetching login page: %s", self.login_url)

        try:
            response = self.session.get(self.login_url, timeout=30)
            response.raise_for_status()

            # Extract CSRF token
            csrf_token = self._extract_csrf_token(response.text)

            # Update XSRF headers from cookies
            refresh_xsrf_headers_from_cookies(self.session)

            logger.info("Login page fetched successfully")
            return csrf_token, response.text

        except requests.exceptions.ConnectionError as e:
            error_msg = str(e)
            if "Connection refused" in error_msg and self.use_socks_proxy:
                logger.error(
                    "SOCKS proxy connection refused. "
                    "Please ensure Tor is running:\n"
                    "  - Tor Browser: Should be running (uses port 9150)\n"
                    "  - System Tor: Run 'sudo systemctl start tor' (uses port 9050)\n"
                    "  - Current SOCKS URL: %s\n"
                    "  - Try alternative: socks5h://127.0.0.1:9050 for system Tor",
                    self.socks_url
                )
            else:
                logger.error("Failed to fetch login page: %s", e)
            return None, None
        except requests.RequestException as e:
            logger.error("Failed to fetch login page: %s", e)
            return None, None

    def perform_login(self, csrf_token: Optional[str], login_html: str) -> bool:
        """Perform login with credentials"""
        if not self.username or not self.password:
            logger.error("Username and password are required")
            return False

        # Build form data
        form_data = self._build_login_form(login_html)
        if not form_data:
            # Fallback form data
            form_data = {
                "_token": csrf_token or "",
                "username": self.username,
                "password": self.password,
            }

        logger.info("Attempting login...")

        try:
            response = self.session.post(
                self.login_url, data=form_data, timeout=30, allow_redirects=False
            )

            # Check if login was successful
            if response.is_redirect:
                # Follow redirect
                location = response.headers.get("Location")
                if location:
                    redirect_url = urljoin(self.login_url, location)
                    response = self.session.get(redirect_url, timeout=30)

            # Check for successful login indicators
            session_cookie = self._get_cookie_value("bbm_session")
            if session_cookie:
                logger.info("Login successful - session cookie received")
                return True
            elif response.is_redirect:
                logger.info("Login successful - redirect received")
                return True
            else:
                logger.warning("Login may have failed - no clear success indicators")
                return True  # Let verify_login_success() do the final check

        except requests.RequestException as e:
            logger.error("Login failed: %s", e)
            return False

    def verify_login_success(self) -> bool:
        """Verify if login was successful"""
        # First check if we have a session cookie
        session_cookie = self._get_cookie_value("bbm_session")
        if not session_cookie:
            logger.warning("No session cookie found")
            return False

        # Try to access a protected page to verify login
        try:
            shop_url = urljoin(self.referer, "shop")
            response = self.session.get(shop_url, timeout=15)

            # Check if we're redirected to login page
            if "login" in response.url.lower() or response.status_code == 401:
                logger.error("Redirected to login - authentication failed")
                return False

            response.raise_for_status()
            logger.info("Login verification successful")
            return True

        except requests.RequestException as e:
            logger.error("Failed to verify login: %s", e)
            return False

    def login(self) -> bool:
        """Complete login flow"""
        logger.info("Starting login flow for %s", self.base_url)

        # Fetch login page
        csrf_token, login_html = self.fetch_login_page()
        if not login_html:
            logger.error("Could not fetch login page")
            return False

        # Perform login
        if not self.perform_login(csrf_token, login_html):
            logger.error("Login failed")
            return False

        # Verify login success
        if self.verify_login_success():
            logger.info("✓ Login completed successfully")
            return True
        else:
            logger.error("✗ Login verification failed")
            return False

