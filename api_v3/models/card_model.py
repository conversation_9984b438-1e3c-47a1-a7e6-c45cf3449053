"""
API v3 Card Models

Data models for API v3 card representation and collections.
"""

from __future__ import annotations

import re
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class APIV3Card(BaseModel):
    """
    API v3 Card model with comprehensive field parsing.

    Represents a single card with all available data fields parsed
    from the API v3 table format.
    """

    # Core identification
    id: str = Field(alias="_id", description="Unique card identifier")
    bin: str = Field(default="", description="Bank Identification Number")

    # Card details
    expiry: str = Field(default="", description="Expiry date (MM/YY format)")
    base: str = Field(default="", description="Base/quality information")
    quality: str = Field(default="UNKNOWN", description="Card quality level")

    # Cardholder information
    name: str = Field(default="", description="Cardholder name")
    f_name: str = Field(default="", description="First name")

    # Location information
    country: str = Field(default="", description="Country")
    continent: str = Field(default="", description="Continent")
    state: str = Field(default="", description="State/Region")
    city: str = Field(default="", description="City")

    # Card scheme and type
    brand: str = Field(default="", description="Card brand (VISA, MASTERCARD, etc.)")
    scheme: str = Field(default="", description="Card scheme")
    type: str = Field(default="", description="Card type (DEBIT, CREDIT, etc.)")
    level: str = Field(default="", description="Card level (PLATINUM, GOLD, etc.)")
    bank: str = Field(default="", description="Issuing bank")

    # Additional data
    address: str = Field(default="", description="Address information")
    phone: str = Field(default="", description="Phone number")
    dob: str = Field(default="", description="Date of birth availability")

    # Pricing
    price: str = Field(default="0", description="Card price")
    original_price: Optional[str] = Field(
        default=None, description="Original price before discount"
    )
    discounted: bool = Field(default=False, description="Whether card is discounted")
    expiring_soon: bool = Field(
        default=False, description="Whether card is expiring soon"
    )

    # Raw data fields for reference
    scheme_type_level: str = Field(default="", description="Raw scheme/type/level data")
    address_phone_dob: str = Field(default="", description="Raw address/phone/DOB data")
    price_raw: str = Field(default="", description="Raw price data")

    class Config:
        """Pydantic configuration"""

        populate_by_name = True  # Updated from allow_population_by_field_name
        extra = "allow"

    @classmethod
    def from_table_row(
        cls,
        row: List[Dict[str, Any]],
        headers: List[str],
    ) -> Optional[APIV3Card]:
        """
        Create APIV3Card from table row data.

        Args:
            row: Table row data (list of cell dictionaries)
            headers: Table headers

        Returns:
            APIV3Card instance or None if invalid data
        """
        if not row or len(row) == 0:
            return None

        try:
            card_data = {}

            # Extract card ID from first cell (checkbox)
            if len(row) > 0 and isinstance(row[0], dict):
                card_id = row[0].get("input_value", "")
                if not card_id:
                    return None
                card_data["_id"] = card_id

            # Process each column based on header
            for col_idx, cell in enumerate(row):
                if col_idx >= len(headers) or not isinstance(cell, dict):
                    continue

                header = headers[col_idx].strip()
                text = cell.get("text", "").strip()

                if not text or text == "checkbox":
                    continue

                # Map columns to fields
                if header == "BIN":
                    card_data["bin"] = text
                elif header == "Expiry":
                    card_data["expiry"] = text
                elif header == "Base":
                    card_data["base"] = text
                    # Extract quality from base
                    if "SUPER_VALID" in text:
                        card_data["quality"] = "HIGH"
                    elif "VALID" in text:
                        card_data["quality"] = "MEDIUM"
                elif header == "F. Name":
                    card_data["name"] = text
                    card_data["f_name"] = text
                elif header == "Country/Ethnicity/Continent":
                    # Parse "UNITED STATES,North America"
                    parts = [p.strip() for p in text.split(",")]
                    if len(parts) >= 1:
                        card_data["country"] = parts[0]
                    if len(parts) >= 2:
                        card_data["continent"] = parts[1]
                elif header == "Scheme/Type/Level":
                    card_data["scheme_type_level"] = text
                    # Parse scheme, type, level, bank
                    cls._parse_scheme_type_level(text, card_data)
                elif header == "Address/Phone/DOB":
                    card_data["address_phone_dob"] = text
                    # Parse address, phone, DOB
                    cls._parse_address_phone_dob(text, card_data)
                elif header == "Price":
                    card_data["price_raw"] = text
                    # Parse price and discounts
                    cls._parse_price(text, card_data)

            return cls(**card_data)

        except Exception:
            return None

    @classmethod
    def _parse_scheme_type_level(cls, text: str, card_data: Dict[str, Any]) -> None:
        """Parse scheme/type/level field."""
        text_upper = text.upper()

        # Extract brand/scheme
        if "MASTERCARD" in text_upper:
            card_data["brand"] = "MASTERCARD"
            card_data["scheme"] = "MASTERCARD"
        elif "VISA" in text_upper:
            card_data["brand"] = "VISA"
            card_data["scheme"] = "VISA"
        elif "AMEX" in text_upper or "AMERICAN EXPRESS" in text_upper:
            card_data["brand"] = "AMEX"
            card_data["scheme"] = "AMEX"

        # Extract type
        if "DEBIT" in text_upper:
            card_data["type"] = "DEBIT"
        elif "CREDIT" in text_upper:
            card_data["type"] = "CREDIT"
        elif "PREPAID" in text_upper:
            card_data["type"] = "PREPAID"

        # Extract level
        if "PLATINUM" in text_upper:
            card_data["level"] = "PLATINUM"
        elif "GOLD" in text_upper:
            card_data["level"] = "GOLD"
        elif "CLASSIC" in text_upper:
            card_data["level"] = "CLASSIC"
        elif "STANDARD" in text_upper:
            card_data["level"] = "STANDARD"

        # Extract bank (usually at the end after comma)
        if "," in text:
            bank_part = text.split(",")[-1].strip()
            if bank_part and len(bank_part) > 3:
                card_data["bank"] = bank_part

    @classmethod
    def _parse_address_phone_dob(cls, text: str, card_data: Dict[str, Any]) -> None:
        """Parse address/phone/DOB field."""
        # Extract phone number
        if "Phone" in text:
            phone_match = re.search(r"Phone\s*:\s*([+\d\-\.\s\(\)]+)", text)
            if phone_match:
                card_data["phone"] = phone_match.group(1).strip()

        # Extract DOB availability
        if "DOB" in text:
            if "DOB: YES" in text or "DOB:YES" in text:
                card_data["dob"] = "YES"
            elif "DOB: NO" in text or "DOB:NO" in text:
                card_data["dob"] = "NO"
            else:
                card_data["dob"] = "UNKNOWN"

        # Extract address
        if "No address" in text:
            card_data["address"] = "No address"
        elif text and not text.startswith("Phone") and not text.startswith("DOB"):
            # Try to extract address part
            address_part = text.split("Phone")[0].split("DOB")[0].strip()
            if address_part and address_part != "." and len(address_part) > 2:
                card_data["address"] = address_part

    @classmethod
    def _parse_price(cls, text: str, card_data: Dict[str, Any]) -> None:
        """Parse price field."""
        # Extract prices
        price_matches = re.findall(r"(\d+(?:\.\d+)?)\$", text)
        if price_matches:
            card_data["price"] = price_matches[-1]  # Final price
            if len(price_matches) > 1:
                card_data["original_price"] = price_matches[0]  # Original price

        # Check for discounts and expiry warnings
        if "%" in text and "off" in text:
            card_data["discounted"] = True
        if "EXPIRING" in text:
            card_data["expiring_soon"] = True

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format expected by CardService."""
        return {
            "_id": self.id,
            "bin": self.bin,
            "expiry": self.expiry,
            "country": self.country,
            "brand": self.brand,
            "type": self.type,
            "level": self.level,
            "price": self.price,
            "bank": self.bank,
            "quality": self.quality,
            "name": self.name,
            "address": self.address,
            "phone": self.phone,
            "dob": self.dob,
            "state": self.state,
            "city": self.city,
            "continent": self.continent,
        }


class APIV3CardList(BaseModel):
    """
    Collection of API v3 cards with metadata.
    """

    cards: List[APIV3Card] = Field(default_factory=list, description="List of cards")
    headers: List[str] = Field(default_factory=list, description="Table headers")
    page: int = Field(default=1, description="Current page number")
    limit: int = Field(default=50, description="Items per page")
    total_count: int = Field(default=0, description="Total number of cards")
    filters_applied: Dict[str, str] = Field(
        default_factory=dict, description="Applied filters"
    )

    @classmethod
    def from_table_data(
        cls,
        table_data: Dict[str, Any],
        page: int = 1,
        limit: int = 50,
        filters_applied: Optional[Dict[str, str]] = None,
    ) -> APIV3CardList:
        """
        Create APIV3CardList from table data.

        Args:
            table_data: Table data with headers and rows
            page: Current page
            limit: Items per page
            filters_applied: Applied filters

        Returns:
            APIV3CardList instance
        """
        headers = table_data.get("headers", [])
        rows = table_data.get("rows", [])

        cards = []
        for row in rows:
            card = APIV3Card.from_table_row(row, headers)
            if card:
                cards.append(card)

        return cls(
            cards=cards,
            headers=headers,
            page=page,
            limit=limit,
            total_count=len(cards),
            filters_applied=filters_applied or {},
        )

    def to_dict_list(self) -> List[Dict[str, Any]]:
        """Convert cards to list of dictionaries."""
        return [card.to_dict() for card in self.cards]
