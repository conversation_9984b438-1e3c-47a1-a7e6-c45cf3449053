# API v3 Implementation

Comprehensive documentation for API v3 session-based authentication and integration.

## Quick Start

```bash
# Configure environment
export EXTERNAL_API_VERSION=v3
export EXTERNAL_V3_BASE_URL=https://example.com
export EXTERNAL_V3_USERNAME=your_username
export EXTERNAL_V3_PASSWORD=your_password

# Run tests
python tests/test_api_v3_integration.py
```

## Key Features

- Session-based authentication with form login
- Automatic session persistence and validation
- SOCKS proxy support for .onion domains
- Seamless integration with external API service

See full documentation in the codebase analysis.
