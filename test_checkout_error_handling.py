#!/usr/bin/env python3
"""
Test script for checkout error handling and graceful degradation.

This script tests the enhanced error handling mechanisms implemented
for the checkout process when API v3 service is unavailable.
"""

import asyncio
import os
import sys
sys.path.insert(0, '.')

from dotenv import load_dotenv
load_dotenv(override=True)

from database.connection import init_database
from services.checkout_queue_service import CheckoutQueueService
from services.api_health_monitor import health_monitor, ServiceStatus


async def test_error_handling():
    """Test the error handling mechanisms"""
    
    print("🧪 Testing Checkout Error Handling and Graceful Degradation")
    print("=" * 70)
    
    # Initialize database
    await init_database()
    print("✅ Database connected")
    
    # Initialize checkout service
    checkout_service = CheckoutQueueService()
    print("✅ Checkout service initialized")
    
    # Test 1: API v3 Enforcement
    print("\n🔒 Test 1: API v3 Enforcement")
    print("-" * 40)
    
    is_v3_only = checkout_service._ensure_api_v3_only()
    print(f"   API v3 enforcement: {is_v3_only}")
    
    if not is_v3_only:
        print("❌ API v3 not configured - cannot test error handling")
        return False
    
    # Test 2: Service Health Check
    print("\n🏥 Test 2: Service Health Check")
    print("-" * 40)
    
    health_result = await health_monitor.check_api_v3_health(checkout_service.external_api_service)
    health_monitor.record_health_check(health_result)
    
    print(f"   Service status: {health_result.status.value}")
    print(f"   Response time: {health_result.response_time_ms:.1f}ms")
    if health_result.error_message:
        print(f"   Error message: {health_result.error_message}")
    
    # Test 3: Error Message Generation
    print("\n📝 Test 3: Error Message Generation")
    print("-" * 40)
    
    test_errors = [
        "502 Bad Gateway",
        "503 Service Unavailable",
        "504 Gateway Timeout",
        "Connection refused",
        "Network is unreachable",
        "Unknown error"
    ]
    
    for error in test_errors:
        user_message = checkout_service._get_service_unavailable_message(error)
        print(f"   {error} -> {user_message[:60]}...")
    
    # Test 4: Service Unavailability Detection
    print("\n🔍 Test 4: Service Unavailability Detection")
    print("-" * 40)
    
    for error in test_errors:
        is_unavailable = checkout_service._is_service_unavailable_error(error)
        print(f"   {error}: {'UNAVAILABLE' if is_unavailable else 'OTHER'}")
    
    # Test 5: Cart Operations with Current Service State
    print("\n🛒 Test 5: Cart Operations with Current Service State")
    print("-" * 40)
    
    if health_result.status == ServiceStatus.UNAVAILABLE:
        print("   ⚠️ Service is unavailable - testing error handling")
        
        # Test cart clearing with unavailable service
        print("   Testing cart clearing...")
        clear_success = await checkout_service._clear_external_cart_v3()
        print(f"   Clear result: {clear_success} (expected: False)")
        
        # Test cart verification with unavailable service
        print("   Testing cart verification...")
        empty_verification = await checkout_service._verify_cart_empty_v3()
        print(f"   Empty verification: {empty_verification} (expected: True - graceful handling)")
        
        # Test cart population with unavailable service
        print("   Testing cart population...")
        test_items = [
            {
                "card_id": "test123",
                "quantity": 1,
                "card_data": {"name": "Test Card"}
            }
        ]
        populate_success = await checkout_service._populate_cart_from_virtual_v3(test_items)
        print(f"   Populate result: {populate_success} (expected: False)")
        
    elif health_result.status == ServiceStatus.HEALTHY:
        print("   ✅ Service is healthy - testing normal operations")
        
        # Test normal cart operations
        print("   Testing cart clearing...")
        clear_success = await checkout_service._clear_external_cart_v3()
        print(f"   Clear result: {clear_success}")
        
        print("   Testing cart verification...")
        empty_verification = await checkout_service._verify_cart_empty_v3()
        print(f"   Empty verification: {empty_verification}")
        
    else:
        print(f"   ⚠️ Service is {health_result.status.value} - testing degraded operations")
    
    # Test 6: Health Metrics
    print("\n📊 Test 6: Health Metrics")
    print("-" * 40)
    
    metrics = health_monitor.get_service_health("api_v3")
    if metrics:
        print(f"   Current status: {metrics.current_status.value}")
        print(f"   Last check: {metrics.last_check}")
        print(f"   Success rate (24h): {metrics.success_rate_24h:.1f}%")
        print(f"   Avg response time: {metrics.avg_response_time_ms:.1f}ms")
        print(f"   Consecutive failures: {metrics.consecutive_failures}")
        if metrics.last_error:
            print(f"   Last error: {metrics.last_error}")
    else:
        print("   No metrics available")
    
    # Test 7: Complete Workflow Test
    print("\n🔄 Test 7: Complete Workflow Test")
    print("-" * 40)
    
    test_cart_items = [
        {
            "card_id": "0415a6e7e2d8f45e716f204d1403220eacb3e75f",
            "quantity": 1,
            "card_data": {
                "_id": "0415a6e7e2d8f45e716f204d1403220eacb3e75f",
                "name": "Test Card 1"
            }
        }
    ]
    
    print("   Testing complete cart synchronization workflow...")
    workflow_success = await checkout_service._populate_external_cart(test_cart_items)
    print(f"   Workflow result: {workflow_success}")
    
    if not workflow_success:
        print("   ✅ Workflow correctly failed due to service unavailability")
    else:
        print("   ✅ Workflow succeeded - service is available")
    
    print("\n🎯 Error Handling Test Summary")
    print("=" * 70)
    
    if health_result.status == ServiceStatus.UNAVAILABLE:
        print("✅ Service unavailability correctly detected")
        print("✅ Error handling mechanisms working properly")
        print("✅ Graceful degradation implemented")
        print("✅ User-friendly error messages generated")
    elif health_result.status == ServiceStatus.HEALTHY:
        print("✅ Service is healthy and operations work normally")
        print("✅ Error handling mechanisms are ready for failures")
    else:
        print("⚠️ Service is degraded but error handling is working")
    
    print("\n📋 Recommendations:")
    if health_result.status == ServiceStatus.UNAVAILABLE:
        print("   1. Check Tor connectivity (ports 9050/9150)")
        print("   2. Verify .onion domain accessibility")
        print("   3. Monitor service status and retry later")
        print("   4. Consider implementing fallback mechanisms if needed")
    else:
        print("   1. Monitor service health regularly")
        print("   2. Set up alerts for service degradation")
        print("   3. Consider implementing proactive health checks")
    
    return True


if __name__ == "__main__":
    asyncio.run(test_error_handling())
