# API v3 Fix - Summary

## Problem Identified

The bot's API v3 authentication was failing with the error:

```
[WinError 10061] No connection could be made because the target machine actively refused it
```

## Root Cause

**SOCKS Proxy Port Mismatch:**

- The `.env` file was configured to use port **9050** (system Tor service)
- But **Tor Browser** was running on port **9150**
- The connection was being refused because no service was listening on port 9050

## Solution Applied

### 1. Created Diagnostic Scripts

**`test_tor_connection.py`:**

- Tests which Tor ports are accessible
- Identifies available SOCKS proxy ports (9050 vs 9150)
- Provides clear guidance on which port to use

**`test_onion_connection.py`:**

- Tests actual connectivity to `.onion` domains
- Verifies Tor routing is working correctly
- Confirms the target site is reachable

**`test_api_v3.py`:**

- Tests the bot's API v3 implementation end-to-end
- Verifies authentication and data fetching
- Confirms the complete integration works

### 2. Updated Configuration Files

**Main `.env` file:**

```env
# Changed from port 9050 to 9150
SOCKS_URL=socks5h://127.0.0.1:9150
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9150
```

**Demo `.env` file (`demo/api3_demo/.env`):**

```env
# Added SOCKS configuration
USE_SOCKS_PROXY=true
SOCKS_URL=socks5h://127.0.0.1:9150
```

## Test Results

### ✅ All Tests Passing

1. **Port Detection Test:**

   - Found Tor Browser on port 9150 ✅
   - Port 9050 not available (system Tor not running)

2. **Onion Connection Test:**

   - Successfully connected to base URL ✅
   - Successfully connected to login URL ✅
   - Received valid HTTP responses (200 OK)

3. **API v3 Integration Test:**
   - Configuration loaded correctly ✅
   - Authentication successful ✅
   - Retrieved 94 items from the shop ✅
   - Data parsing working correctly ✅

## Configuration Notes

### Tor Ports:

- **Port 9150:** Tor Browser (when Tor Browser is running)
- **Port 9050:** System Tor service (when standalone Tor daemon is running)

### Current Setup:

- Using **Tor Browser** on port **9150**
- All API v3 requests now route correctly through Tor
- .onion domain access is working

## Files Modified

1. `.env` - Updated SOCKS proxy port configuration
2. `demo/api3_demo/.env` - Added SOCKS proxy configuration
3. Created diagnostic scripts:
   - `test_tor_connection.py`
   - `test_onion_connection.py`
   - `test_api_v3.py`

## What Was NOT Changed

The API v3 implementation code (`api_v3/` directory) did not need any changes. The issue was purely a configuration problem with the SOCKS proxy port.

## How to Use

### Starting the Bot:

1. **Ensure Tor Browser is running:**

   - Open Tor Browser
   - Wait for "Connected to Tor Network"
   - Keep it running in the background

2. **Run the bot:**

   ```bash
   python run.py
   # or
   python main.py
   ```

3. **Verify API v3 is active:**
   - Check logs for successful authentication
   - Test browsing cards in the bot
   - Confirm data is being fetched from the API

### Troubleshooting:

If you encounter connection issues:

1. **Check Tor connection:**

   ```bash
   python test_tor_connection.py
   ```

2. **Test .onion connectivity:**

   ```bash
   python test_onion_connection.py
   ```

3. **Test API v3 integration:**
   ```bash
   python test_api_v3.py
   ```

### Switching Between Tor Versions:

**To use System Tor (port 9050):**

```env
SOCKS_URL=socks5h://127.0.0.1:9050
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9050
```

Then start system Tor:

- Windows: Run `tor.exe` from Tor Expert Bundle
- Linux: `sudo systemctl start tor`
- macOS: `brew services start tor`

**To use Tor Browser (port 9150):**

```env
SOCKS_URL=socks5h://127.0.0.1:9150
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9150
```

Then open Tor Browser and keep it running.

## Summary

✅ **Fixed:** SOCKS proxy port configuration
✅ **Working:** API v3 authentication and data fetching
✅ **Verified:** End-to-end connectivity through Tor
✅ **Ready:** Bot can now use API v3 with .onion domains

The issue was a simple configuration mismatch, not a code problem. The bot's API v3 implementation is solid and matches the working demo implementation.
