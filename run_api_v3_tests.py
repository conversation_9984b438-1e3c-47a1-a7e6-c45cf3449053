"""
API v3 Test Suite - Quick Runner

Run all API v3 tests with a single command.
"""

import sys
import subprocess
from pathlib import Path


def run_tests():
    """Run all API v3 tests"""
    print("=" * 70)
    print("🧪 API v3 Test Suite")
    print("=" * 70)
    print()

    test_file = Path(__file__).parent / "tests" / "test_api_v3_fixed.py"

    if not test_file.exists():
        print(f"❌ Test file not found: {test_file}")
        return 1

    # Run unit tests
    print("📋 Running Unit Tests...")
    print("-" * 70)
    result_unit = subprocess.run(
        [
            sys.executable,
            "-m",
            "pytest",
            str(test_file),
            "-v",
            "-k",
            "not integration",
            "--tb=short",
        ],
        cwd=Path(__file__).parent,
    )

    print()
    print("=" * 70)

    # Run integration tests
    print("📋 Running Integration Tests...")
    print("-" * 70)
    result_integration = subprocess.run(
        [
            sys.executable,
            "-m",
            "pytest",
            str(test_file),
            "-v",
            "-k",
            "integration",
            "--tb=short",
        ],
        cwd=Path(__file__).parent,
    )

    print()
    print("=" * 70)
    print("📊 Summary")
    print("=" * 70)

    if result_unit.returncode == 0 and result_integration.returncode == 0:
        print("✅ All tests passed!")
        print()
        print("🎉 API v3 is fully operational")
        return 0
    else:
        print("❌ Some tests failed")
        print()
        if result_unit.returncode != 0:
            print("   - Unit tests: FAILED")
        if result_integration.returncode != 0:
            print("   - Integration tests: FAILED")
        return 1


if __name__ == "__main__":
    sys.exit(run_tests())
