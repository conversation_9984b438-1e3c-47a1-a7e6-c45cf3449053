"""
Service Status API Endpoints

Provides real-time status information about external services,
particularly API v3 service health and circuit breaker status.
"""

from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from services.api_health_monitor import health_monitor
from services.circuit_breaker import circuit_breaker_manager
from services.external_api_service import get_external_api_service
from utils.api_logging import logger


router = APIRouter(prefix="/service-status", tags=["Service Status"])


class ServiceHealthResponse(BaseModel):
    """Response model for service health"""
    service_name: str
    status: str
    is_available: bool
    last_check: Optional[datetime]
    success_rate_24h: float
    avg_response_time_ms: float
    consecutive_failures: int
    last_error: Optional[str]


class CircuitBreakerResponse(BaseModel):
    """Response model for circuit breaker status"""
    name: str
    state: str
    is_available: bool
    failure_count: int
    success_count: int
    total_requests: int
    total_failures: int
    total_successes: int
    failure_rate: float
    last_failure_time: Optional[datetime]
    last_success_time: Optional[datetime]
    opened_at: Optional[datetime]
    next_attempt_time: Optional[datetime]


class ServiceStatusSummary(BaseModel):
    """Summary of all service statuses"""
    overall_status: str
    api_v3_available: bool
    checkout_available: bool
    services: Dict[str, ServiceHealthResponse]
    circuit_breakers: Dict[str, CircuitBreakerResponse]
    timestamp: datetime


@router.get("/health", response_model=ServiceStatusSummary)
async def get_service_health():
    """
    Get comprehensive service health status including API v3 and circuit breakers.
    
    Returns:
        ServiceStatusSummary: Complete service health information
    """
    try:
        logger.info("📊 Service health status requested")
        
        # Get external API service
        external_api_service = get_external_api_service()
        
        # Perform health check for API v3
        api_v3_health = await health_monitor.check_api_v3_health(external_api_service)
        health_monitor.record_health_check(api_v3_health)
        
        # Get health metrics
        api_v3_metrics = health_monitor.get_service_health("api_v3")
        
        # Get circuit breaker stats
        circuit_stats = circuit_breaker_manager.get_all_stats()
        
        # Build service health responses
        services = {}
        if api_v3_metrics:
            services["api_v3"] = ServiceHealthResponse(
                service_name="api_v3",
                status=api_v3_metrics.current_status.value,
                is_available=api_v3_metrics.current_status.value == "healthy",
                last_check=api_v3_metrics.last_check,
                success_rate_24h=api_v3_metrics.success_rate_24h,
                avg_response_time_ms=api_v3_metrics.avg_response_time_ms,
                consecutive_failures=api_v3_metrics.consecutive_failures,
                last_error=api_v3_metrics.last_error
            )
        
        # Build circuit breaker responses
        circuit_breakers = {}
        for name, stats in circuit_stats.items():
            breaker = circuit_breaker_manager.get_breaker(name)
            circuit_breakers[name] = CircuitBreakerResponse(
                name=name,
                state=stats.state.value,
                is_available=breaker.is_available(),
                failure_count=stats.failure_count,
                success_count=stats.success_count,
                total_requests=stats.total_requests,
                total_failures=stats.total_failures,
                total_successes=stats.total_successes,
                failure_rate=breaker.get_failure_rate(),
                last_failure_time=stats.last_failure_time,
                last_success_time=stats.last_success_time,
                opened_at=stats.opened_at,
                next_attempt_time=stats.next_attempt_time
            )
        
        # Determine overall status
        api_v3_available = (
            api_v3_metrics and 
            api_v3_metrics.current_status.value in ["healthy", "degraded"]
        )
        
        checkout_available = True
        if "api_v3_checkout" in circuit_breakers:
            checkout_cb = circuit_breakers["api_v3_checkout"]
            checkout_available = checkout_cb.is_available
        
        if api_v3_available and checkout_available:
            overall_status = "healthy"
        elif api_v3_available or checkout_available:
            overall_status = "degraded"
        else:
            overall_status = "unavailable"
        
        return ServiceStatusSummary(
            overall_status=overall_status,
            api_v3_available=api_v3_available,
            checkout_available=checkout_available,
            services=services,
            circuit_breakers=circuit_breakers,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"❌ Error getting service health: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get service health: {str(e)}")


@router.get("/api-v3", response_model=ServiceHealthResponse)
async def get_api_v3_status():
    """
    Get specific API v3 service status.
    
    Returns:
        ServiceHealthResponse: API v3 service health information
    """
    try:
        logger.info("📊 API v3 status requested")
        
        # Get external API service
        external_api_service = get_external_api_service()
        
        # Perform health check
        health_result = await health_monitor.check_api_v3_health(external_api_service)
        health_monitor.record_health_check(health_result)
        
        # Get metrics
        metrics = health_monitor.get_service_health("api_v3")
        
        if not metrics:
            raise HTTPException(status_code=404, detail="API v3 metrics not available")
        
        return ServiceHealthResponse(
            service_name="api_v3",
            status=metrics.current_status.value,
            is_available=metrics.current_status.value == "healthy",
            last_check=metrics.last_check,
            success_rate_24h=metrics.success_rate_24h,
            avg_response_time_ms=metrics.avg_response_time_ms,
            consecutive_failures=metrics.consecutive_failures,
            last_error=metrics.last_error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error getting API v3 status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get API v3 status: {str(e)}")


@router.get("/circuit-breakers", response_model=Dict[str, CircuitBreakerResponse])
async def get_circuit_breaker_status():
    """
    Get status of all circuit breakers.
    
    Returns:
        Dict[str, CircuitBreakerResponse]: Circuit breaker status information
    """
    try:
        logger.info("📊 Circuit breaker status requested")
        
        circuit_stats = circuit_breaker_manager.get_all_stats()
        circuit_breakers = {}
        
        for name, stats in circuit_stats.items():
            breaker = circuit_breaker_manager.get_breaker(name)
            circuit_breakers[name] = CircuitBreakerResponse(
                name=name,
                state=stats.state.value,
                is_available=breaker.is_available(),
                failure_count=stats.failure_count,
                success_count=stats.success_count,
                total_requests=stats.total_requests,
                total_failures=stats.total_failures,
                total_successes=stats.total_successes,
                failure_rate=breaker.get_failure_rate(),
                last_failure_time=stats.last_failure_time,
                last_success_time=stats.last_success_time,
                opened_at=stats.opened_at,
                next_attempt_time=stats.next_attempt_time
            )
        
        return circuit_breakers
        
    except Exception as e:
        logger.error(f"❌ Error getting circuit breaker status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get circuit breaker status: {str(e)}")


@router.post("/circuit-breakers/{breaker_name}/reset")
async def reset_circuit_breaker(breaker_name: str):
    """
    Manually reset a specific circuit breaker.
    
    Args:
        breaker_name: Name of the circuit breaker to reset
        
    Returns:
        Dict: Reset confirmation
    """
    try:
        logger.info(f"🔄 Circuit breaker reset requested: {breaker_name}")
        
        if breaker_name not in circuit_breaker_manager.breakers:
            raise HTTPException(status_code=404, detail=f"Circuit breaker '{breaker_name}' not found")
        
        breaker = circuit_breaker_manager.get_breaker(breaker_name)
        await breaker.reset()
        
        logger.info(f"✅ Circuit breaker '{breaker_name}' reset successfully")
        
        return {
            "message": f"Circuit breaker '{breaker_name}' reset successfully",
            "timestamp": datetime.utcnow(),
            "breaker_name": breaker_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error resetting circuit breaker {breaker_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset circuit breaker: {str(e)}")


@router.get("/checkout-availability")
async def get_checkout_availability():
    """
    Get checkout service availability status.
    
    Returns:
        Dict: Checkout availability information
    """
    try:
        logger.info("📊 Checkout availability requested")
        
        # Check API v3 health
        external_api_service = get_external_api_service()
        health_result = await health_monitor.check_api_v3_health(external_api_service)
        
        # Check circuit breaker
        checkout_breaker = circuit_breaker_manager.get_breaker("api_v3_checkout")
        circuit_available = checkout_breaker.is_available()
        circuit_stats = checkout_breaker.get_stats()
        
        # Determine availability
        service_healthy = health_result.status.value in ["healthy", "degraded"]
        checkout_available = service_healthy and circuit_available
        
        response = {
            "checkout_available": checkout_available,
            "api_v3_status": health_result.status.value,
            "circuit_breaker_state": circuit_stats.state.value,
            "circuit_breaker_available": circuit_available,
            "failure_rate": checkout_breaker.get_failure_rate(),
            "last_error": health_result.error_message,
            "timestamp": datetime.utcnow()
        }
        
        if not checkout_available:
            if not circuit_available:
                response["unavailable_reason"] = "Circuit breaker is open due to repeated failures"
                response["estimated_recovery"] = circuit_stats.next_attempt_time
            else:
                response["unavailable_reason"] = f"API v3 service is {health_result.status.value}"
                response["estimated_recovery"] = "Unknown - depends on external service recovery"
        
        return response
        
    except Exception as e:
        logger.error(f"❌ Error getting checkout availability: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get checkout availability: {str(e)}")
