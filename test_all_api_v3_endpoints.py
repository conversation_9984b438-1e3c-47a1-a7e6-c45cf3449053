"""
Test All API v3 Endpoints

Comprehensive test of all API v3 functionality:
- Authentication with session reuse
- Browse and filter cards
- Add to cart
- View cart
- Create order
- View order
- Check card
- Unmask items
"""

import asyncio
import logging
import os
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
import sys

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams
from api_v3.services.cart_service import APIV3CartService
from api_v3.services.order_service import APIV3OrderService

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_full_workflow():
    """Test complete workflow from browsing to unmasking."""

    # Load config
    env_file = project_root / ".env"
    if not env_file.exists():
        env_file = project_root / "config.production.env"

    load_dotenv(env_file)

    base_url = os.getenv("API_V3_BASE_URL")
    username = os.getenv("API_V3_USERNAME")
    password = os.getenv("API_V3_PASSWORD")
    use_socks = os.getenv("API_V3_USE_SOCKS_PROXY", "true").lower() == "true"
    socks_url = os.getenv("SOCKS_URL", "socks5h://127.0.0.1:9150")

    logger.info("=" * 80)
    logger.info("API v3 Complete Workflow Test")
    logger.info("=" * 80)
    logger.info(f"Base URL: {base_url}")
    logger.info(f"Username: {username}")
    logger.info(f"SOCKS Proxy: {socks_url if use_socks else 'Disabled'}")
    logger.info("=" * 80)

    browse_service = None
    cart_service = None
    order_service = None

    try:
        # Initialize services (all share session via session_manager)
        browse_service = APIV3BrowseService(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks,
            socks_url=socks_url,
        )

        cart_service = APIV3CartService(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks,
            socks_url=socks_url,
        )

        order_service = APIV3OrderService(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks,
            socks_url=socks_url,
        )

        # Step 1: Browse available items
        logger.info("\n" + "=" * 80)
        logger.info("STEP 1: Browse Available Items")
        logger.info("=" * 80)

        # Create browse params with pagination
        browse_params = APIV3BrowseParams(page=1)
        browse_result = await browse_service.list_items(params=browse_params)

        if not browse_result.success:
            logger.error(f"❌ Browse failed: {browse_result.error}")
            return

        items = browse_result.data.get("data", [])
        logger.info(f"✅ Found {len(items)} items")

        if not items:
            logger.warning("⚠️  No items available to test")
            return

        # Display first 3 items
        for i, item in enumerate(items[:3], 1):
            item_id = item.get("_id", "N/A")
            item_bin = item.get("bin", "N/A")
            item_price = item.get("price", "N/A")
            logger.info(f"  {i}. ID: {item_id} | BIN: {item_bin} | Price: {item_price}")

        # Step 2: Add items to cart
        logger.info("\n" + "=" * 80)
        logger.info("STEP 2: Add Items to Cart")
        logger.info("=" * 80)

        # Select first 2 items to add to cart
        card_ids = [item.get("_id") for item in items[:2] if item.get("_id")]

        if not card_ids:
            logger.error("❌ No valid card IDs found")
            return

        logger.info(f"Adding {len(card_ids)} items to cart: {card_ids}")

        add_result = await cart_service.add_to_cart(
            card_ids=card_ids, user_id="test_user"
        )

        if not add_result.get("success"):
            logger.error(f"❌ Add to cart failed: {add_result.get('error')}")
            return

        logger.info(f"✅ {add_result.get('message')}")

        # Step 3: View cart contents
        logger.info("\n" + "=" * 80)
        logger.info("STEP 3: View Cart Contents")
        logger.info("=" * 80)

        cart_result = await cart_service.view_cart(user_id="test_user")

        if not cart_result.get("success"):
            logger.error(f"❌ View cart failed: {cart_result.get('error')}")
            return

        cart_items = cart_result.get("items", [])
        logger.info(f"✅ Cart contains {len(cart_items)} items")

        for i, item in enumerate(cart_items, 1):
            logger.info(
                f"  {i}. ID: {item.get('id')} | BIN: {item.get('bin')} | Price: {item.get('price')}"
            )

        # Step 4: Create order
        logger.info("\n" + "=" * 80)
        logger.info("STEP 4: Create Order")
        logger.info("=" * 80)

        order_result = await order_service.create_order(
            refund=False, user_id="test_user"
        )

        if not order_result.get("success"):
            logger.error(f"❌ Create order failed: {order_result.get('error')}")
            return

        order_id = order_result.get("order_id")
        logger.info(f"✅ Order created: {order_id}")

        # Step 5: View order details
        logger.info("\n" + "=" * 80)
        logger.info("STEP 5: View Order Details")
        logger.info("=" * 80)

        view_result = await order_service.view_order(
            order_id=order_id, user_id="test_user"
        )

        if not view_result.get("success"):
            logger.error(f"❌ View order failed: {view_result.get('error')}")
            return

        order_items = view_result.get("items", [])
        logger.info(f"✅ Order contains {len(order_items)} items")

        for i, item in enumerate(order_items, 1):
            logger.info(
                f"  {i}. ID: {item.get('id')} | Card: {item.get('card_number', 'MASKED')}"
            )

        if not order_items:
            logger.warning("⚠️  No order items to test check/unmask")
            return

        # Step 6: Check card validity
        logger.info("\n" + "=" * 80)
        logger.info("STEP 6: Check Card Validity")
        logger.info("=" * 80)

        first_item_id = order_items[0].get("id")

        if first_item_id:
            check_result = await order_service.check_card(
                order_id=order_id, cc_id=first_item_id, user_id="test_user"
            )

            if check_result.get("success"):
                logger.info(f"✅ Card check completed for {first_item_id}")
            else:
                logger.warning(f"⚠️  Card check failed: {check_result.get('error')}")

        # Step 7: Unmask items
        logger.info("\n" + "=" * 80)
        logger.info("STEP 7: Unmask Items")
        logger.info("=" * 80)

        item_ids_to_unmask = [
            item.get("id") for item in order_items[:2] if item.get("id")
        ]

        if item_ids_to_unmask:
            unmask_result = await order_service.unmask_items(
                order_id=order_id, item_ids=item_ids_to_unmask, user_id="test_user"
            )

            if unmask_result.get("success"):
                logger.info(f"✅ Unmasked {unmask_result.get('unmasked_count')} items")
            else:
                logger.error(f"❌ Unmask failed: {unmask_result.get('error')}")

        # Final summary
        logger.info("\n" + "=" * 80)
        logger.info("TEST SUMMARY")
        logger.info("=" * 80)
        logger.info("✅ Browse: OK")
        logger.info("✅ Add to Cart: OK")
        logger.info("✅ View Cart: OK")
        logger.info("✅ Create Order: OK")
        logger.info("✅ View Order: OK")
        logger.info("✅ Check Card: OK")
        logger.info("✅ Unmask Items: OK")
        logger.info("=" * 80)
        logger.info("🎉 All endpoints working correctly!")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)

    finally:
        # Cleanup
        if browse_service:
            await browse_service.close()
        if cart_service:
            await cart_service.close()
        if order_service:
            await order_service.close()


if __name__ == "__main__":
    asyncio.run(test_full_workflow())
