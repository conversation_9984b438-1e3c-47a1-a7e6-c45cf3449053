# API v3 Quick Reference

## Setup Checklist

- [ ] Install dependencies: `pip install requests[socks] beautifulsoup4 python-dotenv`
- [ ] Configure `.env` with API v3 settings
- [ ] Create storage directory: `mkdir -p storage/api_v3`
- [ ] Start Tor (if using .onion): `systemctl start tor`
- [ ] Validate setup: `python scripts/validate_api_v3_setup.py`

## Essential Configuration

```bash
# Minimum required .env settings
EXTERNAL_API_VERSION=v3
EXTERNAL_V3_BASE_URL=https://your-site.onion
EXTERNAL_V3_USERNAME=your_username
EXTERNAL_V3_PASSWORD=your_password
```

## Common Operations

### List Items
```python
response = await api_service.list_items({"bins": "405621", "page": 1})
items = response.data["items"] if response.success else []
```

### Add to Cart
```python
response = await api_service.add_to_cart("item_id")
success = response.success
```

### Checkout
```python
response = await api_service.checkout()
order_id = response.data["order_id"] if response.success else None
```

### Check Order
```python
response = await api_service.check_order(order_id, card_id)
status = response.data["card_status"] if response.success else "unknown"
```

### Unmask Cards
```python
response = await api_service.unmask_order(order_id, [card_id1, card_id2])
success = response.success
```

## Troubleshooting Commands

```bash
# Validate configuration
python scripts/validate_api_v3_setup.py

# Test integration logic
python scripts/test_api_v3_integration_logic.py

# Migrate from api3
python scripts/migrate_api3_config.py --dry-run

# Test specific config
python scripts/test_api_v3_config.py
```

## File Structure

```
api_v3/
├── __init__.py          # Module exports
├── client.py            # Main HTTP client
├── config.py            # Configuration management
├── html_utils.py        # Form and table parsing
└── cookies.py           # Cookie and CSRF handling

scripts/
├── migrate_api3_config.py           # Configuration migration
├── validate_api_v3_setup.py         # Setup validation
├── test_api_v3_config.py            # Configuration testing
└── test_api_v3_integration_logic.py # Integration testing

storage/api_v3/
└── session_cookies.json # Persistent session storage
```

## Key Classes

- **APIV3Client**: Main HTTP client with session management
- **APIV3Config**: Configuration with auto-proxy detection
- **APIV3Result**: Standardized response wrapper
- **ExternalAPIService**: Bot integration layer

## Environment Variables

| Variable | Purpose | Example |
|----------|---------|---------|
| `EXTERNAL_V3_BASE_URL` | Target site URL | `https://site.onion` |
| `EXTERNAL_V3_USERNAME` | Login username | `myuser` |
| `EXTERNAL_V3_PASSWORD` | Login password | `mypass` |
| `EXTERNAL_V3_USE_TOR_PROXY` | Enable Tor proxy | `true` |
| `EXTERNAL_V3_SOCKS_URL` | SOCKS proxy URL | `socks5h://127.0.0.1:9050` |
| `EXTERNAL_V3_SESSION_PATH` | Session storage | `storage/api_v3/session_cookies.json` |
| `EXTERNAL_V3_TIMEOUT` | Request timeout | `60` |
| `EXTERNAL_V3_VERIFY_TLS` | TLS verification | `true` |

## Error Handling

```python
response = await api_service.list_items()
if response.success:
    # Handle success
    items = response.data["items"]
else:
    # Handle error
    error_msg = response.error
    status_code = response.status_code
```

## Session Management

- Sessions are automatically saved to `storage/api_v3/session_cookies.json`
- Invalid sessions trigger automatic re-authentication
- Cookie conflicts are resolved by path specificity
- CSRF tokens are automatically extracted and managed

## Tor Integration

- .onion domains automatically enable SOCKS proxy
- Proxy settings are configurable via environment variables
- Connection testing validates Tor availability
- Fallback handling for proxy failures

## Testing

```bash
# Quick validation (no dependencies required)
python scripts/test_api_v3_integration_logic.py

# Full validation (requires dependencies)
python scripts/validate_api_v3_setup.py

# Configuration testing
python scripts/test_api_v3_config.py
```

## Common Issues

1. **"No module named 'bs4'"** → `pip install beautifulsoup4`
2. **"SOCKS proxy unreachable"** → Start Tor: `systemctl start tor`
3. **"Authentication failed"** → Check username/password in `.env`
4. **"Permission denied"** → `chmod 755 storage/api_v3`

## Migration from api3

```bash
# See what would be migrated
python scripts/migrate_api3_config.py --dry-run

# Perform migration with validation
python scripts/migrate_api3_config.py --validate
```

## Debug Mode

```bash
export LOG_LEVEL=DEBUG
python your_script.py
```

## Performance Tips

- Sessions are cached and reused
- Use filters to reduce response size
- Configure appropriate timeouts
- Monitor session file size

## Security Notes

- Passwords are masked in logs
- TLS verification is enabled by default
- Session cookies are stored securely
- CSRF tokens are handled automatically
