# API v3 Troubleshooting Guide

## Common Issues and Solutions

### 1. SOCKS Proxy Connection Refused

**Error Message:**
```
Failed to establish a new connection: [Errno 111] Connection refused
```

**Cause:** Tor is not running or not accessible on the configured SOCKS port.

**Solution:**

#### Option A: Use Tor Browser (Recommended for testing)

1. **Download and install Tor Browser:**
   - Visit: https://www.torproject.org/download/
   - Install for your platform

2. **Start Tor Browser:**
   - Open Tor Browser
   - Wait for it to connect to the Tor network
   - Keep it running in the background

3. **Configure the bot:**
   ```bash
   export SOCKS_URL=socks5h://127.0.0.1:9150
   # or add to .env file:
   # SOCKS_URL=socks5h://127.0.0.1:9150
   ```

#### Option B: Use System Tor (Recommended for production)

1. **Install Tor:**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install tor
   
   # macOS
   brew install tor
   
   # Fedora/RHEL
   sudo dnf install tor
   ```

2. **Start Tor service:**
   ```bash
   # Start Tor
   sudo systemctl start tor
   
   # Enable Tor to start on boot
   sudo systemctl enable tor
   
   # Check status
   sudo systemctl status tor
   ```

3. **Configure the bot:**
   ```bash
   export SOCKS_URL=socks5h://127.0.0.1:9050
   # or add to .env file:
   # SOCKS_URL=socks5h://127.0.0.1:9050
   ```

#### Check Tor Connectivity

Run the connectivity check script:

```bash
python scripts/check_tor.py
```

This will tell you which Tor services are running and which SOCKS URL to use.

### 2. Authentication Failed

**Error Message:**
```
Failed to authenticate
Login verification failed
```

**Possible Causes:**
- Incorrect username or password
- Session cookies expired
- CSRF token extraction failed
- Website structure changed

**Solutions:**

1. **Verify credentials:**
   ```bash
   # Check your .env file
   cat .env | grep API_V3
   
   # Should show:
   # API_V3_BASE_URL=http://...
   # API_V3_USERNAME=your_username
   # API_V3_PASSWORD=your_password
   ```

2. **Clear session cache:**
   ```bash
   # Remove cached sessions
   rm -rf storage/api_v3/session_*.json
   
   # Try again - will force fresh login
   ```

3. **Test login manually:**
   ```python
   from api_v3.auth import LoginSession
   
   session = LoginSession(
       base_url="http://your-site.onion",
       username="your_username",
       password="your_password",
       use_socks_proxy=True,
   )
   
   if session.login():
       print("✓ Login successful")
   else:
       print("✗ Login failed")
   ```

### 3. Session Expired

**Error Message:**
```
Redirected to login - authentication failed
Session validation failed
```

**Cause:** Session cookies have expired.

**Solution:**

Sessions are automatically refreshed, but you can force a refresh:

```bash
# Delete session files
rm -rf storage/api_v3/session_*.json

# Restart the bot - will create new session
```

### 4. HTML Parsing Errors

**Error Message:**
```
Error parsing table data
Error parsing row
```

**Cause:** Website HTML structure changed.

**Solution:**

1. **Check if the demo still works:**
   ```bash
   cd demo/api3_demo
   python list.py
   ```

2. **If demo works but API v3 doesn't:**
   - The HTML parsing logic may need updating
   - Compare `api_v3/http/client.py` with `demo/api3_demo/common_utils.py`
   - Update parsing logic to match current HTML structure

### 5. Timeout Errors

**Error Message:**
```
Request timeout
Connection timeout
```

**Possible Causes:**
- Slow Tor connection
- .onion site is down
- Network issues

**Solutions:**

1. **Increase timeout:**
   ```python
   # In your code
   service = APIV3BrowseService(
       base_url="...",
       username="...",
       password="...",
   )
   
   # Increase timeout in HTTP client
   response = await service.client.get(
       endpoint="shop",
       timeout=120,  # Increase from default 60
   )
   ```

2. **Check .onion site status:**
   - Try accessing the site in Tor Browser
   - Verify the site is online

3. **Check Tor connection:**
   ```bash
   # Test Tor connectivity
   curl --socks5-hostname 127.0.0.1:9150 https://check.torproject.org/api/ip
   ```

### 6. Environment Variable Issues

**Error Message:**
```
API v3 configuration not found in environment variables
base_url is required for API v3 configuration
```

**Cause:** Required environment variables not set.

**Solution:**

1. **Check current environment:**
   ```bash
   env | grep -E "(API_V3|EXTERNAL_V3)"
   ```

2. **Set required variables:**
   ```bash
   # Option 1: Use API_V3_* prefix
   export API_V3_BASE_URL="http://your-site.onion"
   export API_V3_USERNAME="your_username"
   export API_V3_PASSWORD="your_password"
   
   # Option 2: Use EXTERNAL_V3_* prefix
   export EXTERNAL_V3_BASE_URL="http://your-site.onion"
   export EXTERNAL_V3_USERNAME="your_username"
   export EXTERNAL_V3_PASSWORD="your_password"
   
   # Enable API v3
   export EXTERNAL_API_VERSION=v3
   ```

3. **Or add to .env file:**
   ```bash
   # Edit .env file
   nano .env
   
   # Add:
   EXTERNAL_API_VERSION=v3
   API_V3_BASE_URL=http://your-site.onion
   API_V3_USERNAME=your_username
   API_V3_PASSWORD=your_password
   SOCKS_URL=socks5h://127.0.0.1:9150
   ```

## Diagnostic Commands

### Check Configuration

```bash
# View current configuration
python -c "
from api_v3.config import get_api_v3_config_from_env
config = get_api_v3_config_from_env()
if config:
    print(f'Base URL: {config.base_url}')
    print(f'Username: {config.username}')
    print(f'Use SOCKS: {config.use_socks_proxy}')
    print(f'SOCKS URL: {config.socks_url}')
else:
    print('No configuration found')
"
```

### Test Authentication

```bash
# Test login
python -c "
import asyncio
from api_v3 import APIV3BrowseService
from api_v3.config import get_api_v3_config_from_env

async def test():
    config = get_api_v3_config_from_env()
    if not config:
        print('No config')
        return
    
    service = APIV3BrowseService(
        base_url=config.base_url,
        username=config.username,
        password=config.password,
        use_socks_proxy=config.use_socks_proxy,
        socks_url=config.socks_url,
    )
    
    # This will trigger authentication
    response = await service.list_items()
    print(f'Success: {response.success}')
    if not response.success:
        print(f'Error: {response.error}')
    
    await service.close()

asyncio.run(test())
"
```

### Check Tor

```bash
# Run Tor check script
python scripts/check_tor.py

# Or manually check ports
nc -zv 127.0.0.1 9150  # Tor Browser
nc -zv 127.0.0.1 9050  # System Tor
```

### View Logs

```bash
# Check session files
ls -la storage/api_v3/

# View session content
cat storage/api_v3/session_*.json | jq .

# Check bot logs
tail -f logs/bot.log | grep api_v3
```

## Getting Help

If you're still having issues:

1. **Enable debug logging:**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **Run integration tests:**
   ```bash
   python tests/test_api_v3_integration.py
   ```

3. **Check the demo:**
   ```bash
   cd demo/api3_demo
   python list.py
   ```

4. **Review documentation:**
   - `api_v3/README.md` - API v3 usage guide
   - `API_V3_INTEGRATION_SUMMARY.md` - Integration details

## Quick Reference

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `EXTERNAL_API_VERSION` | Yes | `v2` | Set to `v3` to enable API v3 |
| `API_V3_BASE_URL` | Yes | - | API base URL (e.g., `http://site.onion`) |
| `API_V3_USERNAME` | Yes | - | Username for authentication |
| `API_V3_PASSWORD` | Yes | - | Password for authentication |
| `SOCKS_URL` | No | `socks5h://127.0.0.1:9150` | SOCKS proxy URL for Tor |
| `API_V3_USE_SOCKS_PROXY` | No | Auto-detect | Force SOCKS proxy usage |

### SOCKS Ports

| Port | Service | How to Start |
|------|---------|--------------|
| 9150 | Tor Browser | Open Tor Browser |
| 9050 | System Tor | `sudo systemctl start tor` |

### Common Commands

```bash
# Check Tor
python scripts/check_tor.py

# Clear sessions
rm -rf storage/api_v3/

# Test API v3
python tests/test_api_v3_integration.py

# View logs
tail -f logs/bot.log | grep api_v3
```

