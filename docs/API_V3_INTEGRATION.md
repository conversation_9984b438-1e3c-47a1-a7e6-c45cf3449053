# API v3 Integration Guide

This document provides comprehensive information about the API v3 integration, which enhances the bot with advanced web scraping capabilities, Tor network support, and improved session management.

## Overview

The API v3 integration combines the existing `api_v3` module with enhanced functionality from the standalone `api3` scripts, providing:

- **Advanced Session Management**: Persistent cookie storage with conflict resolution
- **Tor Network Support**: Automatic SOCKS5 proxy configuration for .onion domains
- **Enhanced Form Processing**: Comprehensive HTML form parsing and payload building
- **Robust Error Handling**: Improved error detection and recovery mechanisms
- **CSRF Token Management**: Automatic token extraction and header management

## Architecture

### Core Components

1. **APIV3Client** (`api_v3/client.py`)
   - Main HTTP client with session management
   - Handles authentication, form processing, and API operations
   - Supports both regular HTTP and Tor proxy connections

2. **APIV3Config** (`api_v3/config.py`)
   - Configuration management with automatic proxy detection
   - URL normalization and validation
   - Default filter and timeout settings

3. **HTML Utilities** (`api_v3/html_utils.py`)
   - Advanced form extraction with comprehensive field detection
   - Table parsing for checkbox-based item selection
   - Payload building for form submissions

4. **Cookie Management** (`api_v3/cookies.py`)
   - XSRF token handling with multiple header formats
   - Cookie duplicate resolution by domain/path specificity
   - Session persistence and restoration

5. **External API Service** (`services/external_api_service.py`)
   - Integration layer between bot and API v3 client
   - Async/sync bridging using `asyncio.to_thread`
   - Operation routing and response normalization

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```bash
# API v3 Configuration
EXTERNAL_API_VERSION=v3
EXTERNAL_V3_BASE_URL=https://your-site.onion
EXTERNAL_V3_USERNAME=your_username
EXTERNAL_V3_PASSWORD=your_password
EXTERNAL_V3_USE_TOR_PROXY=true
EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9050
EXTERNAL_V3_SESSION_PATH=storage/api_v3/session_cookies.json
EXTERNAL_V3_TIMEOUT=60
EXTERNAL_V3_VERIFY_TLS=true
EXTERNAL_V3_DEFAULT_BINS=405621,424242
```

### Configuration Options

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `EXTERNAL_V3_BASE_URL` | Base URL of the target site | - | Yes |
| `EXTERNAL_V3_USERNAME` | Authentication username | - | Yes |
| `EXTERNAL_V3_PASSWORD` | Authentication password | - | Yes |
| `EXTERNAL_V3_USE_TOR_PROXY` | Enable Tor proxy usage | `true` for .onion | No |
| `EXTERNAL_V3_SOCKS_URL` | SOCKS proxy URL | `socks5h://127.0.0.1:9050` | No |
| `EXTERNAL_V3_SESSION_PATH` | Session cookies storage path | `storage/api_v3/session_cookies.json` | No |
| `EXTERNAL_V3_TIMEOUT` | Request timeout in seconds | `60` | No |
| `EXTERNAL_V3_VERIFY_TLS` | Enable TLS certificate verification | `true` | No |
| `EXTERNAL_V3_DEFAULT_BINS` | Default BIN filters (comma-separated) | - | No |

## Setup and Migration

### 1. Install Dependencies

Ensure all required dependencies are installed:

```bash
pip install -r requirements.txt
```

Required packages:
- `requests[socks]` - HTTP client with SOCKS proxy support
- `beautifulsoup4` - HTML parsing
- `python-dotenv` - Environment variable management

### 2. Migrate Configuration

Use the migration script to transfer settings from existing api3 configuration:

```bash
# Dry run to see what would be migrated
python scripts/migrate_api3_config.py --dry-run

# Perform actual migration
python scripts/migrate_api3_config.py --validate
```

### 3. Validate Setup

Run the validation script to ensure everything is configured correctly:

```bash
python scripts/validate_api_v3_setup.py
```

This will check:
- Required dependencies
- Configuration completeness
- Storage directory permissions
- Tor connectivity (if enabled)

### 4. Test Integration

Run the integration logic tests:

```bash
python scripts/test_api_v3_integration_logic.py
```

## Usage Examples

### Basic Operations

```python
from services.external_api_service import ExternalAPIService

# Initialize service
api_service = ExternalAPIService()

# List items with filters
response = await api_service.list_items({
    "bins": "405621",
    "country": "US",
    "page": 1
})

if response.success:
    items = response.data["items"]
    print(f"Found {len(items)} items")

# Add item to cart
item_id = items[0]["id"]
cart_response = await api_service.add_to_cart(item_id)

if cart_response.success:
    print("Item added to cart successfully")

# View cart
cart_response = await api_service.view_cart()
print(f"Cart total: {cart_response.data['total']}")

# Checkout
checkout_response = await api_service.checkout()
if checkout_response.success:
    order_id = checkout_response.data["order_id"]
    print(f"Order created: {order_id}")
```

### Order Management

```python
# List orders
orders_response = await api_service.list_orders()
orders = orders_response.data["orders"]

# Check specific order
order_id = 123
card_id = 456
check_response = await api_service.check_order(order_id, card_id)

if check_response.success:
    print(f"Card status: {check_response.data['card_status']}")

# Unmask order cards
unmask_response = await api_service.unmask_order(order_id, [card_id])
if unmask_response.success:
    print("Cards unmasked successfully")
```

### Connection Testing

```python
# Test API v3 connection
test_response = await api_service.test_api_v3_connection()

if test_response.success:
    conn_info = test_response.data["connection_info"]
    print(f"Connected to: {conn_info['base_url']}")
    print(f"Using Tor: {conn_info['use_tor_proxy']}")
else:
    print(f"Connection failed: {test_response.error}")
```

## Advanced Features

### Session Management

The API v3 client automatically manages sessions with:

- **Persistent Cookies**: Sessions are saved to disk and restored on restart
- **Session Validation**: Automatic detection of expired sessions
- **Re-authentication**: Automatic login when sessions expire
- **Cookie Conflict Resolution**: Intelligent handling of duplicate cookies

### Form Processing

Enhanced form processing capabilities include:

- **Comprehensive Field Detection**: Supports all HTML form elements
- **Dynamic Token Extraction**: Automatic CSRF token detection
- **Payload Building**: Smart form data construction with overrides
- **Validation**: Form structure validation and error detection

### Tor Network Integration

Seamless Tor network support with:

- **Automatic Proxy Detection**: .onion domains automatically use SOCKS proxy
- **Configurable Proxy Settings**: Custom SOCKS proxy configuration
- **Connection Testing**: Built-in Tor connectivity validation
- **Fallback Handling**: Graceful degradation when Tor is unavailable

## Troubleshooting

### Common Issues

1. **Dependencies Missing**
   ```bash
   pip install requests[socks] beautifulsoup4 python-dotenv
   ```

2. **Tor Connection Failed**
   - Ensure Tor is running: `systemctl status tor`
   - Check SOCKS proxy URL: `EXTERNAL_V3_SOCKS_URL=socks5h://127.0.0.1:9050`
   - Test connectivity: `curl --socks5-hostname 127.0.0.1:9050 https://check.torproject.org`

3. **Authentication Issues**
   - Verify credentials in `.env` file
   - Check for special characters that need escaping
   - Clear session cookies: `rm storage/api_v3/session_cookies.json`

4. **Session Persistence Problems**
   - Ensure storage directory is writable: `chmod 755 storage/api_v3`
   - Check disk space availability
   - Verify JSON format of session file

### Debug Mode

Enable debug logging by setting:

```bash
export PYTHONPATH=.
export LOG_LEVEL=DEBUG
```

### Validation Commands

```bash
# Check configuration
python scripts/validate_api_v3_setup.py

# Test specific functionality
python scripts/test_api_v3_config.py

# Run integration tests
python scripts/test_api_v3_integration_logic.py --verbose
```

## Migration from API v2

If migrating from API v2, note these key differences:

1. **Configuration**: API v3 uses different environment variables
2. **Session Management**: Enhanced with persistent storage
3. **Error Handling**: More robust with automatic retry logic
4. **Tor Support**: Built-in SOCKS proxy integration
5. **Form Processing**: Advanced HTML parsing capabilities

The external API service maintains backward compatibility, so existing bot commands will continue to work without modification.

## Performance Considerations

- **Session Reuse**: Sessions are cached and reused across requests
- **Connection Pooling**: HTTP connections are pooled for efficiency
- **Async Integration**: Synchronous API v3 calls are wrapped with `asyncio.to_thread`
- **Memory Management**: Large responses are processed in chunks
- **Timeout Handling**: Configurable timeouts prevent hanging requests

## Security Features

- **TLS Verification**: Configurable certificate validation
- **Credential Protection**: Passwords are masked in logs
- **Session Security**: Secure cookie handling and storage
- **CSRF Protection**: Automatic token management
- **Proxy Security**: Secure SOCKS proxy configuration

## Support and Maintenance

For issues or questions:

1. Check the troubleshooting section above
2. Run validation scripts to identify configuration issues
3. Review logs for detailed error information
4. Test with minimal configuration to isolate problems

The API v3 integration is designed to be robust and self-healing, with automatic recovery from common failure scenarios.
