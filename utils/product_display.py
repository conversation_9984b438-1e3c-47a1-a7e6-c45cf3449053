"""
Professional Product Display Components
Optimized layouts for displaying multiple products with enhanced visual hierarchy

This module provides comprehensive card catalogue display functionality with:
- Modern emoji-based UI with heavy vertical bar separators (❘) for better visibility
- Responsive design for mobile, tablet, and desktop devices
- Comprehensive pagination system with Previous/Next and page indicators
- Accessible button layouts with proper touch targets (48px mobile, 44px desktop)
- Clean code with removed deprecated functions and commented-out code
- Enhanced mobile responsiveness with CSS media queries
- Intelligent field filtering for cleaner displays

Key Features:
- Card catalogue format with emojis (💳🏦📋📍✅💰♻️)
- Single-line sections with heavy vertical bar (❘) separators
- Bold formatting for important values like BIN numbers
- Responsive button layouts (1 button/row mobile, 2 tablet, 3 desktop)
- Comprehensive pagination with jump buttons for long lists
- Mobile-first CSS media queries for proper responsive behavior
"""

from __future__ import annotations

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from numbers import Number

from textwrap import shorten

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

logger = logging.getLogger(__name__)


class ProductDisplayFormatter:
    """Enhanced product display formatting with professional layouts"""

    # Enhanced visual separators and styling with improved aesthetics
    SECTION_SEPARATOR = "─" * 40
    ITEM_SEPARATOR = "—" * 25
    HIGHLIGHT_SEPARATOR = "═" * 30
    CARD_SEPARATOR = "┄" * 20
    PRICE_SEPARATOR = "━" * 20

    # Optimized card borders with clean, professional appearance
    CARD_CORNER = "╭─────────────────────────╮"
    CARD_BOTTOM = "╰─────────────────────────╯"
    CARD_DIVIDER = "├─────────────────────────┤"
    CARD_SECTION_BREAK = "│ ┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈ │"

    # Enhanced visual elements with better contrast
    VERIFIED_BADGE = "🔐✨"
    PREMIUM_BADGE = "⭐💎"
    QUALITY_BADGE = "💎"
    AVAILABILITY_BADGE = "📦"

    # Enhanced emojis for different data types with better visual hierarchy
    FIELD_EMOJIS = {
        "bank": "🏦",
        "country": "🌍",
        "brand": "💳",
        "type": "📋",
        "level": "⭐",
        "quality": "💎",  # Changed from 💠 for better visibility
        "bin": "🔢",
        "location": "📍",
        "price": "💰",
        "expiry": "⏰",
        "address": "🏠",  # Changed from 📍 to differentiate from location
        "refundable": "♻️",
        "zip": "📮",  # Changed from 📍 to differentiate
        "state": "🏙️",
        "city": "🏘️",
        "status": "📊",
        "id": "🆔",
        "availability": "✅",
        "verification": "🔐",
    }

    FIELD_LABELS = {
        "bin": "BIN",
        "bank": "Bank",
        "country": "Country",
        "location": "Location",
        "brand": "Brand",
        "type": "Type",
        "level": "Level",
        "quality": "Quality",
        "price": "Price",
        "expiry": "Expiry",
        "address": "Address",
        "refundable": "Refundable",
        "state": "State",
        "city": "City",
        "zip": "ZIP",
        "status": "Status",
    }

    PRIMARY_FIELD_ORDER = [
        "bin",
        "bank",
        "location",
        "level",
        "quality",
        "type",
        "expiry",
        "address",
        "refundable",
        "price",
        "brand",
    ]

    # Fields to hide from display - removed "base" to show all details
    HIDDEN_FIELDS = {
        "_id",
        "id",
        "sellerusername",
        "sellerUsername",
        "seller_username",
        "seller",
        "refund_rate",
        "isfirsthand",
        "expmonth",
        "expyear",
    }

    FIELD_ALIASES = {"basequality": "quality", "exp": "expiry"}

    VERIFIED_FIELDS = {
        "address": "Address",
        "ip": "IP",
        "email": "Email",
        "phone": "Phone",
        "dob": "DOB",
        "ssn": "SSN",
        "ua": "User Agent",
        "dl": "Driver License",
        "mmn": "MMN",
    }

    def __init__(self):
        self.display_cache: Dict[str, str] = {}
        self.responsive_settings = {
            "mobile": {
                "max_line_length": 60,
                "buttons_per_row": 1,
                "compact_mode": True,
                "wrap_long_lines": True,
            },
            "tablet": {
                "max_line_length": 70,
                "buttons_per_row": 2,
                "compact_mode": False,
                "wrap_long_lines": True,
            },
            "desktop": {
                "max_line_length": 80,
                "buttons_per_row": 3,
                "compact_mode": False,
                "wrap_long_lines": False,
            },
        }

        # Simplified field filtering
        self.filtered_patterns = [
            "id",
            "_id",
            "uuid",
            "token",
            "key",
            "secret",
            "password",
            "auth",
            "session",
            "cookie",
            "created",
            "updated",
            "timestamp",
        ]

    def _wrap_text_intelligently(
        self, text: str, max_length: int, device_type: str = "mobile"
    ) -> List[str]:
        """
        Intelligently wrap text to prevent lines from exceeding specified length

        Args:
            text: Text to wrap
            max_length: Maximum line length
            device_type: Device type for responsive behavior

        Returns:
            List[str]: List of wrapped lines
        """
        if not text or len(text) <= max_length:
            return [text] if text else []

        settings = self.responsive_settings.get(
            device_type, self.responsive_settings["mobile"]
        )

        # Don't wrap on desktop unless absolutely necessary
        if device_type == "desktop" and not settings.get("wrap_long_lines", False):
            return [text]

        lines = []
        current_line = ""

        # Split by logical separators first
        parts = text.split(" • ")

        for i, part in enumerate(parts):
            separator = " • " if i > 0 else ""
            test_line = current_line + separator + part

            if len(test_line) <= max_length:
                current_line = test_line
            else:
                # Current line is full, start new line
                if current_line:
                    lines.append(current_line)

                # Check if part itself is too long
                if len(part) > max_length:
                    # Break long part at word boundaries
                    words = part.split()
                    temp_line = ""

                    for word in words:
                        test_word_line = temp_line + (" " if temp_line else "") + word
                        if len(test_word_line) <= max_length:
                            temp_line = test_word_line
                        else:
                            if temp_line:
                                lines.append(temp_line)
                            temp_line = word

                    current_line = temp_line
                else:
                    current_line = part

        if current_line:
            lines.append(current_line)

        return lines

    def _should_filter_field(self, field_key: str, field_value: Any) -> bool:
        """
        Determine if a field should be filtered out from display

        Args:
            field_key: Field name
            field_value: Field value

        Returns:
            bool: True if field should be filtered out
        """
        field_key_lower = field_key.lower()

        # Check filtered patterns
        for pattern in self.filtered_patterns:
            if pattern in field_key_lower:
                return True

        # Filter out email addresses
        if isinstance(field_value, str) and "@" in field_value and "." in field_value:
            return True

        # Filter out very long strings (likely tokens)
        if isinstance(field_value, str) and len(field_value) > 100:
            return True

        return False

    def _create_enhanced_card_button_text(
        self, serial_number: int, card: Dict[str, Any]
    ) -> str:
        """
        Create enhanced card button text with comprehensive information

        Args:
            serial_number: Card serial number (1, 2, 3, etc.)
            card: Card data dictionary

        Returns:
            str: Enhanced button text with all requested information
        """
        # Get basic card information
        bin_number = card.get("bin", "N/A")
        country = card.get("country", "")
        state = card.get("state", "")
        price = card.get("price")
        refundable = card.get("refundable")

        # Build button text components
        button_parts = []

        # 1. Serial number and BIN
        button_parts.append(f"{serial_number}. {bin_number}")

        # 2. Country and state with flag emoji
        location_part = self._format_location_for_button(country, state)
        if location_part:
            button_parts.append(location_part)

        # 3. Price information
        price_part = self._format_price_for_button(price)
        if price_part:
            button_parts.append(price_part)

        # 4. Refundable status
        refund_part = self._format_refundable_for_button(refundable)
        if refund_part:
            button_parts.append(refund_part)

        # Join with pipe separators for compact display
        return " ❘ ".join(button_parts)

    def _format_location_for_button(self, country: str, state: str) -> str:
        """Format location information for button display"""
        if not country:
            return ""

        # Get country flag emoji
        country_flag = self._get_country_flag_emoji(country)

        # Format location string
        if state and state.strip():
            return f"{state},{country_flag}"
        else:
            return f"{country} {country_flag}"

    def _format_price_for_button(self, price: Any) -> str:
        """Format price information for button display"""
        if price is None or price == "":
            return ""

        try:
            price_float = float(price)
            if price_float == 0:
                return "FREE"
            else:
                return f"${price_float:.2f}"
        except (TypeError, ValueError):
            return str(price)

    def _format_refundable_for_button(self, refundable: Any) -> str:
        """Format refundable status for button display"""
        if refundable is None:
            return ""

        # Check various ways refundable might be represented
        if refundable == 1 or refundable == "1" or refundable is True:
            return "✓"
        elif refundable == 0 or refundable == "0" or refundable is False:
            return "✗"
        else:
            # If it's some other value, try to interpret it
            refundable_str = str(refundable).lower()
            if refundable_str in ["yes", "true", "refundable"]:
                return "✓"
            elif refundable_str in ["no", "false", "non-refundable"]:
                return "✗"
            else:
                return ""

    def _create_compact_card_button_text(
        self, serial_number: int, card: Dict[str, Any]
    ) -> str:
        """
        Create compact card button text for mobile devices

        Args:
            serial_number: Card serial number (1, 2, 3, etc.)
            card: Card data dictionary

        Returns:
            str: Compact button text optimized for mobile
        """
        # Get basic card information
        bin_number = card.get("bin", "N/A")
        country = card.get("country", "")
        price = card.get("price")
        refundable = card.get("refundable")

        # Build compact button text
        button_parts = []

        # 1. Serial number and BIN (always included)
        button_parts.append(f"{serial_number}. {bin_number}")

        # 2. Country with flag (if available)
        if country:
            country_flag = self._get_country_flag_emoji(country)
            button_parts.append(f"{country_flag}")

        # 3. Price (if available)
        price_part = self._format_price_for_button(price)
        if price_part:
            button_parts.append(price_part)

        # 4. Refundable status (if available)
        refund_part = self._format_refundable_for_button(refundable)
        if refund_part:
            button_parts.append(refund_part)

        # Join with spaces for mobile compactness
        return " ".join(button_parts)

    def _format_with_wrapping(self, text: str, device_type: str = "mobile") -> str:
        """
        Format text with intelligent wrapping based on device type

        Args:
            text: Text to format
            device_type: Device type for responsive behavior

        Returns:
            str: Formatted text with appropriate line breaks
        """
        settings = self.responsive_settings.get(
            device_type, self.responsive_settings["mobile"]
        )
        max_length = settings.get("max_line_length", 60)

        if not settings.get("wrap_long_lines", True):
            return text

        wrapped_lines = self._wrap_text_intelligently(text, max_length, device_type)
        return "\n".join(wrapped_lines)

    def format_compact_card(
        self, card: Dict[str, Any], index: int = None, device_type: str = "mobile"
    ) -> str:
        """
        Format a single card in modern compact display mode with emoji-based visual indicators

        Args:
            card: Card data dictionary
            index: Optional index number
            device_type: Device type for responsive formatting

        Returns:
            str: Formatted card display with modern emoji-based layout
        """
        return self._format_modern_card_layout(card, index, device_type, compact=True)

    def _format_modern_card_layout(
        self,
        card: Dict[str, Any],
        index: int = None,
        device_type: str = "mobile",
        compact: bool = True,
    ) -> str:
        """
        Format card using enhanced professional layout with strategic visual elements

        Target Format:
        ◆ BIN: **497856** ─ EXP: 09/25
        🏛️ BANK: CNCE │ 💳 TYPE: CREDIT (CLASSIC)
        🇺🇸 LOC: ALBERT, FR (80300)
        🔒 VERIFIED: ADDR, IP, EMAIL, PHONE | ⭐ QUALITY: UNKNOWN
        💰 PRICE: $7.99 ▲ ◇ REFUNDABLE: ✓
        """
        card_lines = []

        # Line 1: Card BIN and Expiry with enhanced separators
        card_line = self._build_card_header_line(card, index)
        if card_line:
            card_lines.append(card_line)

        # Line 2: Bank and Type information with visual separators
        bank_type_line = self._build_bank_type_line(card)
        if bank_type_line:
            card_lines.append(bank_type_line)

        holder_line = self._build_holder_line(card)
        if holder_line:
            card_lines.append(holder_line)

        # Line 3: Location information (keep country flags for geographic context)
        location_line = self._build_location_line(card)
        if location_line:
            card_lines.append(location_line)

        # Line 4: Verification and Quality with enhanced visual frame
        verification_line = self._build_verification_quality_line(card)
        if verification_line:
            card_lines.append(verification_line)

        # Line 5: Price and Refund with consistent tick marks and visual elements
        price_refund_line = self._build_price_refund_line(card)
        if price_refund_line:
            card_lines.append(price_refund_line)

        # Return enhanced 5-line format with strategic visual elements
        return "\n".join(card_lines)

    def _build_card_header_line(self, card: Dict[str, Any], index: int = None) -> str:
        """Build the enhanced card header line with strategic visual elements"""
        # Card BIN - always display with label
        bin_value = self._get_card_value(card, "bin") or card.get("_id") or "UNKNOWN"
        if not self._is_displayable(bin_value):
            bin_value = "UNKNOWN"

        formatted_bin = self._strip_tags(
            self._format_field_value("bin", bin_value)
        ).upper()

        # Build the main card line with enhanced visual elements
        if index is not None:
            card_line = f"<b>{index}.</b> ◆ BIN: <b>{formatted_bin}</b>"
        else:
            card_line = f"◆ BIN: <b>{formatted_bin}</b>"

        # Add expiry information with enhanced separator
        expiry_display = self._get_expiry_display(card)
        if expiry_display:
            card_line += f" ─ EXP: <b>{expiry_display.upper()}</b>"

        return card_line

    def _build_bank_type_line(self, card: Dict[str, Any]) -> str:
        """Build the bank and type line with enhanced visual elements"""
        parts = []

        # Bank information with enhanced visual indicator
        bank_value = self._get_card_value(card, "bank")
        if self._is_displayable(bank_value):
            formatted_bank = self._strip_tags(
                self._format_field_value("bank", bank_value)
            ).upper()
            parts.append(f"🏛️ BANK: <b>{formatted_bank}</b>")

        # Type and Level with enhanced visual indicator
        type_value = self._get_card_value(card, "type")
        level_value = self._get_card_value(card, "level")
        brand_value = self._get_card_value(card, "brand")

        if self._is_displayable(type_value):
            type_text = self._strip_tags(
                self._format_field_value("type", type_value)
            ).upper()
            if self._is_displayable(level_value):
                level_text = self._strip_tags(
                    self._format_field_value("level", level_value)
                ).upper()
                type_display = f"<b>{type_text}</b> <i>({level_text})</i>"
            else:
                type_display = f"<b>{type_text}</b>"
            parts.append(f"💳 TYPE: {type_display}")

        if self._is_displayable(brand_value):
            brand_text = self._strip_tags(
                self._format_field_value("brand", brand_value)
            ).upper()
            parts.append(f"🏷️ BRAND: <b>{brand_text}</b>")

        return " ❘ ".join(parts) if parts else ""

    def _build_holder_line(self, card: Dict[str, Any]) -> str:
        """Build the card holder line using available name fields."""
        holder_fields = ["name", "cardholder", "holder", "full_name"]

        for field in holder_fields:
            value = self._get_card_value(card, field)
            if self._is_displayable(value):
                holder_text = self._strip_tags(str(value)).strip()
                if holder_text:
                    return f"👤 HOLDER: <b>{holder_text}</b>"

        return ""

    def _get_country_flag_emoji(self, country_code: str) -> str:
        """Get country flag emoji based on country code, fallback to 📍"""
        if not country_code or not isinstance(country_code, str):
            return "📍"

        # Convert country code to uppercase and handle common variations
        country_code = country_code.upper().strip()

        # Map of country codes to flag emojis
        country_flags = {
            "US": "🇺🇸",
            "USA": "🇺🇸",
            "UNITED STATES": "🇺🇸",
            "CA": "🇨🇦",
            "CAN": "🇨🇦",
            "CANADA": "🇨🇦",
            "GB": "🇬🇧",
            "UK": "🇬🇧",
            "UNITED KINGDOM": "🇬🇧",
            "DE": "🇩🇪",
            "DEU": "🇩🇪",
            "GERMANY": "🇩🇪",
            "FR": "🇫🇷",
            "FRA": "🇫🇷",
            "FRANCE": "🇫🇷",
            "IT": "🇮🇹",
            "ITA": "🇮🇹",
            "ITALY": "🇮🇹",
            "ES": "🇪🇸",
            "ESP": "🇪🇸",
            "SPAIN": "🇪🇸",
            "AU": "🇦🇺",
            "AUS": "🇦🇺",
            "AUSTRALIA": "🇦🇺",
            "JP": "🇯🇵",
            "JPN": "🇯🇵",
            "JAPAN": "🇯🇵",
            "CN": "🇨🇳",
            "CHN": "🇨🇳",
            "CHINA": "🇨🇳",
            "IN": "🇮🇳",
            "IND": "🇮🇳",
            "INDIA": "🇮🇳",
            "BR": "🇧🇷",
            "BRA": "🇧🇷",
            "BRAZIL": "🇧🇷",
            "MX": "🇲🇽",
            "MEX": "🇲🇽",
            "MEXICO": "🇲🇽",
            "RU": "🇷🇺",
            "RUS": "🇷🇺",
            "RUSSIA": "🇷🇺",
            "KR": "🇰🇷",
            "KOR": "🇰🇷",
            "SOUTH KOREA": "🇰🇷",
            "NL": "🇳🇱",
            "NLD": "🇳🇱",
            "NETHERLANDS": "🇳🇱",
            "SE": "🇸🇪",
            "SWE": "🇸🇪",
            "SWEDEN": "🇸🇪",
            "NO": "🇳🇴",
            "NOR": "🇳🇴",
            "NORWAY": "🇳🇴",
            "CH": "🇨🇭",
            "CHE": "🇨🇭",
            "SWITZERLAND": "🇨🇭",
            "AT": "🇦🇹",
            "AUT": "🇦🇹",
            "AUSTRIA": "🇦🇹",
            "BE": "🇧🇪",
            "BEL": "🇧🇪",
            "BELGIUM": "🇧🇪",
            "DK": "🇩🇰",
            "DNK": "🇩🇰",
            "DENMARK": "🇩🇰",
            "FI": "🇫🇮",
            "FIN": "🇫🇮",
            "FINLAND": "🇫🇮",
            "PL": "🇵🇱",
            "POL": "🇵🇱",
            "POLAND": "🇵🇱",
            "PT": "🇵🇹",
            "PRT": "🇵🇹",
            "PORTUGAL": "🇵🇹",
            "IE": "🇮🇪",
            "IRL": "🇮🇪",
            "IRELAND": "🇮🇪",
            "GR": "🇬🇷",
            "GRC": "🇬🇷",
            "GREECE": "🇬🇷",
            "TR": "🇹🇷",
            "TUR": "🇹🇷",
            "TURKEY": "🇹🇷",
            "ZA": "🇿🇦",
            "ZAF": "🇿🇦",
            "SOUTH AFRICA": "🇿🇦",
            "EG": "🇪🇬",
            "EGY": "🇪🇬",
            "EGYPT": "🇪🇬",
            "SA": "🇸🇦",
            "SAU": "🇸🇦",
            "SAUDI ARABIA": "🇸🇦",
            "AE": "🇦🇪",
            "ARE": "🇦🇪",
            "UAE": "🇦🇪",
            "SG": "🇸🇬",
            "SGP": "🇸🇬",
            "SINGAPORE": "🇸🇬",
            "MY": "🇲🇾",
            "MYS": "🇲🇾",
            "MALAYSIA": "🇲🇾",
            "TH": "🇹🇭",
            "THA": "🇹🇭",
            "THAILAND": "🇹🇭",
            "VN": "🇻🇳",
            "VNM": "🇻🇳",
            "VIETNAM": "🇻🇳",
            "PH": "🇵🇭",
            "PHL": "🇵🇭",
            "PHILIPPINES": "🇵🇭",
            "ID": "🇮🇩",
            "IDN": "🇮🇩",
            "INDONESIA": "🇮🇩",
            "AR": "🇦🇷",
            "ARG": "🇦🇷",
            "ARGENTINA": "🇦🇷",
            "CL": "🇨🇱",
            "CHL": "🇨🇱",
            "CHILE": "🇨🇱",
            "CO": "🇨🇴",
            "COL": "🇨🇴",
            "COLOMBIA": "🇨🇴",
            "PE": "🇵🇪",
            "PER": "🇵🇪",
            "PERU": "🇵🇪",
            "VE": "🇻🇪",
            "VEN": "🇻🇪",
            "VENEZUELA": "🇻🇪",
            "IL": "🇮🇱",
            "ISR": "🇮🇱",
            "ISRAEL": "🇮🇱",
            "UA": "🇺🇦",
            "UKR": "🇺🇦",
            "UKRAINE": "🇺🇦",
            "CZ": "🇨🇿",
            "CZE": "🇨🇿",
            "CZECH REPUBLIC": "🇨🇿",
            "HU": "🇭🇺",
            "HUN": "🇭🇺",
            "HUNGARY": "🇭🇺",
            "RO": "🇷🇴",
            "ROU": "🇷🇴",
            "ROMANIA": "🇷🇴",
            "BG": "🇧🇬",
            "BGR": "🇧🇬",
            "BULGARIA": "🇧🇬",
            "HR": "🇭🇷",
            "HRV": "🇭🇷",
            "CROATIA": "🇭🇷",
            "SI": "🇸🇮",
            "SVN": "🇸🇮",
            "SLOVENIA": "🇸🇮",
            "SK": "🇸🇰",
            "SVK": "🇸🇰",
            "SLOVAKIA": "🇸🇰",
            "LT": "🇱🇹",
            "LTU": "🇱🇹",
            "LITHUANIA": "🇱🇹",
            "LV": "🇱🇻",
            "LVA": "🇱🇻",
            "LATVIA": "🇱🇻",
            "EE": "🇪🇪",
            "EST": "🇪🇪",
            "ESTONIA": "🇪🇪",
            "IS": "🇮🇸",
            "ISL": "🇮🇸",
            "ICELAND": "🇮🇸",
            "LU": "🇱🇺",
            "LUX": "🇱🇺",
            "LUXEMBOURG": "🇱🇺",
            "MT": "🇲🇹",
            "MLT": "🇲🇹",
            "MALTA": "🇲🇹",
            "CY": "🇨🇾",
            "CYP": "🇨🇾",
            "CYPRUS": "🇨🇾",
        }

        return country_flags.get(country_code, "📍")

    def _build_location_line(self, card: Dict[str, Any]) -> str:
        """Build the location line with country flag emoji and uppercase formatting"""
        location_text = self._format_location(card)
        if location_text:
            # Clean up location formatting and convert to uppercase
            clean_location = self._strip_tags(location_text).upper()

            # Get country flag emoji based on country code
            country_code = self._get_card_value(card, "country")
            location_emoji = self._get_country_flag_emoji(country_code)

            return f"{location_emoji} LOC: <i>{clean_location}</i>"
        return ""

    def _build_verification_quality_line(self, card: Dict[str, Any]) -> str:
        """Build verification and quality line with enhanced visual elements"""
        parts = []

        # Verification status with enhanced visual indicator
        verified_items = []
        verification_fields = {
            "address": "ADDR",
            "ip": "IP",
            "email": "EMAIL",
            "phone": "PHONE",
            "dob": "DOB",
            "ssn": "SSN",
            "ua": "UA",
            "dl": "DL",
            "mmn": "MMN",
        }

        for field_key, label in verification_fields.items():
            value = self._get_card_value(card, field_key)
            if self._is_truthy_flag(value):
                verified_items.append(label)

        if verified_items:
            verified_text = ", ".join(verified_items)
            parts.append(f"🔒 VERIFIED: <b>{verified_text}</b>")

        # Base quality indicator with enhanced visual element
        quality_value = self._get_card_value(card, "basequality")
        if self._is_displayable(quality_value):
            quality_text = self._strip_tags(
                self._format_field_value("basequality", quality_value)
            ).upper()
            parts.append(f"⭐ QUALITY: <i>{quality_text}</i>")

        return " ❘ ".join(parts) if parts else ""

    def _build_price_refund_line(self, card: Dict[str, Any]) -> str:
        """Build price and refund line with enhanced visual elements and consistent tick marks"""
        parts = []

        # Price with enhanced visual indicator
        price_value = self._get_card_value(card, "price")
        if self._is_displayable(price_value):
            try:
                price_float = float(price_value)
                if price_float == 0:
                    price_display = "<b>FREE</b> 🎁"
                else:
                    price_display = f"<b>${price_float:.2f}</b>"

                parts.append(f"💰 PRICE: {price_display}")
            except (ValueError, TypeError):
                parts.append(f"💰 PRICE: <b>{str(price_value).upper()}</b>")

        # Refundable status with consistent tick marks and enhanced visual frame
        refundable_value = self._get_card_value(card, "refundable")
        if refundable_value is not None:
            refund_status = "✓" if self._is_truthy_flag(refundable_value) else "✗"
            parts.append(f"◇ REFUNDABLE: {refund_status}")

        # Note: Removed rate display as requested

        return " ▲ ❘ ".join(parts) if parts else ""

    def _get_expiry_display(self, card: Dict[str, Any]) -> str:
        """Get expiry display in MM/YY format, prioritizing exp field, then expmonth/expyear"""
        # First try the 'exp' field
        exp_value = self._get_card_value(card, "exp")
        if self._is_displayable(exp_value):
            formatted = self._format_expiry_for_display(exp_value)
            if formatted:
                return formatted

        # Then try combining expmonth and expyear
        month = self._get_card_value(card, "expmonth")
        year = self._get_card_value(card, "expyear")

        if self._is_displayable(month) and self._is_displayable(year):
            # Normalize month to MM format
            try:
                month_int = int(str(month).strip())
                if 1 <= month_int <= 12:
                    month_str = f"{month_int:02d}"
                else:
                    return ""
            except (ValueError, TypeError):
                return ""

            # Normalize year to YY format
            try:
                year_str = str(year).strip()
                if len(year_str) == 4:
                    year_str = year_str[-2:]  # Convert YYYY to YY
                elif len(year_str) == 2:
                    pass  # Already in YY format
                else:
                    return ""

                return f"{month_str}/{year_str}"
            except (ValueError, TypeError):
                return ""

        return ""

    def _format_expiry_for_display(self, expiry_value: Any) -> str:
        """Format expiry value for consistent MM/YY display"""
        if not expiry_value:
            return ""

        expiry_str = str(expiry_value).strip()

        # Handle MM/YY format (already correct)
        if "/" in expiry_str and len(expiry_str) == 5:
            parts = expiry_str.split("/")
            if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                month, year = parts
                # Ensure MM format for month
                month = month.zfill(2)
                # Ensure YY format for year
                if len(year) == 4:
                    year = year[-2:]
                elif len(year) == 1:
                    year = year.zfill(2)
                return f"{month}/{year}"

        # Handle MMYY format (convert to MM/YY)
        if len(expiry_str) == 4 and expiry_str.isdigit():
            month = expiry_str[:2]
            year = expiry_str[2:]
            return f"{month}/{year}"

        # Handle MMYYYY format (convert to MM/YY)
        if len(expiry_str) == 6 and expiry_str.isdigit():
            month = expiry_str[:2]
            year = expiry_str[4:]
            return f"{month}/{year}"

        # Use existing formatting as fallback and clean it
        formatted = self._format_field_value("expiry", expiry_value)
        cleaned = self._strip_tags(formatted)

        # Try to extract MM/YY pattern from cleaned text
        import re

        match = re.search(r"(\d{1,2})[/\-]?(\d{2,4})", cleaned)
        if match:
            month, year = match.groups()
            month = month.zfill(2)
            if len(year) == 4:
                year = year[-2:]
            return f"{month}/{year}"

        return cleaned

    def _create_strategic_detail_line(
        self,
        card: Dict[str, Any],
        entry_map: Dict[str, Tuple[str, str, str]],
        used_keys: set,
    ) -> str:
        """
        Create a strategic additional detail line with the most valuable information

        Args:
            card: Card data dictionary
            entry_map: Available field entries
            used_keys: Set to track used keys

        Returns:
            str: Strategic detail line or empty string
        """
        strategic_parts = []

        # Priority 1: Card Type + Level combination (check card directly if entry_map is empty)
        type_entry = entry_map.get("type")
        level_entry = entry_map.get("level")

        # If entry_map is empty, get values directly from card
        if not entry_map:
            type_value = self._get_card_value(card, "type")
            level_value = self._get_card_value(card, "level")

            if self._is_displayable(type_value) and self._is_displayable(level_value):
                strategic_parts.append(f"📋 <b>{type_value}</b> • <i>{level_value}</i>")
                used_keys.add("type")
                used_keys.add("level")
            elif self._is_displayable(type_value):
                strategic_parts.append(f"📋 <b>{type_value}</b>")
                used_keys.add("type")
            elif self._is_displayable(level_value):
                strategic_parts.append(f"📋 <i>{level_value}</i>")
                used_keys.add("level")
        else:
            # Use entry_map when available
            if type_entry and level_entry:
                strategic_parts.append(
                    f"📋 <b>{type_entry[2]}</b> • <i>{level_entry[2]}</i>"
                )
                used_keys.add("type")
                used_keys.add("level")
            elif type_entry:
                strategic_parts.append(f"📋 <b>{type_entry[2]}</b>")
                used_keys.add("type")
            elif level_entry:
                strategic_parts.append(f"📋 <i>{level_entry[2]}</i>")
                used_keys.add("level")

        # Priority 2: Enhanced verification status with count
        verified_fields = []
        for field_key in self.VERIFIED_FIELDS.keys():
            value = self._get_card_value(card, field_key)
            if self._is_displayable(value):
                verified_fields.append(field_key)

        if verified_fields:
            verification_count = len(verified_fields)
            if verification_count >= 3:
                verification_status = (
                    f"🔐✨ <b>Fully Verified</b> ({verification_count} fields)"
                )
            elif verification_count == 2:
                verification_status = (
                    f"🔐 <b>Well Verified</b> ({verification_count} fields)"
                )
            else:
                verification_status = (
                    f"🔒 <b>Basic Verification</b> ({verification_count} field)"
                )

            if not strategic_parts:  # Only add if no type/level info
                strategic_parts.append(verification_status)

        # Priority 3: Availability indicator (if no other strategic info)
        if not strategic_parts:
            # Check for stock or availability indicators
            stock_indicators = ["stock", "available", "quantity", "inventory"]
            for indicator in stock_indicators:
                value = self._get_card_value(card, indicator)
                if self._is_displayable(value):
                    try:
                        stock_num = float(value)
                        if stock_num > 10:
                            strategic_parts.append("📦 <b>High Stock</b> 🔥")
                        elif stock_num > 5:
                            strategic_parts.append("📦 <b>Available</b> ✅")
                        elif stock_num > 0:
                            strategic_parts.append("📦 <b>Limited Stock</b> ⚡")
                        else:
                            strategic_parts.append("📦 <i>Out of Stock</i> ❌")
                        break
                    except (ValueError, TypeError):
                        strategic_parts.append("📦 <b>Available</b> ✅")
                        break

        return " • ".join(strategic_parts)

    def format_detailed_card(
        self, card: Dict[str, Any], index: int = None, device_type: str = "mobile"
    ) -> str:
        """
        Format a single card in detailed display mode using modern emoji-based layout

        Args:
            card: Card data dictionary
            index: Optional index number
            device_type: Device type for responsive formatting

        Returns:
            str: Formatted detailed card display with modern emoji-based layout in bordered format
        """
        card_lines = []

        # Add visual card border
        card_lines.append(self.CARD_CORNER)

        # Get the modern card layout content
        modern_content = self._format_modern_card_layout(
            card, index, device_type, compact=False
        )

        # Add each line of modern content within the card border
        for line in modern_content.split("\n"):
            if line.strip():  # Only add non-empty lines
                card_lines.append(f"│ {line}")

        # Add section break for additional details if needed
        additional_details = self._get_additional_card_details(card)
        if additional_details:
            card_lines.append(self.CARD_SECTION_BREAK)
            for detail_line in additional_details:
                card_lines.append(f"│ {detail_line}")

        # Add bottom border
        card_lines.append(self.CARD_BOTTOM)

        return "\n".join(card_lines)

    def _get_additional_card_details(self, card: Dict[str, Any]) -> List[str]:
        """Get additional card details for detailed view"""
        details = []

        # Stock information
        stock_value = self._get_card_value(card, "stock")
        if self._is_displayable(stock_value):
            try:
                stock_num = int(stock_value)
                if stock_num > 0:
                    stock_indicator = (
                        "📦 High Stock" if stock_num > 10 else "📦 Limited Stock"
                    )
                    details.append(f"{stock_indicator}: {stock_num} available")
            except (ValueError, TypeError):
                details.append(f"📦 Stock: {stock_value}")

        # Additional verification details
        verified_count = sum(
            1
            for field in self.VERIFIED_FIELDS.keys()
            if self._is_displayable(self._get_card_value(card, field))
        )
        if verified_count > 4:
            details.append(
                f"🔐 Enhanced Verification: {verified_count} fields verified"
            )

        # Country and region details
        country_value = self._get_card_value(card, "country")
        if self._is_displayable(country_value):
            country_text = self._strip_tags(
                self._format_field_value("country", country_value)
            )
            details.append(f"🌍 Country: {country_text}")

        base_value = self._get_card_value(card, "base")
        if self._is_displayable(base_value):
            base_text = self._strip_tags(str(base_value)).strip()
            if base_text:
                details.append(f"🧬 Base: {shorten(base_text, width=80, placeholder='…')}")

        price_text_value = self._get_card_value(card, "price_text")
        if self._is_displayable(price_text_value):
            price_text = self._strip_tags(str(price_text_value)).strip()
            if price_text:
                details.append(f"🏷️ Offer: {shorten(price_text, width=80, placeholder='…')}")

        phone_value = self._get_card_value(card, "phone")
        if self._is_displayable(phone_value):
            phone_text = self._strip_tags(
                self._format_field_value("phone", phone_value)
            )
            if phone_text:
                details.append(f"📞 Phone: {phone_text}")

        if self._is_truthy_flag(self._get_card_value(card, "dob")):
            details.append("🎂 DOB Included")

        return details

    def _strip_tags(self, text: str) -> str:
        """Remove HTML tags from text for clean display"""
        if not isinstance(text, str):
            return str(text)

        # Remove common HTML tags
        import re

        clean_text = re.sub(r"<[^>]+>", "", text)
        return clean_text.strip()

    def _collect_card_fields(
        self, card: Dict[str, Any]
    ) -> List[Tuple[str, str, str, str]]:
        """Gather ordered card fields excluding hidden ones."""
        entries: List[Tuple[str, str, str, str]] = []
        seen: set[str] = set()

        def add_field(field_key: str, raw_value: Any):
            normalized_key = self.FIELD_ALIASES.get(field_key, field_key)

            if (
                normalized_key in seen
                or normalized_key in self.HIDDEN_FIELDS
                or normalized_key == "bin"
            ):
                return

            if normalized_key in {"city", "state", "country", "zip"}:
                return

            if not self._is_displayable(raw_value):
                return

            formatted_value = self._format_field_value(normalized_key, raw_value)
            if formatted_value == "":
                return

            label = self.FIELD_LABELS.get(
                normalized_key, normalized_key.replace("_", " ").title()
            )
            emoji = self.FIELD_EMOJIS.get(normalized_key, "•")

            entries.append((normalized_key, label, emoji, formatted_value))
            seen.add(normalized_key)

        for field_key in self.PRIMARY_FIELD_ORDER:
            if field_key == "location":
                location_value = self._format_location(card)
                if location_value and "location" not in seen:
                    label = self.FIELD_LABELS.get("location", "Location")
                    emoji = self.FIELD_EMOJIS.get("location", "📍")
                    entries.append(("location", label, emoji, location_value))
                    seen.add("location")
                continue
            value = self._get_card_value(card, field_key)
            if value is not None:
                add_field(field_key, value)

        for field_key in card.keys():
            add_field(field_key, card[field_key])

        return entries

    def _format_field_value(self, field_key: str, value: Any) -> str:
        """Format field values consistently for display."""
        if isinstance(value, bool):
            return "✅" if value else ""

        if isinstance(value, Number):
            if value == 0:
                return ""
            if field_key == "price":
                return self._as_code(f"${float(value):.2f}")
            if value == 1:
                return "✅"
            return self._as_code(f"{value}")

        if isinstance(value, str):
            stripped = value.strip()
            if not stripped:
                return ""
            if stripped.lower() in {"unavailable", "n/a", "none", "null", "0"}:
                return ""
            if stripped == "1":
                return "✅"
            if field_key == "price":
                try:
                    numeric = float(stripped)
                    return "" if numeric == 0 else self._as_code(f"${numeric:.2f}")
                except (TypeError, ValueError):
                    pass
            return self._as_code(stripped)

        if field_key == "price":
            try:
                numeric = float(value)
                if numeric == 0:
                    return ""
                return self._as_code(f"${numeric:.2f}")
            except (TypeError, ValueError):
                pass

        if isinstance(value, (list, tuple, set)):
            formatted_items = [
                self._format_field_value(field_key, item) for item in value
            ]
            items = [item for item in formatted_items if item]
            return ", ".join(items)

        if isinstance(value, dict):
            formatted_pairs = []
            for key, val in value.items():
                formatted = self._format_field_value(key, val)
                if formatted:
                    formatted_pairs.append(f"{key}: {formatted}")
            return ", ".join(formatted_pairs)

        return self._as_code(str(value))

    def _is_displayable(self, value: Any) -> bool:
        """Determine whether a value should be shown."""
        if value is None:
            return False

        if isinstance(value, bool):
            return value is True

        if isinstance(value, Number):
            return value != 0

        if isinstance(value, str):
            stripped = value.strip()
            if not stripped:
                return False
            return stripped.lower() not in {"unavailable", "n/a", "none", "null", "0"}

        if isinstance(value, (list, tuple, set)):
            return any(self._is_displayable(item) for item in value)

        if isinstance(value, dict):
            return any(self._is_displayable(val) for val in value.values())

        return True

    def _is_truthy_flag(self, value: Any) -> bool:
        """Interpret common truthy flag representations from API responses."""
        if value is None:
            return False

        if isinstance(value, bool):
            return value

        if isinstance(value, (int, float)):
            return value == 1

        if isinstance(value, str):
            normalized = value.strip().lower()
            return normalized in {
                "1",
                "yes",
                "true",
                "y",
                "present",
                "included",
                "available",
                "✅",
                "✔",
            }

        return False

    def _format_location(self, card: Dict[str, Any]) -> str:
        """Combine city, state, country into a single location string, excluding email-format ZIP codes."""
        parts: List[str] = []

        city = card.get("city")
        state = card.get("state")
        country = card.get("country")
        postal_code = card.get("zip")

        if self._is_displayable(city):
            formatted = self._format_field_value("city", city)
            if formatted:
                parts.append(self._strip_tags(formatted))

        if self._is_displayable(state):
            formatted = self._format_field_value("state", state)
            if formatted:
                parts.append(self._strip_tags(formatted))

        # Only include ZIP if it's not an email address
        if self._is_displayable(postal_code):
            postal_str = str(postal_code).strip()
            # Check if ZIP contains @ symbol (email format)
            if "@" not in postal_str:
                formatted = self._format_field_value("zip", postal_code)
                if formatted:
                    parts.append(self._strip_tags(formatted))

        if self._is_displayable(country):
            formatted = self._format_field_value("country", country)
            if formatted:
                parts.append(self._strip_tags(formatted))

        return ", ".join(parts)

    def _get_card_value(self, card: Dict[str, Any], field_key: str) -> Any:
        """Retrieve a card value by field name with alias support."""
        if field_key in card:
            return card[field_key]

        for original_key, alias in self.FIELD_ALIASES.items():
            if alias == field_key and original_key in card:
                return card[original_key]

        return card.get(field_key)

    def _get_field_group(self, field_key: str) -> str:
        """Determine logical grouping for UI layout."""
        if field_key in {"bank", "brand", "price"}:
            return "overview"
        if field_key in {"location", "address"}:
            return "location"
        if field_key in {"quality", "level", "type", "expiry", "refundable"}:
            return "details"
        return "extras"

    def _as_code(self, text: str) -> str:
        """Wrap text in a code tag unless already formatted or not needed."""
        if not text:
            return ""
        if "✅" in text or "<code>" in text:
            return text
        return f"<code>{text}</code>"

    def _create_price_bubble(self, price_value: str) -> List[str]:
        """Create a refined price highlight without heavy box styling."""
        formatted_price = self._as_code(price_value)
        highlight = f"💰 Price: {formatted_price}"
        return [highlight]

    def _create_enhanced_price_display(self, price_value: str) -> List[str]:
        """Create an enhanced price display with better visual appeal and categorization."""
        try:
            price_float = float(price_value)
            if price_float == 0:
                return [f"🎁 <b>FREE</b> ✨"]
            elif price_float < 1:
                formatted_price = f"${price_float:.2f}"
                return [f"💸 <b>{formatted_price}</b> <i>(Budget)</i>"]
            elif price_float < 10:
                formatted_price = f"${price_float:.2f}"
                return [f"💰 <b>{formatted_price}</b> <i>(Standard)</i>"]
            else:
                formatted_price = f"${price_float:.2f}"
                return [f"💎 <b>{formatted_price}</b> <i>(Premium)</i>"]
        except (ValueError, TypeError):
            return [f"💰 <b>{str(price_value)}</b>"]

    def _format_inline_expiry(self, card: Dict[str, Any]) -> str:
        """Build inline expiry text combining month and year when available."""
        month = self._normalize_expiry_month(self._get_card_value(card, "expmonth"))
        year = self._normalize_expiry_year(self._get_card_value(card, "expyear"))
        raw_expiry = self._get_card_value(card, "expiry")

        if (not month or not year) and isinstance(raw_expiry, str):
            parsed_month, parsed_year = self._parse_expiry_string(raw_expiry)
            month = month or parsed_month
            year = year or parsed_year

        if month and year:
            month_tag = self._as_code(month)
            year_tag = self._as_code(year)
            return f"⏳ Exp {month_tag} / {year_tag}"

        if self._is_displayable(raw_expiry):
            formatted = self._format_field_value("expiry", raw_expiry)
            if formatted:
                return f"⏳ Exp {formatted}"

        return ""

    @staticmethod
    def _normalize_expiry_month(value: Any) -> str:
        """Normalize month values to two-digit strings."""
        if value is None:
            return ""

        if isinstance(value, Number):
            month_int = int(value)
        elif isinstance(value, str):
            digits = re.sub(r"\D", "", value)
            if not digits:
                return ""
            month_int = int(digits)
        else:
            return ""

        if not 1 <= month_int <= 12:
            return ""
        return f"{month_int:02d}"

    @staticmethod
    def _normalize_expiry_year(value: Any) -> str:
        """Normalize year values to either two or four digits."""
        if value is None:
            return ""

        if isinstance(value, Number):
            year_int = int(value)
            if year_int <= 0:
                return ""
            return f"{year_int:02d}" if year_int < 100 else str(year_int)

        if isinstance(value, str):
            digits = re.sub(r"\D", "", value)
            if not digits:
                return ""
            if len(digits) <= 2:
                return f"{int(digits):02d}"
            if len(digits) == 4:
                return digits
            if len(digits) > 4:
                return digits[-4:]

        return ""

    @staticmethod
    def _parse_expiry_string(value: str) -> Tuple[str, str]:
        """Extract month and year components from a mixed expiry string."""
        if not value:
            return "", ""

        match = re.search(r"(0?[1-9]|1[0-2])\D*([0-9]{2,4})", value)
        if not match:
            return "", ""

        month = f"{int(match.group(1)):02d}"
        year_digits = match.group(2)
        if len(year_digits) <= 2:
            year = f"{int(year_digits):02d}"
        elif len(year_digits) == 4:
            year = year_digits
        else:
            year = year_digits[-4:]

        return month, year

    @staticmethod
    def _group_inline(entries: List[str], items_per_line: int = 2) -> List[str]:
        """Group multiple detail items per line for a tighter layout."""
        if not entries:
            return []

        grouped: List[str] = []
        for index in range(0, len(entries), items_per_line):
            chunk = entries[index : index + items_per_line]
            grouped.append("   ".join(chunk))
        return grouped

    @staticmethod
    def _append_group_value(
        groups: Dict[str, List[str]], group: str, value: str
    ) -> None:
        """Append a value to a group if it is non-empty and not already present."""
        if not value:
            return

        if value not in groups.get(group, []):
            groups[group].append(value)

    def format_card_grid(
        self,
        cards: List[Dict[str, Any]],
        display_mode: str = "compact",
        max_per_page: int = 5,
    ) -> str:
        """
        Format multiple cards in a grid layout

        Args:
            cards: List of card data
            display_mode: "compact" or "detailed"
            max_per_page: Maximum cards per page

        Returns:
            str: Formatted cards grid
        """
        if not cards:
            return (
                "📭 <b>No Cards Available</b>\n\n"
                "<i>No cards found matching your criteria.</i>\n"
                "Try adjusting your filters or search terms."
            )

        # Limit cards to max per page
        display_cards = cards[:max_per_page]

        formatted_cards = []
        for i, card in enumerate(display_cards, 1):
            if display_mode == "detailed":
                formatted_card = self.format_detailed_card(card, i)
            else:
                formatted_card = self.format_compact_card(card, i)

            formatted_cards.append(formatted_card)

        # Enhanced separation between cards based on display mode
        if display_mode == "detailed":
            separator = f"\n\n{self.SECTION_SEPARATOR}\n\n"
        else:
            separator = f"\n\n{self.CARD_SEPARATOR}\n\n"

        return separator.join(formatted_cards)

    def format_responsive_card_grid(
        self,
        cards: List[Dict[str, Any]],
        display_mode: str = "compact",
        max_per_page: int = 5,
        device_type: str = "mobile",
    ) -> str:
        """
        Format multiple cards with responsive design considerations

        Args:
            cards: List of card data
            display_mode: "compact" or "detailed"
            max_per_page: Maximum cards per page
            device_type: "mobile", "tablet", or "desktop"

        Returns:
            str: Formatted cards grid optimized for device type
        """
        if not cards:
            return self._get_responsive_empty_message(device_type)

        settings = self.responsive_settings.get(
            device_type, self.responsive_settings["mobile"]
        )

        # Adjust display mode based on device capabilities
        if settings["compact_mode"] and display_mode == "detailed":
            display_mode = "compact"

        # Limit cards based on device type
        if device_type == "mobile":
            display_cards = cards[: min(max_per_page, 3)]  # Fewer cards on mobile
        else:
            display_cards = cards[:max_per_page]

        formatted_cards = []
        for i, card in enumerate(display_cards, 1):
            if display_mode == "detailed" and settings["show_full_details"]:
                formatted_card = self.format_detailed_card(card, i)
            else:
                formatted_card = self._format_responsive_compact_card(card, i, settings)

            formatted_cards.append(formatted_card)

        # Responsive separation
        if device_type == "mobile":
            separator = f"\n\n{self.CARD_SEPARATOR}\n\n"
        else:
            separator = f"\n\n{self.ITEM_SEPARATOR}\n\n"

        return separator.join(formatted_cards)

    def _get_responsive_empty_message(self, device_type: str) -> str:
        """Get device-appropriate empty state message"""
        if device_type == "mobile":
            return (
                "📭 <b>No Cards</b>\n\n"
                "<i>No matches found.</i>\n"
                "Try different filters."
            )
        else:
            return (
                "📭 <b>No Cards Available</b>\n\n"
                "<i>No cards found matching your criteria.</i>\n"
                "Try adjusting your filters or search terms."
            )

    def _format_responsive_compact_card(
        self, card: Dict[str, Any], index: int, settings: Dict[str, Any]
    ) -> str:
        """Format a card with responsive design considerations"""
        max_width = settings["max_card_width"]

        # Use existing compact format but with responsive adjustments
        formatted_card = self.format_compact_card(card, index)

        # Truncate long lines for mobile
        if settings["compact_mode"]:
            lines = formatted_card.split("\n")
            truncated_lines = []

            for line in lines:
                # Remove HTML tags for length calculation
                clean_line = self._strip_tags(line)
                if len(clean_line) > max_width:
                    # Truncate and add ellipsis
                    truncated = line[: max_width - 3] + "..."
                    truncated_lines.append(truncated)
                else:
                    truncated_lines.append(line)

            return "\n".join(truncated_lines)

        return formatted_card

    def format_cards_with_filters(
        self,
        cards: List[Dict[str, Any]],
        active_filters: Dict[str, Any],
        page: int = 1,
        total_count: int = None,
        display_mode: str = "compact",
    ) -> str:
        """
        Format cards with filter information and pagination

        Args:
            cards: List of card data
            active_filters: Currently active filters
            page: Current page number
            total_count: Total number of cards available
            display_mode: Display mode for cards

        Returns:
            str: Complete formatted display with filters and pagination
        """
        # Enhanced header with better visual hierarchy
        header_parts = [f"🛒 <b>Cards Catalog</b>"]

        if page > 1:
            header_parts.append(f"<i>(Page {page})</i>")

        header = " ".join(header_parts)

        # Enhanced filter display
        filter_text = ""
        if active_filters:
            filter_text = self._format_active_filters(active_filters)

        # Format cards with enhanced styling
        cards_text = self.format_card_grid(cards, display_mode)

        # Enhanced pagination info with better visual styling
        pagination_text = ""
        if total_count is not None:
            max_page = (total_count + 4) // 5  # Assuming 5 cards per page
            if max_page > 1:
                pagination_text = f"\n\n📊 <b>Page {page} of {max_page}</b> • <i>Total: {total_count:,} cards</i>"
            else:
                pagination_text = (
                    f"\n\n📊 <i>Showing {len(cards)} of {total_count:,} cards</i>"
                )

        # Combine all parts with enhanced visual separation
        result_parts = [header]

        if filter_text:
            result_parts.append(filter_text)
            result_parts.append(self.HIGHLIGHT_SEPARATOR)

        result_parts.append(cards_text)

        if pagination_text:
            result_parts.append(pagination_text)

        return "\n\n".join(result_parts)

    def _format_active_filters(self, filters: Dict[str, Any]) -> str:
        """Format active filters for display"""
        if not filters:
            return ""

        filter_parts = []

        # Handle price range specially
        price_from = filters.get("priceFrom")
        price_to = filters.get("priceTo")

        if price_from is not None and price_to is not None:
            filter_parts.append(
                f"💰 Price: ${float(price_from):.2f} - ${float(price_to):.2f}"
            )
        elif price_from is not None:
            filter_parts.append(f"💰 Min Price: ${float(price_from):.2f}")
        elif price_to is not None:
            filter_parts.append(f"💰 Max Price: ${float(price_to):.2f}")

        # Handle other filters
        for key, value in filters.items():
            if key in ["priceFrom", "priceTo"] or not value:
                continue

            emoji = self.FIELD_EMOJIS.get(key, "🔍")
            display_key = key.replace("_", " ").title()

            if isinstance(value, bool):
                if value:
                    filter_parts.append(f"{emoji} {display_key}: Yes")
            else:
                filter_parts.append(f"{emoji} {display_key}: {value}")

        if not filter_parts:
            return ""

        return f"🔍 <b>Active Filters:</b>\n" + "\n".join(
            [f"• {part}" for part in filter_parts]
        )

    def create_enhanced_card_keyboard(
        self,
        cards: List[Dict[str, Any]],
        page: int = 1,
        total_count: int = None,
        has_filters: bool = False,
        callback_prefix: str = "catalog",
    ) -> InlineKeyboardMarkup:
        """
        Create an enhanced keyboard for card browsing with improved UX

        Args:
            cards: List of cards being displayed
            page: Current page number
            total_count: Total number of cards
            has_filters: Whether filters are active
            callback_prefix: Prefix for callback data

        Returns:
            InlineKeyboardMarkup: Enhanced keyboard with better organization
        """
        keyboard_rows: List[List[InlineKeyboardButton]] = []

        # Remove "Add All" and "Refresh" buttons as requested

        # Enhanced card buttons with comprehensive information
        if cards:
            card_buttons = []
            for i, card in enumerate(cards[:5], 1):  # Max 5 cards per page
                card_id = card.get("_id")
                button_text = self._create_enhanced_card_button_text(i, card)

                card_buttons.append(
                    InlineKeyboardButton(
                        text=button_text,
                        callback_data=f"{callback_prefix}:add_to_cart:{card_id}",
                    )
                )

            # Organize card buttons in rows of 2 for better mobile experience
            for i in range(0, len(card_buttons), 2):
                keyboard_rows.append(card_buttons[i : i + 2])

        # Optimized pagination system - compact and single-line
        nav_buttons = []
        max_page = (total_count + 4) // 5 if total_count else 1

        # Compact pagination with optimized button sizing
        if page > 1:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="⬅️", callback_data=f"{callback_prefix}:view_cards:{page-1}"
                )
            )

        # Compact page indicator in X/Y format
        nav_buttons.append(
            InlineKeyboardButton(text=f"{page}/{max_page}", callback_data="noop")
        )

        if page < max_page:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="➡️", callback_data=f"{callback_prefix}:view_cards:{page+1}"
                )
            )

        # Always add navigation buttons in a single row
        if nav_buttons:
            keyboard_rows.append(nav_buttons)

        # Simplified utility actions - removed filter buttons as requested
        utility_row = [
            InlineKeyboardButton(text="🔍 Search", callback_data="catalog:search"),
            InlineKeyboardButton(text="🛒 Cart", callback_data="local:cart:view"),
        ]
        keyboard_rows.append(utility_row)

        # Enhanced navigation back with context
        keyboard_rows.append(
            [InlineKeyboardButton(text="⬅️ Back to Browse", callback_data="menu:browse")]
        )

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    def create_responsive_card_keyboard(
        self,
        cards: List[Dict[str, Any]],
        page: int = 1,
        total_count: int = None,
        has_filters: bool = False,
        callback_prefix: str = "catalog",
        device_type: str = "mobile",
    ) -> InlineKeyboardMarkup:
        """
        Create a responsive keyboard optimized for different device types

        Args:
            cards: List of cards being displayed
            page: Current page number
            total_count: Total number of cards
            has_filters: Whether filters are active
            callback_prefix: Prefix for callback data
            device_type: "mobile", "tablet", or "desktop"

        Returns:
            InlineKeyboardMarkup: Responsive keyboard optimized for device
        """
        keyboard_rows: List[List[InlineKeyboardButton]] = []
        settings = self.responsive_settings.get(
            device_type, self.responsive_settings["mobile"]
        )
        buttons_per_row = settings["buttons_per_row"]

        # Remove quick actions as requested (Add All, Refresh buttons)

        # Enhanced responsive card buttons with comprehensive information
        if cards:
            card_buttons = []
            max_cards = 3 if device_type == "mobile" else 5

            for i, card in enumerate(cards[:max_cards], 1):
                card_id = card.get("_id")

                # Use enhanced button text for all device types
                if device_type == "mobile":
                    # Mobile: Use slightly more compact format
                    button_text = self._create_compact_card_button_text(i, card)
                else:
                    # Tablet/Desktop: Use full enhanced format
                    button_text = self._create_enhanced_card_button_text(i, card)

                card_buttons.append(
                    InlineKeyboardButton(
                        text=button_text,
                        callback_data=f"{callback_prefix}:add_to_cart:{card_id}",
                    )
                )

            # Organize card buttons responsively
            for i in range(0, len(card_buttons), buttons_per_row):
                keyboard_rows.append(card_buttons[i : i + buttons_per_row])

        # Responsive navigation
        self._add_responsive_navigation(
            keyboard_rows, page, total_count, callback_prefix, device_type
        )

        # Responsive action buttons
        self._add_responsive_actions(keyboard_rows, has_filters, device_type)

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    def _add_responsive_navigation(
        self,
        keyboard_rows: List[List[InlineKeyboardButton]],
        page: int,
        total_count: int,
        callback_prefix: str,
        device_type: str,
    ) -> None:
        """Add responsive navigation buttons"""
        nav_buttons = []
        max_page = (total_count + 4) // 5 if total_count else 1

        # Unified compact navigation for all device types
        if page > 1:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="⬅️", callback_data=f"{callback_prefix}:view_cards:{page-1}"
                )
            )

        nav_buttons.append(
            InlineKeyboardButton(text=f"{page}/{max_page}", callback_data="noop")
        )

        if page < max_page:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="➡️", callback_data=f"{callback_prefix}:view_cards:{page+1}"
                )
            )

        if nav_buttons:
            # Split navigation responsively
            if device_type == "mobile" or len(nav_buttons) <= 3:
                keyboard_rows.append(nav_buttons)
            else:
                # Split into multiple rows for better mobile experience
                keyboard_rows.append(nav_buttons[:3])
                if len(nav_buttons) > 3:
                    keyboard_rows.append(nav_buttons[3:])

    def _add_responsive_actions(
        self,
        keyboard_rows: List[List[InlineKeyboardButton]],
        has_filters: bool,
        device_type: str,
    ) -> None:
        """Add responsive action buttons - removed filter buttons as requested"""
        # Simplified actions for all device types - no filter buttons
        action_buttons = [
            InlineKeyboardButton(text="🔍 Search", callback_data="catalog:search"),
            InlineKeyboardButton(text="🛒 Cart", callback_data="local:cart:view"),
        ]

        keyboard_rows.append(action_buttons)

        # Back button
        keyboard_rows.append(
            [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:browse")]
        )


class ProductCollectionManager:
    """Manages collections of products with enhanced display capabilities"""

    def __init__(self):
        self.formatter = ProductDisplayFormatter()
        self.collection_cache: Dict[str, List[Dict[str, Any]]] = {}

    def organize_products_by_category(
        self, products: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Organize products by category for better display

        Args:
            products: List of product data

        Returns:
            Dict: Products organized by category
        """
        categories = {}

        for product in products:
            # Determine category based on product attributes
            category = self._determine_product_category(product)

            if category not in categories:
                categories[category] = []

            categories[category].append(product)

        return categories

    def _determine_product_category(self, product: Dict[str, Any]) -> str:
        """Determine the category for a product"""
        # Priority-based categorization
        if product.get("brand"):
            return f"{product['brand']} Cards"
        elif product.get("country"):
            return f"{product['country']} Cards"
        elif product.get("type"):
            return f"{product['type']} Cards"
        else:
            return "Other Cards"

    def create_category_summary(
        self, categories: Dict[str, List[Dict[str, Any]]]
    ) -> str:
        """
        Create a summary of product categories

        Args:
            categories: Products organized by category

        Returns:
            str: Formatted category summary
        """
        if not categories:
            return "📭 <b>No Products Available</b>"

        summary_parts = ["📊 <b>Product Categories</b>"]

        total_products = sum(len(products) for products in categories.values())
        summary_parts.append(f"Total Products: <b>{total_products:,}</b>")
        summary_parts.append("")

        # Sort categories by product count (descending)
        sorted_categories = sorted(
            categories.items(), key=lambda x: len(x[1]), reverse=True
        )

        for category, products in sorted_categories:
            count = len(products)
            # Calculate price range if available
            prices = [
                float(p.get("price", 0)) for p in products if p.get("price") is not None
            ]

            category_line = f"• <b>{category}</b>: {count:,} items"

            if prices:
                min_price = min(prices)
                max_price = max(prices)
                if min_price == max_price:
                    category_line += f" (${min_price:.2f})"
                else:
                    category_line += f" (${min_price:.2f} - ${max_price:.2f})"

            summary_parts.append(category_line)

        return "\n".join(summary_parts)


# Global instances for easy import
product_formatter = ProductDisplayFormatter()
collection_manager = ProductCollectionManager()
