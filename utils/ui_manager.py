"""
Enhanced UI Manager for professional Telegram bot interface
Provides consistent message editing, loading states, and visual enhancements
"""

from __future__ import annotations

import asyncio
import logging
from typing import Optional, Dict, Any, List, Union, Callable
from datetime import datetime

from aiogram.types import (
    CallbackQuery,
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton
)
from aiogram.exceptions import TelegramBadRequest

from utils.texts import DEMO_WATERMARK

logger = logging.getLogger(__name__)


class LoadingState:
    """Enhanced loading state with sophisticated animations and multi-stage progress tracking"""

    def __init__(self, message: str = "Loading...", show_progress: bool = False, stages: List[str] = None):
        self.message = message
        self.show_progress = show_progress
        self.progress = 0
        self.total = 100
        self.start_time = datetime.now()
        self.current_stage = 0
        self.stages = stages or ["Processing..."]
        self.stage_start_time = datetime.now()
        self.estimated_total_time = None
        self.stage_durations = []

    def update_progress(self, current: int, total: int = None):
        """Update progress values"""
        self.progress = current
        if total is not None:
            self.total = total

    def advance_stage(self, stage_name: str = None):
        """Advance to the next stage and track timing"""
        if self.current_stage < len(self.stages):
            # Record duration of completed stage
            stage_duration = (datetime.now() - self.stage_start_time).total_seconds()
            self.stage_durations.append(stage_duration)

        self.current_stage += 1
        self.stage_start_time = datetime.now()

        if stage_name:
            if self.current_stage < len(self.stages):
                self.stages[self.current_stage] = stage_name
            else:
                self.stages.append(stage_name)

    def estimate_remaining_time(self) -> Optional[float]:
        """Estimate remaining time based on stage durations"""
        if not self.stage_durations or self.current_stage >= len(self.stages):
            return None

        avg_stage_duration = sum(self.stage_durations) / len(self.stage_durations)
        remaining_stages = len(self.stages) - self.current_stage

        # Add current stage progress
        current_stage_elapsed = (datetime.now() - self.stage_start_time).total_seconds()
        estimated_current_stage_remaining = max(0, avg_stage_duration - current_stage_elapsed)

        return estimated_current_stage_remaining + (remaining_stages - 1) * avg_stage_duration

    def get_progress_bar(self, width: int = 12) -> str:
        """Generate a sophisticated visual progress bar with enhanced styling"""
        if not self.show_progress or self.total == 0:
            return ""

        filled = int((self.progress / self.total) * width)
        percentage = (self.progress / self.total) * 100

        # Enhanced progress bar with smooth transitions
        if percentage == 100:
            bar = "█" * width
            return f"[{bar}] ✅ <b>Complete!</b>"
        elif percentage >= 90:
            bar = "█" * filled + "▓" * (width - filled)
            return f"[{bar}] 🎯 <b>{percentage:.1f}%</b>"
        elif percentage >= 75:
            bar = "█" * filled + "▒" * (width - filled)
            return f"[{bar}] 🔥 <b>{percentage:.1f}%</b>"
        elif percentage >= 50:
            bar = "█" * filled + "░" * (width - filled)
            return f"[{bar}] ⚡ <b>{percentage:.1f}%</b>"
        elif percentage >= 25:
            bar = "▓" * filled + "░" * (width - filled)
            return f"[{bar}] 🔄 <b>{percentage:.1f}%</b>"
        else:
            bar = "▒" * filled + "░" * (width - filled)
            return f"[{bar}] ⏳ <b>{percentage:.1f}%</b>"

    def get_stage_indicator(self) -> str:
        """Get current stage indicator with visual progress"""
        if self.current_stage >= len(self.stages):
            return "✅ <b>All stages complete</b>"

        current_stage_name = self.stages[self.current_stage]
        stage_progress = f"({self.current_stage + 1}/{len(self.stages)})"

        # Animated stage indicator
        elapsed = (datetime.now() - self.stage_start_time).total_seconds()
        animation_frame = int(elapsed * 3) % 4  # Faster animation for stages

        stage_animations = ["⠋", "⠙", "⠹", "⠸"]
        current_animation = stage_animations[animation_frame]

        return f"{current_animation} <b>{current_stage_name}</b> {stage_progress}"

    def format_message(self) -> str:
        """Format the loading message with sophisticated visual feedback and stage tracking"""
        elapsed = (datetime.now() - self.start_time).total_seconds()

        # Main loading indicator with smooth animation
        animation_frame = int(elapsed * 2) % 8  # Smoother animation cycle
        loading_indicators = ["⏳", "⏰", "⏱️", "⏰", "⏳", "⏰", "⏱️", "⏰"]
        current_indicator = loading_indicators[animation_frame]

        base_msg = f"{current_indicator} <b>{self.message}</b>"

        # Add stage indicator if stages are defined
        if len(self.stages) > 1:
            stage_indicator = self.get_stage_indicator()
            base_msg += f"\n\n{stage_indicator}"

        # Add progress bar if enabled
        if self.show_progress:
            progress_bar = self.get_progress_bar()
            if progress_bar:
                base_msg += f"\n\n{progress_bar}"

        # Add time information for longer operations
        if elapsed > 3:
            time_info = f"<i>Elapsed: {elapsed:.1f}s</i>"

            # Add estimated remaining time if available
            estimated_remaining = self.estimate_remaining_time()
            if estimated_remaining and estimated_remaining > 1:
                time_info += f" • <i>Est. remaining: {estimated_remaining:.1f}s</i>"

            base_msg += f"\n\n{time_info}"

        return base_msg

    def create_multi_stage_loader(self, stages: List[str]) -> 'LoadingState':
        """Create a multi-stage loading state for complex operations"""
        return LoadingState(
            message="Processing request...",
            show_progress=True,
            stages=stages
        )


class UIManager:
    """Enhanced UI Manager for professional bot interface"""

    def __init__(self):
        self.loading_states: Dict[str, LoadingState] = {}
        self.message_cache: Dict[str, Dict[str, Any]] = {}

    async def edit_message_safely(
        self,
        callback: CallbackQuery,
        text: str,
        reply_markup: Optional[InlineKeyboardMarkup] = None,
        parse_mode: str = "HTML",
        add_watermark: bool = True
    ) -> bool:
        """
        Safely edit a message with error handling and consistency

        Args:
            callback: Telegram callback query
            text: New message text
            reply_markup: Optional keyboard markup
            parse_mode: Message parse mode
            add_watermark: Whether to add demo watermark

        Returns:
            bool: Success status
        """
        try:
            if add_watermark:
                text += DEMO_WATERMARK

            # Cache the message content for comparison
            cache_key = f"{callback.from_user.id}_{callback.message.message_id}"
            cached = self.message_cache.get(cache_key, {})

            # Only edit if content actually changed
            if cached.get("text") != text or cached.get("markup") != reply_markup:
                await callback.message.edit_text(
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )

                # Update cache
                self.message_cache[cache_key] = {
                    "text": text,
                    "markup": reply_markup,
                    "updated_at": datetime.now()
                }

            return True

        except TelegramBadRequest as e:
            if "message is not modified" in str(e).lower():
                # Message content is identical, this is fine
                return True
            elif "message to edit not found" in str(e).lower():
                logger.warning(f"Message to edit not found: {e}")
                return False
            else:
                logger.error(f"Telegram error editing message: {e}")
                return False
        except Exception as e:
            logger.error(f"Unexpected error editing message: {e}")
            return False

    async def edit_keyboard_safely(
        self,
        callback: CallbackQuery,
        reply_markup: InlineKeyboardMarkup
    ) -> bool:
        """
        Safely edit only the keyboard markup

        Args:
            callback: Telegram callback query
            reply_markup: New keyboard markup

        Returns:
            bool: Success status
        """
        try:
            await callback.message.edit_reply_markup(reply_markup=reply_markup)
            return True
        except TelegramBadRequest as e:
            if "message is not modified" in str(e).lower():
                return True
            logger.error(f"Error editing keyboard: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error editing keyboard: {e}")
            return False

    async def show_loading(
        self,
        callback: CallbackQuery,
        message: str = "Loading...",
        show_progress: bool = False,
        loading_id: Optional[str] = None
    ) -> str:
        """
        Show a loading state with optional progress tracking

        Args:
            callback: Telegram callback query
            message: Loading message
            show_progress: Whether to show progress bar
            loading_id: Unique ID for this loading state

        Returns:
            str: Loading state ID for updates
        """
        if not loading_id:
            loading_id = f"{callback.from_user.id}_{datetime.now().timestamp()}"

        loading_state = LoadingState(message, show_progress)
        self.loading_states[loading_id] = loading_state

        await self.edit_message_safely(
            callback,
            loading_state.format_message(),
            reply_markup=None
        )

        return loading_id

    async def update_loading(
        self,
        callback: CallbackQuery,
        loading_id: str,
        progress: int,
        total: int = None,
        message: str = None
    ) -> bool:
        """
        Update a loading state with new progress

        Args:
            callback: Telegram callback query
            loading_id: Loading state ID
            progress: Current progress value
            total: Total progress value
            message: Optional new message

        Returns:
            bool: Success status
        """
        loading_state = self.loading_states.get(loading_id)
        if not loading_state:
            return False

        loading_state.update_progress(progress, total)
        if message:
            loading_state.message = message

        return await self.edit_message_safely(
            callback,
            loading_state.format_message(),
            reply_markup=None
        )

    async def hide_loading(
        self,
        callback: CallbackQuery,
        loading_id: str,
        final_text: str,
        final_markup: Optional[InlineKeyboardMarkup] = None
    ) -> bool:
        """
        Hide loading state and show final content

        Args:
            callback: Telegram callback query
            loading_id: Loading state ID
            final_text: Final message text
            final_markup: Final keyboard markup

        Returns:
            bool: Success status
        """
        # Remove loading state
        self.loading_states.pop(loading_id, None)

        return await self.edit_message_safely(
            callback,
            final_text,
            reply_markup=final_markup
        )

    def create_enhanced_keyboard(
        self,
        buttons: List[List[Dict[str, str]]],
        max_width: int = 2,
        add_navigation: bool = True,
        back_callback: str = "menu:main"
    ) -> InlineKeyboardMarkup:
        """
        Create an enhanced keyboard with consistent styling

        Args:
            buttons: Button configuration list
            max_width: Maximum buttons per row
            add_navigation: Whether to add back button
            back_callback: Back button callback data

        Returns:
            InlineKeyboardMarkup: Enhanced keyboard
        """
        keyboard_rows = []

        # Process button rows
        for row in buttons:
            if len(row) <= max_width:
                keyboard_rows.append([
                    InlineKeyboardButton(
                        text=btn["text"],
                        callback_data=btn["callback_data"]
                    ) for btn in row
                ])
            else:
                # Split long rows
                for i in range(0, len(row), max_width):
                    chunk = row[i:i + max_width]
                    keyboard_rows.append([
                        InlineKeyboardButton(
                            text=btn["text"],
                            callback_data=btn["callback_data"]
                        ) for btn in chunk
                    ])

        # Add navigation if requested
        if add_navigation:
            keyboard_rows.append([
                InlineKeyboardButton(text="⬅️ Back", callback_data=back_callback)
            ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    def format_professional_message(
        self,
        title: str,
        content: str,
        sections: Optional[Dict[str, str]] = None,
        footer: Optional[str] = None,
        status_emoji: str = "ℹ️"
    ) -> str:
        """
        Format a professional message with consistent styling

        Args:
            title: Message title
            content: Main content
            sections: Optional sections dict
            footer: Optional footer text
            status_emoji: Status emoji for title

        Returns:
            str: Formatted message
        """
        message_parts = [f"{status_emoji} <b>{title}</b>"]

        if content:
            message_parts.append(content)

        if sections:
            for section_title, section_content in sections.items():
                message_parts.append(f"\n<b>{section_title}:</b>\n{section_content}")

        if footer:
            message_parts.append(f"\n<i>{footer}</i>")

        return "\n\n".join(message_parts)

    def cleanup_cache(self, max_age_minutes: int = 60):
        """Clean up old cached messages"""
        cutoff_time = datetime.now().timestamp() - (max_age_minutes * 60)

        to_remove = []
        for cache_key, cache_data in self.message_cache.items():
            if cache_data.get("updated_at", datetime.min).timestamp() < cutoff_time:
                to_remove.append(cache_key)

        for key in to_remove:
            self.message_cache.pop(key, None)


# Global UI manager instance
ui_manager = UIManager()