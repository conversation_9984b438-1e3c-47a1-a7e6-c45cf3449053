"""
Enhanced Inline Keyboards with Professional Layouts
Provides advanced keyboard layouts with better organization and visual cues
"""

from __future__ import annotations

import logging
import unicodedata
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

logger = logging.getLogger(__name__)


class KeyboardStyle(Enum):
    """Keyboard styling options"""
    COMPACT = "compact"
    SPACIOUS = "spacious"
    GRID = "grid"
    LIST = "list"


class ButtonPriority(Enum):
    """Button priority levels for visual hierarchy"""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    TERTIARY = "tertiary"
    DANGER = "danger"
    SUCCESS = "success"


class EnhancedKeyboardBuilder:
    """Advanced keyboard builder with professional layouts"""

    # Enhanced button styling based on priority with better visual hierarchy
    PRIORITY_STYLES = {
        ButtonPriority.PRIMARY: {"prefix": "🔥", "suffix": "", "style": "bold"},
        ButtonPriority.SECONDARY: {"prefix": "", "suffix": "", "style": "normal"},
        ButtonPriority.TERTIARY: {"prefix": "", "suffix": "", "style": "italic"},
        ButtonPriority.DANGER: {"prefix": "⚠️", "suffix": "", "style": "bold"},
        ButtonPriority.SUCCESS: {"prefix": "✅", "suffix": "", "style": "bold"},
    }

    # Enhanced visual elements for better UX
    VISUAL_ELEMENTS = {
        "loading": "⏳",
        "processing": "🔄",
        "success": "✅",
        "error": "❌",
        "new": "🆕",
        "hot": "🔥",
        "premium": "💎",
        "free": "🎁"
    }

    def __init__(self):
        self.rows: List[List[InlineKeyboardButton]] = []
        self.current_row: List[InlineKeyboardButton] = []
        self.max_width = 2
        self.style = KeyboardStyle.COMPACT

    def set_style(self, style: KeyboardStyle, max_width: int = 2) -> 'EnhancedKeyboardBuilder':
        """Set keyboard style and maximum width"""
        self.style = style
        self.max_width = max_width
        return self

    def add_button(
        self,
        text: str,
        callback_data: str,
        priority: ButtonPriority = ButtonPriority.SECONDARY,
        emoji: str = None,
        url: str = None
    ) -> 'EnhancedKeyboardBuilder':
        """
        Add a button with styling based on priority

        Args:
            text: Button text
            callback_data: Callback data (ignored if url is provided)
            priority: Button priority for styling
            emoji: Optional emoji override
            url: Optional URL for link buttons

        Returns:
            Self for method chaining
        """
        # Apply styling based on priority
        styled_text = self._apply_button_styling(text, priority, emoji)

        # Create button
        if url:
            button = InlineKeyboardButton(text=styled_text, url=url)
        else:
            button = InlineKeyboardButton(text=styled_text, callback_data=callback_data)

        # Add to current row or create new row based on style
        if self.style == KeyboardStyle.LIST or len(self.current_row) >= self.max_width:
            if self.current_row:
                self.rows.append(self.current_row)
                self.current_row = []

        self.current_row.append(button)
        return self

    def add_row(self, buttons: List[Dict[str, Any]]) -> 'EnhancedKeyboardBuilder':
        """
        Add a complete row of buttons

        Args:
            buttons: List of button configurations

        Returns:
            Self for method chaining
        """
        # Finish current row if it has buttons
        if self.current_row:
            self.rows.append(self.current_row)
            self.current_row = []

        # Create new row
        row_buttons = []
        for btn_config in buttons:
            text = btn_config["text"]
            callback_data = btn_config.get("callback_data", "noop")
            priority = ButtonPriority(btn_config.get("priority", "secondary"))
            emoji = btn_config.get("emoji")
            url = btn_config.get("url")

            styled_text = self._apply_button_styling(text, priority, emoji)

            if url:
                button = InlineKeyboardButton(text=styled_text, url=url)
            else:
                button = InlineKeyboardButton(text=styled_text, callback_data=callback_data)

            row_buttons.append(button)

        self.rows.append(row_buttons)
        return self

    def add_separator(self, text: str = "─────────") -> 'EnhancedKeyboardBuilder':
        """Add a visual separator row"""
        if self.current_row:
            self.rows.append(self.current_row)
            self.current_row = []

        # Make separator visually distinct and non-interactive
        # Use a special callback that won't trigger any action
        separator_text = f"━━ {text} ━━"  # Make it visually distinct as a header
        separator_button = InlineKeyboardButton(text=separator_text, callback_data="noop:separator")
        self.rows.append([separator_button])
        return self

    def add_navigation_row(
        self,
        back_text: str = "⬅️ Back",
        back_callback: str = "menu:main",
        additional_buttons: List[Dict[str, str]] = None
    ) -> 'EnhancedKeyboardBuilder':
        """Add a navigation row with back button and optional additional buttons"""
        nav_buttons = [
            InlineKeyboardButton(text=back_text, callback_data=back_callback)
        ]

        if additional_buttons:
            for btn in additional_buttons:
                nav_buttons.append(
                    InlineKeyboardButton(
                        text=btn["text"],
                        callback_data=btn["callback_data"]
                    )
                )

        # Finish current row
        if self.current_row:
            self.rows.append(self.current_row)
            self.current_row = []

        self.rows.append(nav_buttons)
        return self

    def add_pagination_row(
        self,
        current_page: int,
        total_pages: int,
        callback_prefix: str,
        show_page_numbers: bool = True
    ) -> 'EnhancedKeyboardBuilder':
        """Add pagination controls"""
        if self.current_row:
            self.rows.append(self.current_row)
            self.current_row = []

        nav_buttons = []

        # Previous button
        if current_page > 1:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="⬅️ Previous",
                    callback_data=f"{callback_prefix}:{current_page-1}"
                )
            )

        # Page indicator
        if show_page_numbers:
            nav_buttons.append(
                InlineKeyboardButton(
                    text=f"📄 {current_page}/{total_pages}",
                    callback_data="noop"
                )
            )

        # Next button
        if current_page < total_pages:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="Next ➡️",
                    callback_data=f"{callback_prefix}:{current_page+1}"
                )
            )

        if nav_buttons:
            self.rows.append(nav_buttons)

        return self

    def build(self) -> InlineKeyboardMarkup:
        """Build and return the final keyboard"""
        # Add any remaining buttons in current row
        if self.current_row:
            self.rows.append(self.current_row)

        return InlineKeyboardMarkup(inline_keyboard=self.rows)

    def _apply_button_styling(
        self,
        text: str,
        priority: ButtonPriority,
        emoji_override: str = None
    ) -> str:
        """Apply styling to button text based on priority"""
        if emoji_override:
            cleaned_text = self._strip_leading_symbol(text)
            if not cleaned_text:
                cleaned_text = text.strip()
            return f"{emoji_override} {cleaned_text}".strip()

        style = self.PRIORITY_STYLES.get(priority, self.PRIORITY_STYLES[ButtonPriority.SECONDARY])

        styled_text = text.strip()
        if style["prefix"] and not self._starts_with_symbol(styled_text):
            styled_text = f"{style['prefix']} {styled_text}"
        if style["suffix"]:
            styled_text = f"{styled_text} {style['suffix']}"

        return styled_text

    def clear(self) -> 'EnhancedKeyboardBuilder':
        """Clear all rows and start fresh"""
        self.rows = []
        self.current_row = []
        return self

    @staticmethod
    def _starts_with_symbol(text: str) -> bool:
        """Check whether the first non-space character is a symbol/emoji."""
        if not text:
            return False

        stripped = text.lstrip()
        if not stripped:
            return False

        first_char = stripped[0]
        category = unicodedata.category(first_char)
        return category.startswith("S") or category.startswith("P")

    @staticmethod
    def _strip_leading_symbol(text: str) -> str:
        """Remove a leading emoji/symbol and following spaces if present."""
        if not text:
            return ""

        stripped = text.lstrip()
        if not stripped:
            return ""

        index = 0
        length = len(stripped)
        removed = False

        while index < length:
            char = stripped[index]
            category = unicodedata.category(char)

            if category.startswith("S") or category.startswith("P"):
                index += 1
                while index < length and unicodedata.category(stripped[index]) in {"Mn", "Cf"}:
                    index += 1
                while index < length and stripped[index].isspace():
                    index += 1
                removed = True
                break
            break

        return stripped[index:] if removed else stripped


class SmartKeyboardLayouts:
    """Pre-built smart keyboard layouts for common use cases"""

    @staticmethod
    def create_browse_keyboard(
        has_filters: bool = False,
        current_product: str = None,
        current_api: str = None
    ) -> InlineKeyboardMarkup:
        """Create an enhanced browse keyboard"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.COMPACT, 2)

        # Main actions
        builder.add_button("🔎 Search", "catalog:search", ButtonPriority.PRIMARY)
        builder.add_button("🧰 Filters", "catalog:filters", ButtonPriority.PRIMARY)

        # Browse options based on filter state
        if has_filters:
            builder.add_button("📄 Browse Filtered", "catalog:browse_filtered", ButtonPriority.SUCCESS)
            builder.add_button("📋 Browse All", "catalog:browse_all", ButtonPriority.SECONDARY)
            builder.add_button("🧹 Clear Filters", "filter:clear", ButtonPriority.TERTIARY)
        else:
            builder.add_button("📄 Browse All", "catalog:browse_all", ButtonPriority.SUCCESS)

        # Product selection if needed
        if not current_product or not current_api:
            builder.add_separator("Product Selection")
            builder.add_button("🛍️ Select Product", "menu:products", ButtonPriority.DANGER)

        # Navigation
        builder.add_navigation_row(
            additional_buttons=[
                {"text": "🛒 Cart", "callback_data": "local:cart:view"}
            ]
        )

        return builder.build()

    @staticmethod
    def create_filter_keyboard(
        active_filters: Dict[str, bool] = None
    ) -> InlineKeyboardMarkup:
        """Create an enhanced filter selection keyboard"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.GRID, 2)

        active_filters = active_filters or {}

        # Filter categories with visual indicators
        filter_categories = [
            ("location", "📍 Location", "filter:category:location"),
            ("card", "💳 Card", "filter:category:card"),
            ("pricing", "💲 Pricing", "filter:category:pricing"),
            ("contact", "🧾 Contact", "filter:category:contact"),
            ("identity", "🛡 Identity", "filter:category:identity"),
            ("extras", "✨ Extras", "filter:category:extras"),
        ]

        for category_key, category_label, callback_data in filter_categories:
            is_active = active_filters.get(category_key, False)
            priority = ButtonPriority.SUCCESS if is_active else ButtonPriority.SECONDARY
            emoji = "✅" if is_active else None

            builder.add_button(category_label, callback_data, priority, emoji)

        # Action buttons
        builder.add_separator()
        builder.add_button("✅ Apply Filters", "filter:apply", ButtonPriority.PRIMARY)
        builder.add_button("🧹 Clear All", "filter:clear", ButtonPriority.DANGER)

        # Navigation
        builder.add_navigation_row("⬅️ Back to Browse", "menu:browse")

        return builder.build()

    @staticmethod
    def create_product_selection_keyboard(
        products: List[Dict[str, Any]],
        current_product: str = None
    ) -> InlineKeyboardMarkup:
        """Create an enhanced product selection keyboard"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.LIST, 1)

        for product in products:
            product_type = product.get("type", "")
            name = product.get("name", "Unknown")
            icon = product.get("icon", "📦")
            active_apis = product.get("active_apis", 0)
            total_apis = product.get("total_apis", 0)

            # Determine button priority and styling
            is_current = current_product == product_type
            priority = ButtonPriority.SUCCESS if is_current else ButtonPriority.SECONDARY

            # Build button text with status
            button_text = f"{name} ({active_apis}/{total_apis} APIs)"
            if is_current:
                button_text = f"✅ {button_text}"

            builder.add_button(
                button_text,
                f"product:select:{product_type}",
                priority,
                icon
            )

        # Navigation
        builder.add_navigation_row()

        return builder.build()

    @staticmethod
    def create_cart_keyboard(
        has_items: bool = False,
        item_count: int = 0,
        total_value: float = 0.0
    ) -> InlineKeyboardMarkup:
        """Create an enhanced cart management keyboard"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.COMPACT, 2)

        if has_items:
            # Primary actions
            builder.add_button("💳 Checkout", "local:cart:checkout", ButtonPriority.PRIMARY)
            builder.add_button("✏️ Edit Items", "local:cart:edit", ButtonPriority.SECONDARY)

            # Secondary actions
            builder.add_button("🔄 Refresh", "local:cart:view", ButtonPriority.TERTIARY)
            builder.add_button("🗑️ Clear Cart", "local:cart:clear", ButtonPriority.DANGER)

            # Add summary info
            builder.add_separator(f"💰 Total: ${total_value:.2f} ({item_count} items)")
        else:
            builder.add_button("🛒 Browse Catalog", "menu:browse", ButtonPriority.PRIMARY)

        # Navigation
        builder.add_navigation_row()

        return builder.build()

    @staticmethod
    def create_feedback_keyboard(
        action_type: str = "general",
        context_data: Dict[str, Any] = None
    ) -> InlineKeyboardMarkup:
        """Create an enhanced feedback keyboard for user actions"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.COMPACT, 2)

        context_data = context_data or {}

        if action_type == "add_to_cart":
            # Enhanced feedback for cart actions
            builder.add_button("🛒 View Cart", "local:cart:view", ButtonPriority.PRIMARY)
            builder.add_button("➕ Add More", "catalog:continue_shopping", ButtonPriority.SECONDARY)
            builder.add_button("💳 Checkout", "local:cart:checkout", ButtonPriority.SUCCESS)

        elif action_type == "search_results":
            # Enhanced feedback for search results
            builder.add_button("🔍 Refine Search", "catalog:search", ButtonPriority.PRIMARY)
            builder.add_button("🧰 Add Filters", "catalog:filters", ButtonPriority.SECONDARY)
            builder.add_button("📄 Browse All", "catalog:browse_all", ButtonPriority.TERTIARY)

        elif action_type == "error":
            # Enhanced error recovery options
            builder.add_button("🔄 Try Again", "catalog:retry", ButtonPriority.PRIMARY)
            builder.add_button("🏠 Main Menu", "menu:main", ButtonPriority.SECONDARY)
            builder.add_button("❓ Get Help", "menu:help", ButtonPriority.TERTIARY)

        else:
            # Generic feedback options
            builder.add_button("✅ Continue", "catalog:continue", ButtonPriority.PRIMARY)
            builder.add_button("⬅️ Back", "menu:back", ButtonPriority.SECONDARY)

        return builder.build()

    @staticmethod
    def create_smart_filter_keyboard(
        active_filters: Dict[str, Any] = None,
        suggested_filters: List[Dict[str, str]] = None,
        quick_presets: List[Dict[str, str]] = None
    ) -> InlineKeyboardMarkup:
        """Create an intelligent filter keyboard with suggestions and presets"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.COMPACT, 2)

        active_filters = active_filters or {}
        suggested_filters = suggested_filters or []
        quick_presets = quick_presets or []

        # Quick filter presets
        if quick_presets:
            builder.add_separator("🚀 Quick Filters")
            for preset in quick_presets[:4]:  # Limit to 4 presets
                builder.add_button(
                    preset["name"],
                    preset["callback"],
                    ButtonPriority.PRIMARY,
                    preset.get("emoji", "⚡")
                )

        # Smart suggestions based on current filters
        if suggested_filters:
            builder.add_separator("💡 Suggested")
            for suggestion in suggested_filters[:3]:  # Limit to 3 suggestions
                builder.add_button(
                    suggestion["name"],
                    suggestion["callback"],
                    ButtonPriority.SUCCESS,
                    "🔍"
                )

        # Standard filter categories with enhanced styling
        builder.add_separator("🧰 All Filters")

        filter_categories = [
            ("location", "📍 Location", "filter:category:location"),
            ("card", "💳 Card Details", "filter:category:card"),
            ("pricing", "💰 Pricing", "filter:category:pricing"),
            ("verification", "🔐 Verification", "filter:category:verification"),
            ("quality", "💎 Quality", "filter:category:quality"),
            ("extras", "✨ Advanced", "filter:category:extras"),
        ]

        for category_key, category_label, callback_data in filter_categories:
            is_active = active_filters.get(category_key, False)
            priority = ButtonPriority.SUCCESS if is_active else ButtonPriority.SECONDARY
            emoji = "✅" if is_active else None

            builder.add_button(category_label, callback_data, priority, emoji)

        # Advanced actions
        builder.add_separator("⚙️ Actions")
        if active_filters:
            builder.add_button("🧹 Clear All", "filter:clear_all", ButtonPriority.DANGER)
            builder.add_button("💾 Save Preset", "filter:save_preset", ButtonPriority.TERTIARY)

        builder.add_button("🔄 Reset to Default", "filter:reset", ButtonPriority.TERTIARY)

        # Navigation
        builder.add_navigation_row("⬅️ Back to Browse", "menu:browse")

        return builder.build()

    @staticmethod
    def create_direct_filter_keyboard(
        active_filters: Dict[str, Any] = None
    ) -> InlineKeyboardMarkup:
        """Create a direct filter interface showing all filter options without categories"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.COMPACT, 3)

        active_filters = active_filters or {}

        # Helper function to format filter button text
        def format_filter_button(key: str, label: str, emoji: str) -> str:
            if key in active_filters and active_filters[key]:
                if isinstance(active_filters[key], bool):
                    return f"✅ {emoji} {label}"
                else:
                    # Show the value for non-boolean filters
                    value = str(active_filters[key])
                    if len(value) > 10:
                        value = value[:10] + "..."
                    return f"✅ {emoji} {label}: {value}"
            return f"{emoji} {label}"

        # Location Filters
        builder.add_separator("📍 Location")
        builder.add_button(
            format_filter_button("country", "Country", "🌍"),
            "filter:select:country",
            ButtonPriority.SUCCESS if "country" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("state", "State", "🏙"),
            "filter:select:state",
            ButtonPriority.SUCCESS if "state" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("city", "City", "🏘"),
            "filter:select:city",
            ButtonPriority.SUCCESS if "city" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("zip", "ZIP", "🏷"),
            "filter:select:zip",
            ButtonPriority.SUCCESS if "zip" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("zipCheck", "ZIP Check", "✔️"),
            "filter:toggle:zipCheck",
            ButtonPriority.SUCCESS if active_filters.get("zipCheck") else ButtonPriority.SECONDARY
        )

        # Card Details Filters
        builder.add_separator("💳 Card Details")
        builder.add_button(
            format_filter_button("bin", "BIN", "🔢"),
            "filter:input:bin",
            ButtonPriority.SUCCESS if "bin" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("brand", "Brand", "🏷"),
            "filter:select:brand",
            ButtonPriority.SUCCESS if "brand" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("type", "Card Type", "💳"),
            "filter:select:type",
            ButtonPriority.SUCCESS if "type" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("level", "Level", "🎚"),
            "filter:select:level",
            ButtonPriority.SUCCESS if "level" in active_filters else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("bank", "Bank", "🏦"),
            "filter:select:bank",
            ButtonPriority.SUCCESS if "bank" in active_filters else ButtonPriority.SECONDARY
        )

        # Pricing Filters
        builder.add_separator("💰 Pricing")
        # Show price range if both are set, otherwise individual buttons
        if "priceFrom" in active_filters and "priceTo" in active_filters:
            price_text = f"💰 ${active_filters['priceFrom']}-${active_filters['priceTo']}"
            builder.add_button(price_text, "filter:select:price", ButtonPriority.SUCCESS)
        else:
            builder.add_button(
                format_filter_button("priceFrom", "Min Price", "⬆️"),
                "filter:input:priceFrom",
                ButtonPriority.SUCCESS if "priceFrom" in active_filters else ButtonPriority.SECONDARY
            )
            builder.add_button(
                format_filter_button("priceTo", "Max Price", "⬇️"),
                "filter:input:priceTo",
                ButtonPriority.SUCCESS if "priceTo" in active_filters else ButtonPriority.SECONDARY
            )

        # Contact Data Filters
        builder.add_separator("🧾 Contact Data")
        builder.add_button(
            format_filter_button("address", "Address", "🏠"),
            "filter:toggle:address",
            ButtonPriority.SUCCESS if active_filters.get("address") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("phone", "Phone", "📞"),
            "filter:toggle:phone",
            ButtonPriority.SUCCESS if active_filters.get("phone") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("email", "Email", "📧"),
            "filter:toggle:email",
            ButtonPriority.SUCCESS if active_filters.get("email") else ButtonPriority.SECONDARY
        )

        # Identity & Extras Filters
        builder.add_separator("🛡 Identity & Extras")
        builder.add_button(
            format_filter_button("dob", "DOB", "📅"),
            "filter:toggle:dob",
            ButtonPriority.SUCCESS if active_filters.get("dob") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("ssn", "SSN", "🔢"),
            "filter:toggle:ssn",
            ButtonPriority.SUCCESS if active_filters.get("ssn") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("mmn", "MMN", "👤"),
            "filter:toggle:mmn",
            ButtonPriority.SUCCESS if active_filters.get("mmn") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("dl", "Driving License", "🚗"),
            "filter:toggle:dl",
            ButtonPriority.SUCCESS if active_filters.get("dl") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("ip", "IP", "🌐"),
            "filter:toggle:ip",
            ButtonPriority.SUCCESS if active_filters.get("ip") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("ua", "User Agent", "🖥"),
            "filter:toggle:ua",
            ButtonPriority.SUCCESS if active_filters.get("ua") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("refundable", "Refundable", "♻️"),
            "filter:toggle:refundable",
            ButtonPriority.SUCCESS if active_filters.get("refundable") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("withoutcvv", "Without CVV", "🚫"),
            "filter:toggle:withoutcvv",
            ButtonPriority.SUCCESS if active_filters.get("withoutcvv") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("expirethismonth", "Expire This Month", "📆"),
            "filter:toggle:expirethismonth",
            ButtonPriority.SUCCESS if active_filters.get("expirethismonth") else ButtonPriority.SECONDARY
        )
        builder.add_button(
            format_filter_button("discount", "Discount", "💸"),
            "filter:toggle:discount",
            ButtonPriority.SUCCESS if active_filters.get("discount") else ButtonPriority.SECONDARY
        )

        # Action buttons
        builder.add_separator("⚙️ Actions")
        builder.add_button("🔍 Search with Filters", "catalog:search_with_filters", ButtonPriority.PRIMARY)
        if active_filters:
            builder.add_button("🧹 Clear All", "filter:clear", ButtonPriority.DANGER)
        builder.add_button("✅ Apply & Browse", "filter:apply", ButtonPriority.SUCCESS)

        # Navigation
        builder.add_navigation_row("⬅️ Back to Browse", "menu:browse")

        return builder.build()

    @staticmethod
    def create_quick_actions_keyboard(
        context: str = "catalog",
        user_preferences: Dict[str, Any] = None
    ) -> InlineKeyboardMarkup:
        """Create a keyboard with quick actions and shortcuts for power users"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.COMPACT, 2)

        user_preferences = user_preferences or {}

        if context == "catalog":
            # Catalog-specific quick actions
            builder.add_button("⚡ Quick Search", "catalog:quick_search", ButtonPriority.PRIMARY, "🔍")
            builder.add_button("🔥 Popular Cards", "catalog:popular", ButtonPriority.PRIMARY, "🔥")

            # Smart shortcuts based on user behavior
            if user_preferences.get("frequent_country"):
                country = user_preferences["frequent_country"]
                builder.add_button(f"🌍 {country} Cards", f"filter:quick:country:{country}", ButtonPriority.SUCCESS)

            if user_preferences.get("preferred_price_range"):
                price_range = user_preferences["preferred_price_range"]
                builder.add_button(f"💰 {price_range}", f"filter:quick:price:{price_range}", ButtonPriority.SUCCESS)

            # Advanced actions
            builder.add_separator("🚀 Power User")
            builder.add_button("📊 Analytics", "catalog:analytics", ButtonPriority.TERTIARY, "📊")
            builder.add_button("🎯 Smart Recommendations", "catalog:recommendations", ButtonPriority.TERTIARY, "🎯")
            builder.add_button("📈 Price Trends", "catalog:trends", ButtonPriority.TERTIARY, "📈")

        elif context == "search":
            # Search-specific quick actions
            builder.add_button("🔍 Advanced Search", "search:advanced", ButtonPriority.PRIMARY)
            builder.add_button("📝 Search History", "search:history", ButtonPriority.SECONDARY)
            builder.add_button("💾 Save Search", "search:save", ButtonPriority.TERTIARY)

        # Universal quick actions
        builder.add_separator("⚡ Quick Access")
        builder.add_button("🛒 Cart", "local:cart:view", ButtonPriority.SECONDARY, "🛒")
        builder.add_button("📦 Orders", "menu:orders", ButtonPriority.SECONDARY, "📦")
        builder.add_button("⚙️ Settings", "menu:settings", ButtonPriority.TERTIARY, "⚙️")

        # Navigation
        builder.add_navigation_row()

        return builder.build()

    @staticmethod
    def create_enhanced_search_keyboard(
        search_history: List[str] = None,
        popular_searches: List[str] = None
    ) -> InlineKeyboardMarkup:
        """Create an enhanced search keyboard with history and suggestions"""
        builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.LIST, 1)

        search_history = search_history or []
        popular_searches = popular_searches or []

        # Search input
        builder.add_button("✍️ Enter Search Term", "search:input", ButtonPriority.PRIMARY, "🔍")

        # Recent searches
        if search_history:
            builder.add_separator("🕒 Recent Searches")
            for search_term in search_history[:3]:  # Show last 3 searches
                builder.add_button(
                    f"🔍 {search_term}",
                    f"search:repeat:{search_term}",
                    ButtonPriority.SECONDARY
                )

        # Popular searches
        if popular_searches:
            builder.add_separator("🔥 Popular Searches")
            for search_term in popular_searches[:3]:  # Show top 3 popular
                builder.add_button(
                    f"🔥 {search_term}",
                    f"search:popular:{search_term}",
                    ButtonPriority.SUCCESS
                )

        # Search options
        builder.add_separator("⚙️ Search Options")
        builder.add_button("🧰 Advanced Filters", "catalog:filters", ButtonPriority.TERTIARY)
        builder.add_button("🎯 Smart Search", "search:smart", ButtonPriority.TERTIARY)

        # Navigation
        builder.add_navigation_row("⬅️ Back to Browse", "menu:browse")

        return builder.build()


# Convenience functions for easy import
def create_enhanced_keyboard() -> EnhancedKeyboardBuilder:
    """Create a new enhanced keyboard builder"""
    return EnhancedKeyboardBuilder()


def quick_keyboard(buttons: List[Tuple[str, str]], back_callback: str = "menu:main") -> InlineKeyboardMarkup:
    """Quickly create a simple keyboard with buttons and back navigation"""
    builder = EnhancedKeyboardBuilder()

    for text, callback_data in buttons:
        builder.add_button(text, callback_data)

    builder.add_navigation_row(back_callback=back_callback)
    return builder.build()
