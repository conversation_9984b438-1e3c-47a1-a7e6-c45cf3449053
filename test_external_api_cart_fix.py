#!/usr/bin/env python3
"""
Test script to verify that the external API service cart operations
work correctly with the checkout queue service format.
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv
load_dotenv()

from services.external_api_service import get_external_api_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_external_api_cart_operations():
    """Test external API cart operations."""
    logger.info("🧪 Testing External API Cart Operations")
    logger.info("=" * 50)
    
    try:
        # Get external API service
        external_api_service = get_external_api_service()
        
        logger.info(f"External API version: {external_api_service.api_version}")
        
        # Step 1: View cart to see current state
        logger.info("\n📋 Step 1: View cart (current state)...")
        view_result = await external_api_service.view_cart()
        
        logger.info(f"View cart success: {view_result.success}")
        logger.info(f"View cart data type: {type(view_result.data)}")
        
        if view_result.success:
            data = view_result.data
            logger.info(f"Cart data structure: {json.dumps(data, indent=2) if isinstance(data, dict) else str(data)}")
            
            # Test the checkout queue service parsing logic
            if isinstance(data, dict):
                if isinstance(data.get("data"), list):
                    cart_items = data["data"]
                    logger.info(f"✅ Found cart items using data.data: {len(cart_items)} items")
                elif isinstance(data.get("cart"), dict) and isinstance(data["cart"].get("items"), list):
                    cart_items = data["cart"]["items"]
                    logger.info(f"✅ Found cart items using data.cart.items: {len(cart_items)} items")
                else:
                    logger.warning("⚠️  Cart data structure doesn't match expected format")
                    cart_items = []
            elif isinstance(data, list):
                cart_items = data
                logger.info(f"✅ Found cart items as direct list: {len(cart_items)} items")
            else:
                logger.error(f"❌ Unexpected data type: {type(data)}")
                cart_items = []
            
            # Show some cart items
            if cart_items:
                logger.info(f"\n📦 Cart contains {len(cart_items)} items:")
                for i, item in enumerate(cart_items[:3]):  # Show first 3 items
                    item_id = (
                        item.get("product_id") or 
                        item.get("card_id") or 
                        item.get("id") or 
                        item.get("_id") or 
                        "unknown"
                    )
                    logger.info(f"  Item {i+1}: {item_id}")
                
                if len(cart_items) > 3:
                    logger.info(f"  ... and {len(cart_items) - 3} more items")
            else:
                logger.info("📦 Cart is empty")
        else:
            logger.error(f"❌ Failed to view cart: {view_result.error}")
            return False
        
        # Step 2: Test add to cart (if we have items to add)
        if cart_items:
            # Use the first item's ID for testing
            test_item = cart_items[0]
            test_card_id = (
                test_item.get("product_id") or 
                test_item.get("card_id") or 
                test_item.get("id") or 
                test_item.get("_id")
            )
            
            if test_card_id:
                logger.info(f"\n➕ Step 2: Test add to cart with existing item {test_card_id}...")
                add_result = await external_api_service.add_to_cart(test_card_id, "Cards")
                
                logger.info(f"Add to cart success: {add_result.success}")
                if not add_result.success:
                    logger.warning(f"Add to cart failed: {add_result.error}")
                else:
                    logger.info("✅ Add to cart succeeded")
                
                # Step 3: View cart again to verify
                logger.info(f"\n📋 Step 3: View cart after adding...")
                view_result_after = await external_api_service.view_cart()
                
                if view_result_after.success:
                    data_after = view_result_after.data
                    if isinstance(data_after, dict) and isinstance(data_after.get("data"), list):
                        cart_items_after = data_after["data"]
                        logger.info(f"✅ Cart now contains {len(cart_items_after)} items")
                        
                        # Check if our item is still there
                        found_item = False
                        for item in cart_items_after:
                            item_id = (
                                item.get("product_id") or 
                                item.get("card_id") or 
                                item.get("id") or 
                                item.get("_id")
                            )
                            if str(item_id) == str(test_card_id):
                                found_item = True
                                break
                        
                        if found_item:
                            logger.info(f"✅ Test item {test_card_id} found in cart")
                        else:
                            logger.warning(f"⚠️  Test item {test_card_id} not found in cart")
                    else:
                        logger.error("❌ Unexpected cart data format after adding")
                else:
                    logger.error(f"❌ Failed to view cart after adding: {view_result_after.error}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def main():
    """Run the external API cart test."""
    logger.info("🚀 Starting External API Cart Fix Test")
    logger.info("=" * 60)
    
    success = await test_external_api_cart_operations()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS")
    logger.info("=" * 60)
    
    if success:
        logger.info("✅ External API cart test completed successfully")
        logger.info("   The cart operations should now work with checkout queue service")
    else:
        logger.error("❌ External API cart test failed")
        logger.error("   Check the error messages above")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
