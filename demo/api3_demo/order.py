"""
Order flow: Ultra-optimized version for maximum performance.
Reuse authenticated session from login.py to POST to /order endpoint.
"""

import json
import re
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

from login import logger, REFERER
from session_manager import get_authenticated_session, save_session_cookies

# Import standard response converter for consistency
try:
    from add_to_cart import _response_to_jsonable
except ImportError:

    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


def _order_url() -> str:
    """Build the absolute URL for the order endpoint."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "order")


def _cart_url() -> str:
    """Build the absolute URL for the cart page."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "cart")


def _extract_token(html: str) -> str | None:
    """Fast regex-based token extraction."""
    if not html or "_token" not in html:
        return None

    match = re.search(r'name=["\']_token["\'][^>]*value=["\']([^"\']{10,})["\']', html)
    if not match:
        match = re.search(
            r'value=["\']([^"\']{10,})["\'][^>]*name=["\']_token["\']', html
        )

    return match.group(1).strip() if match else None


def parse_response_with_soup(html: str) -> dict:
    """Parse HTML response using BeautifulSoup - optimized for sections only."""
    if not html:
        return {"sections": []}

    soup = BeautifulSoup(html, "html.parser")

    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()

    result = {"sections": []}

    # Extract headings and organize into sections (main focus)
    headings = soup.find_all(["h1", "h2", "h3", "h4"], limit=10)
    for heading in headings:
        section = {"text": heading.get_text(strip=True), "content": []}

        # Get content after heading until next heading
        content_elements = []
        for sibling in heading.find_next_siblings():
            if sibling.name in ["h1", "h2", "h3", "h4"]:
                break
            if sibling.name in ["p", "div", "ul", "ol"]:
                text = sibling.get_text(strip=True)
                if text:
                    content_elements.append(text)

        section["content"] = content_elements
        result["sections"].append(section)

    return result


def display_parsed_response(parsed_data: dict) -> None:
    """Display sections data from parsed response."""
    print("\n" + "=" * 60)
    print("📋 SECTIONS DATA")
    print("=" * 60)

    # Extract sections from the standardized response structure
    sections = parsed_data.get("sections", [])

    if sections:
        print(f"Found: {len(sections)} section(s)")
        print("-" * 40)

        for i, section in enumerate(sections, 1):
            print(f"{i}. {section.get('text', '')}")

            # Show content under each section
            content = section.get("content", [])
            if content:
                for j, content_item in enumerate(content, 1):
                    # Show full content without truncation
                    print(f"   {j}. {content_item}")
            else:
                print("   (No content)")

            if i < len(sections):  # Add separator between sections
                print()
    else:
        print("No sections found in the response.")

    print("=" * 60)


def create_order(session: requests.Session, no_refund: bool = False) -> dict:
    """Create order and return structured response with sections data."""
    order_type = "no-refund order" if no_refund else "order"
    logger.info(f"Creating {order_type}...")

    # Get CSRF token from cart
    cart_resp = session.get(_cart_url(), timeout=15)
    if cart_resp.status_code != 200:
        return {"error": f"Cart fetch failed: {cart_resp.status_code}", "sections": []}

    token = _extract_token(cart_resp.text)
    if not token:
        logger.warning("No CSRF token found")

    # Prepare form data
    form_data = {"_token": token or ""}
    if no_refund:
        form_data["no_refund"] = "on"

    # Post order
    order_resp = session.post(
        _order_url(),
        data=form_data,
        headers={
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": cart_resp.url or _cart_url(),
        },
        allow_redirects=True,
        timeout=15,
    )

    logger.info(f"Order status: {order_resp.status_code}")

    # Check if we got redirected to an order page (success indicator)
    if "/orders/" in order_resp.url:
        order_id = order_resp.url.split("/orders/")[-1]
        logger.info(f"Order created successfully with ID: {order_id}")

    # Return structured response data
    if order_resp.status_code == 200:
        # Parse sections data and combine with standard response structure
        sections_data = parse_response_with_soup(order_resp.text)
        full_response = _response_to_jsonable(order_resp)

        # Add no_refund flag to response for clarity
        if no_refund:
            full_response["no_refund_enabled"] = True

        return {
            "order_response": full_response,
            "sections": sections_data.get("sections", []),
            "no_refund": no_refund,
        }
    else:
        return {
            "error": f"Order request failed: {order_resp.status_code}",
            "sections": [],
            "no_refund": no_refund,
        }


def save_result(result: dict, filepath: str = "order_response.json") -> str:
    """Save order response data to JSON file with pretty formatting."""
    # Print JSON response to console
    print("\n" + "=" * 60)
    print("ORDER RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")
    
    # Save to file
    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    return filepath


def main() -> None:
    """Fast order creation - show only sections data."""
    logger.info("=== Fast Order Creation ===")

    # Get authenticated session
    session = get_authenticated_session(logger)

    # Create order - returns only sections data (default: refund allowed)
    result = create_order(session, no_refund=False)

    # Display sections data or error
    if result.get("error"):
        print(f"❌ Error: {result['error']}")
    elif result.get("sections"):
        display_parsed_response(result)
        # Save only sections data
        out_file = save_result(result)
        logger.info(f"Saved sections to: {out_file}")
    else:
        print("No sections data found in response.")

    # Save session cookies for reuse
    save_session_cookies(session, logger=logger)
    logger.info("=== Complete ===")


if __name__ == "__main__":
    main()
