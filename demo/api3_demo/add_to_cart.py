"""
Add-to-cart flow: reuse authenticated session from login.py to POST to
the /cart endpoint and save the response in a JSON file for testing.
"""

import os
import json
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup, SoupStrainer

from login import (
    logger,
    REFERER,
    log_request,
    log_response,
    refresh_xsrf_headers_from_cookies,
    fetch_login_and_extract,
    perform_login,
    prune_cookie_duplicates,
)
from session_manager import (
    get_authenticated_session,
    save_session_cookies,
    is_unauthenticated,
)


def _cart_url() -> str:
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "cart")


def _shop_url() -> str:
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "shop")


def _shop_filter_params() -> dict:
    # Align with filter.py defaults (bins may be overridden by env if desired)
    return {
        "base_id[]": "",
        "continent[]": "",
        "country[]": "",
        "scheme[]": "",
        "type[]": "",
        "level[]": "",
        "ethnicity": "",
        "postal_code": "",
        "searched_bank": "",
        "selected_bank": "",
        "region": "",
        "city": "",
        "bins": os.getenv("FILTER_BINS", "405621"),
        "with_billing": "",
        "with_phone": "",
        "with_dob": "",
    }


def _refresh_xsrf_headers_from_cookies(session: requests.Session) -> None:
    # Delegate to the shared helper in login.py
    refresh_xsrf_headers_from_cookies(session)


def _extract_post_form_token(html: str) -> str | None:
    """Extract hidden _token value from the first POST form on the page."""
    try:
        from bs4 import BeautifulSoup
        from urllib.parse import urljoin

        # Extract forms manually since _extract_forms was removed
        soup = BeautifulSoup(html, "html.parser")
        forms = []

        for form in soup.find_all("form"):
            form_data = {
                "method": form.get("method", "GET").upper(),
                "action": urljoin(REFERER, form.get("action", "")),
                "inputs": [],
            }

            # Extract input fields
            for inp in form.find_all("input"):
                input_data = {
                    "name": inp.get("name", ""),
                    "type": inp.get("type", "text"),
                    "value": inp.get("value", ""),
                }
                form_data["inputs"].append(input_data)

            # Create payload for POST forms
            if form_data["method"] == "POST":
                payload = {}
                for inp in form_data["inputs"]:
                    if inp["name"] and inp["type"] not in ("submit", "button"):
                        payload[inp["name"]] = inp["value"]
                form_data["payload"] = payload

            forms.append(form_data)
        for f in forms:
            if (f.get("method") or "").upper() != "POST":
                continue
            # Prefer payload snapshot
            payload = f.get("payload") or {}
            tok = payload.get("_token")
            if tok:
                return tok
            # Fallback: scan inputs
            for i in f.get("inputs", []):
                if (i.get("name") or "").lower() == "_token":
                    v = i.get("value")
                    if v:
                        return v
    except Exception:
        pass
    return None


def fetch_shop_for_token(session: requests.Session) -> tuple[str, requests.Response]:
    url = _shop_url()
    params = _shop_filter_params()
    logger.info("Fetching shop page for CSRF token: %s", url)
    r = session.get(url, params=params, allow_redirects=True, timeout=60)
    log_request(r)
    log_response(r)
    # Build the exact Referer (with query) for subsequent POST
    try:
        from urllib.parse import urlencode

        referer_full = url + ("?" + urlencode(params, doseq=True) if params else "")
    except Exception:
        referer_full = r.url or url
    return referer_full, r


def _checked_ids_from_env() -> list[str]:
    # Read comma-separated IDs from env, e.g., ADD_CHECKED_IDS=id1,id2
    raw = os.getenv("ADD_CHECKED_IDS", "").strip()
    if not raw:
        return []
    return [x.strip() for x in raw.split(",") if x.strip()]


def _build_cart_payload(csrf_token: str) -> dict:
    # Prefer IDs from env; fallback to a single example from the original cURL
    ids = _checked_ids_from_env()
    if not ids:
        ids = ["0589d0530a1650ae4aead2fff53b7dee87b136d0"]
    payload = {
        "_token": csrf_token or "",
        "target": "Add selected to cart",
    }
    # Multiple checked[] keys are supported by requests using tuples or lists
    payload.update({"checked[]": ids})
    return payload


def post_add_to_cart(
    session: requests.Session, csrf_token: str, referer: str
) -> requests.Response:
    url = _cart_url()
    data = _build_cart_payload(csrf_token)
    logger.info("POSTing to cart: %s", url)
    r = session.post(
        url,
        data=data,
        headers={
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": referer,
            "Origin": REFERER.rstrip("/"),
        },
        allow_redirects=False,
        timeout=60,
    )
    log_request(r)
    log_response(r)
    return r


def follow_redirect_if_any(
    session: requests.Session, r: requests.Response
) -> requests.Response:
    try:
        status = getattr(r, "status_code", 0) or 0
        loc = r.headers.get("Location")
        if loc and 300 <= status < 400:
            base = getattr(r, "url", None) or _cart_url()
            from urllib.parse import urljoin as _uj

            target = _uj(base, loc)
            logger.info("Following redirect to: %s", target)
            g = session.get(target, allow_redirects=True, timeout=60)
            log_request(g)
            log_response(g)
            return g
    except Exception:
        pass
    return r


def _response_to_jsonable(r: requests.Response) -> dict:
    def _cell_flat(cell):
        text = cell.get_text(separator=" ", strip=True)
        inputs = list(cell.find_all("input"))
        obj = {"text": text}
        if len(inputs) == 1:
            itype = (inputs[0].get("type") or "text").lower()
            val = inputs[0].get("value")
            if val is None and itype in ("checkbox", "radio"):
                val = "on"
            obj["input_type"] = itype
            obj["input_value"] = "" if val is None else val
        elif len(inputs) > 1:
            for idx, inp in enumerate(inputs, start=1):
                itype = (inp.get("type") or "text").lower()
                val = inp.get("value")
                if val is None and itype in ("checkbox", "radio"):
                    val = "on"
                obj[f"input_type_{idx}"] = itype
                obj[f"input_value_{idx}"] = "" if val is None else val
        return obj

    def _tables_plain(root) -> list:
        results = []
        for tbl in root.find_all("table"):
            header = []
            rows = []
            thead = tbl.find("thead")
            if thead:
                head_rows = thead.find_all("tr")
                if head_rows:
                    header = [
                        _cell_flat(c) for c in head_rows[0].find_all(["th", "td"])
                    ]
            if not header:
                first_tr = tbl.find("tr")
                if first_tr:
                    ths = first_tr.find_all("th")
                    if ths:
                        header = [_cell_flat(th) for th in ths]
            bodies = tbl.find_all("tbody")
            if bodies:
                for body in bodies:
                    for tr in body.find_all("tr"):
                        cells = tr.find_all(["th", "td"])  # include row header cells
                        if cells:
                            rows.append([_cell_flat(c) for c in cells])
            else:
                for tr in tbl.find_all("tr"):
                    cells = tr.find_all(["th", "td"])  # include row header cells
                    if cells:
                        rows.append([_cell_flat(c) for c in cells])
            results.append({"header": header, "rows": rows})
        return results

    def _flash_messages(soup: BeautifulSoup) -> list:
        msgs = []
        for el in soup.find_all(attrs={"role": "alert"}):
            t = el.get_text(separator=" ", strip=True)
            if t:
                msgs.append(t)
        for cls in [
            "alert",
            "alert-success",
            "alert-danger",
            "alert-warning",
            "alert-info",
            "error",
            "success",
            "notice",
        ]:
            for el in soup.select(f".{cls}"):
                t = el.get_text(separator=" ", strip=True)
                if t and t not in msgs:
                    msgs.append(t)
        return msgs

    def _headings(soup: BeautifulSoup) -> list:
        hs = []
        for tag in ["h1", "h2", "h3"]:
            for el in soup.find_all(tag):
                t = el.get_text(separator=" ", strip=True)
                if t:
                    hs.append(t)
        return hs

    def _sections(soup: BeautifulSoup) -> list:
        sections = []
        for sec in soup.find_all("section"):
            # Heading inside section
            heading = ""
            for tag in ["h1", "h2", "h3"]:
                h = sec.find(tag)
                if h:
                    heading = h.get_text(separator=" ", strip=True)
                    break
            # Messages within section
            msgs = []
            for el in sec.find_all(attrs={"role": "alert"}):
                t = el.get_text(separator=" ", strip=True)
                if t:
                    msgs.append(t)
            # Tables limited to this section
            tables = _tables_plain(sec)
            # Concise text (first few paragraphs)
            paras = [p.get_text(separator=" ", strip=True) for p in sec.find_all("p")]
            sections.append(
                {
                    "heading": heading,
                    "messages": msgs,
                    "tables": tables,
                    "paragraphs": [p for p in paras if p][:5],
                }
            )
        return sections

    ct = (r.headers.get("Content-Type") if r.headers else "") or ""
    if "application/json" in ct.lower():
        try:
            return {"sections": []}  # No sections in JSON response
        except Exception:
            return {"sections": []}
    else:
        html = r.text or ""
        strainer = SoupStrainer(
            [
                "section",
                "table",
                "thead",
                "tbody",
                "tr",
                "th",
                "td",
                "div",
                "span",
                "p",
                "h1",
                "h2",
                "h3",
                "ul",
                "ol",
                "li",
                "input",
            ]
        )
        soup = BeautifulSoup(html, "html.parser", parse_only=strainer)
        return {"sections": _sections(soup)}


def save_add_to_cart_result(
    final_resp: requests.Response,
    out_path: str = "add_to_cart_response.json",
) -> str:
    data = {"cart": _response_to_jsonable(final_resp)}
    
    # Print JSON response to console
    print("\n" + "=" * 60)
    print("ADD TO CART RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")
    
    # Save to file
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return out_path


def main() -> None:
    session = get_authenticated_session(logger)

    # The session is now guaranteed to be authenticated.
    # We still need to fetch the shop page to get the CSRF token for the form.
    referer_full, shop_resp = fetch_shop_for_token(session)

    # The token extraction might fail if the page structure is unexpected, but it's not an auth issue.
    token = _extract_post_form_token(shop_resp.text or "")

    post_resp = post_add_to_cart(session, token, referer_full)
    final_resp = follow_redirect_if_any(session, post_resp)
    out_file = save_add_to_cart_result(final_resp)
    logger.info("Saved add-to-cart response JSON to: %s", out_file)

    # Continue same session: load cart view and save separately
    try:
        from view_cart import get_cart, save_view_cart_result

        cart_resp = get_cart(session)
        cart_out = save_view_cart_result(cart_resp)
        logger.info("Saved view-cart JSON to: %s", cart_out)
    except Exception as e:
        logger.warning("View-cart step failed: %s", e)
    finally:
        # Always save the session at the very end to capture all cookie updates.
        save_session_cookies(session, logger=logger)


if __name__ == "__main__":
    main()
