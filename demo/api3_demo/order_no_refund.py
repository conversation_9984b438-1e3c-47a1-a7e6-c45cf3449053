"""
Order flow with no-refund option: Ultra-optimized version for maximum performance.
Reuse authenticated session from login.py to POST to /order endpoint with no_refund=on.
Based on analysis of no_refund.py curl commands.
"""

import json
import re
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

from login import logger, REFER<PERSON>
from session_manager import get_authenticated_session, save_session_cookies

# Import standard response converter for consistency
try:
    from add_to_cart import _response_to_jsonable
except ImportError:

    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


def _order_url() -> str:
    """Build the absolute URL for the order endpoint."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "order")


def _cart_url() -> str:
    """Build the absolute URL for the cart page."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "cart")


def _extract_token(html: str) -> str | None:
    """Fast regex-based token extraction."""
    if not html or "_token" not in html:
        return None

    match = re.search(r'name=["\']_token["\'][^>]*value=["\']([^"\']{10,})["\']', html)
    if not match:
        match = re.search(
            r'value=["\']([^"\']{10,})["\'][^>]*name=["\']_token["\']', html
        )

    return match.group(1).strip() if match else None


def parse_response_with_soup(html: str) -> dict:
    """Parse HTML response using BeautifulSoup - optimized for sections only."""
    if not html:
        return {"sections": []}

    soup = BeautifulSoup(html, "html.parser")

    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()

    result = {"sections": []}

    # Extract headings and organize into sections (main focus)
    headings = soup.find_all(["h1", "h2", "h3", "h4"], limit=10)
    for heading in headings:
        section = {"text": heading.get_text(strip=True), "content": []}

        # Get content after heading until next heading
        content_elements = []
        for sibling in heading.find_next_siblings():
            if sibling.name in ["h1", "h2", "h3", "h4"]:
                break
            if sibling.name in ["p", "div", "ul", "ol"]:
                text = sibling.get_text(strip=True)
                if text:
                    content_elements.append(text)

        section["content"] = content_elements
        result["sections"].append(section)

    return result


def display_parsed_response(parsed_data: dict) -> None:
    """Display sections data from parsed response."""
    print("\n" + "=" * 60)
    print("📋 SECTIONS DATA (NO REFUND ORDER)")
    print("=" * 60)

    # Extract sections from the standardized response structure
    sections = parsed_data.get("sections", [])

    if sections:
        print(f"Found: {len(sections)} section(s)")
        print("-" * 40)

        for i, section in enumerate(sections, 1):
            print(f"{i}. {section.get('text', '')}")

            # Show content under each section
            content = section.get("content", [])
            if content:
                for j, content_item in enumerate(content, 1):
                    # Show full content without truncation
                    print(f"   {j}. {content_item}")
            else:
                print("   (No content)")

            if i < len(sections):  # Add separator between sections
                print()
    else:
        print("No sections found in the response.")

    print("=" * 60)


def create_order_no_refund(session: requests.Session) -> dict:
    """
    Create order with no-refund option and return structured response with sections data.
    Based on the analysis of no_refund.py curl commands.
    """
    logger.info("Creating order with no-refund option...")

    # Get CSRF token from cart
    cart_resp = session.get(_cart_url(), timeout=15)
    if cart_resp.status_code != 200:
        return {"error": f"Cart fetch failed: {cart_resp.status_code}", "sections": []}

    token = _extract_token(cart_resp.text)
    if not token:
        logger.warning("No CSRF token found")

    # Prepare form data with no_refund option (based on no_refund.py analysis)
    form_data = {
        "no_refund": "on",  # Key addition from no_refund.py
        "_token": token or "",
    }

    # Post order with no-refund option
    # Using the same headers as seen in no_refund.py
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Referer": cart_resp.url or _cart_url(),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Cache-Control": "max-age=0",
        "Origin": REFERER,
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-User": "?1",
        "Sec-GPC": "1",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36",
        "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
        "sec-ch-ua-mobile": "?1",
        "sec-ch-ua-platform": '"Android"',
    }

    order_resp = session.post(
        _order_url(),
        data=form_data,
        headers=headers,
        allow_redirects=True,  # Will follow the 302 redirect to /orders/{order_id}
        timeout=15,
    )

    logger.info(f"No-refund order status: {order_resp.status_code}")
    logger.info(f"Final URL after redirects: {order_resp.url}")

    # Check if we got redirected to an order page (success indicator)
    if "/orders/" in order_resp.url:
        order_id = order_resp.url.split("/orders/")[-1]
        logger.info(f"Order created successfully with ID: {order_id}")

    # Return structured response data
    if order_resp.status_code == 200:
        # Parse sections data and combine with standard response structure
        sections_data = parse_response_with_soup(order_resp.text)
        full_response = _response_to_jsonable(order_resp)

        # Add no_refund flag to response for clarity
        full_response["no_refund_enabled"] = True

        return {
            "order_response": full_response,
            "sections": sections_data.get("sections", []),
            "no_refund": True,
            "form_data_sent": form_data,
        }
    else:
        return {
            "error": f"No-refund order request failed: {order_resp.status_code}",
            "sections": [],
            "no_refund": True,
            "form_data_sent": form_data,
        }


def save_result(result: dict, filepath: str = "order_no_refund_response.json") -> str:
    """Save order response data to JSON file with pretty formatting."""
    # Print JSON response to console
    print("\n" + "=" * 60)
    print("ORDER NO REFUND RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")
    
    # Save to file
    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    return filepath


def main() -> None:
    """Fast order creation with no-refund option - show only sections data."""
    logger.info("=== Fast No-Refund Order Creation ===")

    # Get authenticated session
    session = get_authenticated_session(logger)

    # Create order with no-refund option - returns only sections data
    result = create_order_no_refund(session)

    # Display sections data or error
    if result.get("error"):
        print(f"❌ Error: {result['error']}")
        print(f"Form data sent: {result.get('form_data_sent', {})}")
    elif result.get("sections"):
        print(f"✅ No-refund order created successfully!")
        display_parsed_response(result)
        # Save only sections data
        out_file = save_result(result)
        logger.info(f"Saved no-refund order response to: {out_file}")
    else:
        print("No sections data found in response.")
        if result.get("no_refund"):
            print("✅ No-refund option was enabled for this order.")

    # Save session cookies for reuse
    save_session_cookies(session, logger=logger)
    logger.info("=== No-Refund Order Complete ===")


if __name__ == "__main__":
    main()
