"""
Session Manager: Handles loading and saving of session cookies to a JSON file.
This allows scripts to reuse an authenticated session, avoiding repeated logins.
"""

import os
import json
import time
import requests
from logging import Logger

# Assuming login.py contains these helpers and is in the same directory.
from login import (
    logger as default_logger,
    make_session,
    _cookies_to_serializable,
    _load_cookies_into_jar,
    refresh_xsrf_headers_from_cookies,
    prune_cookie_duplicates,
    REFERER,
    fetch_login_and_extract,
    perform_login,
)

from urllib.parse import urljoin

SESSION_COOKIES_PATH = os.getenv("SESSION_COOKIES_PATH", "session_cookies.json").strip()
FAST_VALIDATION_CACHE_TIME = 300  # 5 minutes cache for validation

# Session validation cache
_last_validation_time = 0
_last_validation_result = False


def save_session_cookies(
    session: requests.Session,
    path: str | None = None,
    logger: Logger = default_logger,
) -> str | None:
    """Persist current session cookies to JSON for reuse across scripts."""
    global _last_validation_time
    try:
        out_path = path or SESSION_COOKIES_PATH
        data = {
            "cookies": _cookies_to_serializable(session.cookies),
            "saved_at": time.time(),
            "validated_at": _last_validation_time,
        }
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info("Saved session cookies to: %s", out_path)
        return out_path
    except Exception as e:
        logger.warning("Failed to save session cookies: %s", e)
        return None


def load_session_cookies(
    session: requests.Session,
    path: str | None = None,
    logger: Logger = default_logger,
) -> int:
    """Load cookies from JSON into the session. Returns number of cookies loaded."""
    global _last_validation_time
    p = path or SESSION_COOKIES_PATH
    try:
        if not os.path.exists(p):
            return 0

        with open(p, "r", encoding="utf-8") as f:
            data = json.load(f) or {}

        items = data.get("cookies") or []

        # Check if session data is recent (less than 1 hour old)
        saved_at = data.get("saved_at", 0)
        if time.time() - saved_at < 3600:  # 1 hour
            _last_validation_time = data.get("validated_at", 0)
            logger.info(
                f"Loading recent session (saved {int(time.time() - saved_at)}s ago)"
            )

        loaded = _load_cookies_into_jar(session.cookies, items)
        if loaded:
            refresh_xsrf_headers_from_cookies(session)
            prune_cookie_duplicates(session.cookies, "XSRF-TOKEN", base_url=REFERER)
            prune_cookie_duplicates(session.cookies, "bbm_session", base_url=REFERER)

        logger.info("Loaded %d cookies from: %s", loaded, p)
        return loaded
    except Exception as e:
        logger.warning("Failed to load session cookies: %s", e)
        return 0


def is_unauthenticated(resp: requests.Response) -> bool:
    """
    Check if a response indicates that the user is not authenticated.
    """
    try:
        url = getattr(resp, "url", "") or ""
        if "/login" in url:
            return True
        if resp.status_code in (401, 403):
            return True
        html = resp.text or ""
        # Heuristic: presence of login form fields
        if ('name="username"' in html and 'name="passwd"' in html) or (
            'name="password"' in html
        ):
            return True
    except Exception:
        pass
    return False


def _shop_url() -> str:
    """Build the absolute URL for the shop page from the base REFERER."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "shop")


def validate_session(
    session: requests.Session,
    logger: Logger = default_logger,
    force_check: bool = False,
) -> bool:
    """
    Fast session validation with caching to avoid expensive network calls.
    Only validates once every 5 minutes unless forced.
    """
    global _last_validation_time, _last_validation_result
    current_time = time.time()

    # Use cached validation if recent and not forced, but only if it was successful
    if (
        not force_check
        and (current_time - _last_validation_time) < FAST_VALIDATION_CACHE_TIME
        and _last_validation_result  # Only use cache if last result was positive
    ):
        logger.info(
            f"Using cached validation result (age: {int(current_time - _last_validation_time)}s)"
        )
        return _last_validation_result

    logger.info("Performing fresh session validation...")
    try:
        # Ensure proxy settings are applied to the session before validation
        # (This is usually already done by make_session(), but this is a safety check)
        from login import USE_PROXY, SOCKS_URL

        if USE_PROXY and not session.proxies:
            session.proxies.update({"http": SOCKS_URL, "https": SOCKS_URL})
            logger.info(f"Applied missing proxy settings for validation: {SOCKS_URL}")
            # Clear cached result since we just fixed the session configuration
            _last_validation_time = 0
            _last_validation_result = False

        # Make a request to a page that requires authentication
        url = _shop_url()
        resp = session.get(url, allow_redirects=True, timeout=30)  # Reduced timeout

        # Check if the response indicates we are unauthenticated
        is_valid = not is_unauthenticated(resp)

        # Cache the result
        _last_validation_time = current_time
        _last_validation_result = is_valid

        if is_valid:
            logger.info("✅ Session is valid")
        else:
            logger.warning("❌ Session validation failed")

        return is_valid
    except requests.RequestException as e:
        logger.error("Session validation failed due to network error: %s", e)
        # Don't cache network failures
        return False


def get_authenticated_session(logger: Logger = default_logger) -> requests.Session:
    """
    Provides a guaranteed authenticated session with optimized validation.
    Uses aggressive caching to minimize network overhead.
    """
    global _last_validation_time, _last_validation_result

    session = make_session()

    # Load cookies and try fast validation
    if load_session_cookies(session, logger=logger) > 0:
        if validate_session(session, logger=logger):
            logger.info("✅ Reusing valid session from cookies")
            return session
        else:
            logger.info("❌ Loaded session is invalid, performing fresh login")
            session.cookies.clear()

    # Perform fresh login if needed
    logger.info("🔐 Performing fresh login...")
    session, csrf, _, _ = fetch_login_and_extract()
    _ = perform_login(session, csrf)
    prune_cookie_duplicates(session.cookies, "XSRF-TOKEN", base_url=REFERER)
    prune_cookie_duplicates(session.cookies, "bbm_session", base_url=REFERER)

    # Mark as validated and cache
    _last_validation_time = time.time()
    _last_validation_result = True

    # Save the new session cookies with validation timestamp
    save_session_cookies(session, logger=logger)

    logger.info("✅ Fresh authentication completed")
    return session
