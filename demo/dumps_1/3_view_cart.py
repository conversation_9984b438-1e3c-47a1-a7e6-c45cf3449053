curl ^"https://ronaldo-club.to/api/cart/^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -b ^"__ddg9_=117.214.120.180; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk3ODcwLCJpYXQiOjE3NTgzNzUyNTIsImV4cCI6MTc2MDk2NzI1Mn0.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg8_=0GOJC9URXUrwG8rk; __ddg10_=1758375718^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cart^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^"

#fetch
fetch("https://ronaldo-club.to/api/cart/", {
  "headers": {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.6",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Brave\";v=\"140\"",
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": "\"Android\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sec-gpc": "1"
  },
  "referrer": "https://ronaldo-club.to/store/cart",
  "body": null,
  "method": "GET",
  "mode": "cors",
  "credentials": "include"
});

#response
{"success":true,"data":[{"_id":377654,"user_id":197870,"product_id":94226,"product_table_name":"dumps","createdAt":"2025-09-20T13:41:58.000Z","brand":"VISA","state":"CA","code":221,"country":"US","zip":"23506","bin":"408039","price":"24.0000","discount":0}],"totalCartPrice":24}

