"""
Authentication Synchronization Service

Manages authentication synchronization between API v1 and API v2,
allowing API v2 to inherit authentication from API v1 when configured.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from admin.models.api_config_storage import get_admin_api_service
from api_v2.config.api_config import create_api_v2_configuration, get_api1_configuration_for_inheritance
from shared_api.config.registry import api_registry
from shared_api.config.api_config import APIConfiguration, AuthenticationConfiguration
from shared_api.core.constants import AuthenticationType

logger = logging.getLogger(__name__)


class AuthenticationSyncService:
    """Service for managing authentication synchronization between APIs"""
    
    def __init__(self):
        self.admin_storage = get_admin_api_service()
    
    async def sync_api2_auth_from_api1(self, force_update: bool = False) -> Tuple[bool, str]:
        """Synchronize API v2 authentication from API v1.
        
        Args:
            force_update: Whether to force update even if API v2 has custom auth
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Get API v1 configuration
            api1_config = await self.admin_storage.get_api_config("api1")
            if not api1_config:
                api1_config = await self.admin_storage.get_api_config("api_v1")
            
            if not api1_config:
                return False, "API v1 configuration not found"
            
            # Get API v2 configuration
            api2_config = await self.admin_storage.get_api_config("api2")
            if not api2_config:
                return False, "API v2 configuration not found"
            
            # Check if API v2 has custom authentication and force_update is False
            if not force_update and self._has_custom_authentication(api2_config):
                return False, "API v2 has custom authentication. Use force_update=True to override."
            
            # Prepare API v1 configuration for inheritance
            api1_config_for_inheritance = {
                "authentication": api1_config.shared_config.get("authentication", {}),
                "credentials": {
                    "login_token": api1_config.shared_config.get("authentication", {}).get("bearer_token", ""),
                    "headers": api1_config.shared_config.get("default_headers", {}),
                    "session_cookies": api1_config.shared_config.get("session_cookies", {})
                },
                "shared_config": api1_config.shared_config
            }

            # Create new API v2 configuration with inherited authentication
            new_api2_config = create_api_v2_configuration(
                base_url=api2_config.shared_config.get("base_url", "https://ronaldo-club.to/api/cards/vhq"),
                inherit_auth_from_api1=True,
                api1_config=api1_config_for_inheritance,
                environment=api2_config.shared_config.get("environment", "production")
            )
            
            # Update API v2 configuration
            success = await self.admin_storage.update_api_config(
                name="api2",
                shared_config=new_api2_config,
                updated_by="auth_sync_service",
                changes_description="Synchronized authentication from API v1"
            )
            
            if success:
                logger.info("Successfully synchronized API v2 authentication from API v1")
                return True, "Authentication synchronized successfully"
            else:
                return False, "Failed to update API v2 configuration"
                
        except Exception as e:
            logger.error(f"Failed to sync API v2 auth from API v1: {e}")
            return False, f"Synchronization failed: {str(e)}"
    
    async def enable_auth_inheritance(self, api_name: str = "api2") -> Tuple[bool, str]:
        """Enable authentication inheritance for an API.
        
        Args:
            api_name: Name of the API to enable inheritance for
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if api_name != "api2":
                return False, "Authentication inheritance is currently only supported for API v2"
            
            return await self.sync_api2_auth_from_api1(force_update=True)
            
        except Exception as e:
            logger.error(f"Failed to enable auth inheritance for {api_name}: {e}")
            return False, f"Failed to enable inheritance: {str(e)}"
    
    async def disable_auth_inheritance(self, api_name: str = "api2") -> Tuple[bool, str]:
        """Disable authentication inheritance for an API.
        
        Args:
            api_name: Name of the API to disable inheritance for
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if api_name != "api2":
                return False, "Authentication inheritance is currently only supported for API v2"
            
            # Get current API v2 configuration
            api2_config = await self.admin_storage.get_api_config("api2")
            if not api2_config:
                return False, "API v2 configuration not found"
            
            # Create new API v2 configuration without inheritance
            new_api2_config = create_api_v2_configuration(
                base_url=api2_config.shared_config.get("base_url", "https://ronaldo-club.to/api/cards/vhq"),
                inherit_auth_from_api1=False,
                environment=api2_config.shared_config.get("environment", "production")
            )
            
            # Update API v2 configuration
            success = await self.admin_storage.update_api_config(
                name="api2",
                shared_config=new_api2_config,
                updated_by="auth_sync_service",
                changes_description="Disabled authentication inheritance"
            )
            
            if success:
                logger.info("Successfully disabled API v2 authentication inheritance")
                return True, "Authentication inheritance disabled"
            else:
                return False, "Failed to update API v2 configuration"
                
        except Exception as e:
            logger.error(f"Failed to disable auth inheritance for {api_name}: {e}")
            return False, f"Failed to disable inheritance: {str(e)}"
    
    async def get_auth_sync_status(self) -> Dict[str, Any]:
        """Get authentication synchronization status.
        
        Returns:
            Dictionary with sync status information
        """
        try:
            status = {
                "api1_exists": False,
                "api2_exists": False,
                "api1_has_auth": False,
                "api2_has_auth": False,
                "api2_inherits_from_api1": False,
                "last_sync": None,
                "sync_available": False
            }
            
            # Check API v1
            api1_config = await self.admin_storage.get_api_config("api1")
            if not api1_config:
                api1_config = await self.admin_storage.get_api_config("api_v1")
            
            if api1_config:
                status["api1_exists"] = True
                status["api1_has_auth"] = self._has_authentication(api1_config)
            
            # Check API v2
            api2_config = await self.admin_storage.get_api_config("api2")
            if api2_config:
                status["api2_exists"] = True
                status["api2_has_auth"] = self._has_authentication(api2_config)
                status["api2_inherits_from_api1"] = self._inherits_from_api1(api2_config)
                
                # Check last sync from audit trail
                for entry in reversed(api2_config.audit_trail):
                    if "authentication" in entry.get("changes", {}).get("description", "").lower():
                        status["last_sync"] = entry.get("timestamp")
                        break
            
            status["sync_available"] = status["api1_exists"] and status["api2_exists"] and status["api1_has_auth"]
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get auth sync status: {e}")
            return {"error": str(e)}
    
    def _has_authentication(self, config) -> bool:
        """Check if a configuration has meaningful authentication."""
        try:
            auth_config = config.shared_config.get("authentication", {})
            auth_type = auth_config.get("type", "none")
            
            if auth_type == "none":
                return False
            
            # Check for actual credentials
            if auth_type == "bearer_token":
                return bool(auth_config.get("bearer_token"))
            elif auth_type == "api_key":
                return bool(auth_config.get("api_key"))
            elif auth_type == "basic_auth":
                return bool(auth_config.get("username") and auth_config.get("password"))
            
            return True
            
        except Exception:
            return False
    
    def _has_custom_authentication(self, config) -> bool:
        """Check if a configuration has custom (non-inherited) authentication."""
        try:
            # This is a simple heuristic - in a real implementation,
            # you might want to store inheritance metadata
            description = config.description or ""
            return "inherited" not in description.lower() and self._has_authentication(config)
            
        except Exception:
            return False
    
    def _inherits_from_api1(self, config) -> bool:
        """Check if a configuration inherits authentication from API v1."""
        try:
            description = config.description or ""
            return "inherited" in description.lower() or "inherit" in description.lower()
            
        except Exception:
            return False


# Global service instance
_auth_sync_service = None


def get_auth_sync_service() -> AuthenticationSyncService:
    """Get the global authentication sync service instance"""
    global _auth_sync_service
    if _auth_sync_service is None:
        _auth_sync_service = AuthenticationSyncService()
    return _auth_sync_service
