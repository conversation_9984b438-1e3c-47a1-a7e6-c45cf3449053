"""Adapters that expose API v2 browse functionality through v1-style interfaces."""

from __future__ import annotations

from typing import Any, Dict, Optional

from api_v2.services.browse_service import APIV2BrowseService, APIV2BrowseParams, APIV2BrowseResponse
from services.external_api_service import APIResponse, APIOperation, ListItemsParams


class APIV2ExternalBrowseAdapter:
    """Adapter that makes API v2 behave like the legacy ExternalAPIService."""

    def __init__(self, api_config) -> None:
        self._api_config = api_config
        config_kwargs = self._prepare_config_kwargs(api_config)
        self._service = APIV2BrowseService(config_kwargs=config_kwargs)

    @property
    def config(self):
        """Provide compatibility with health check expectations"""
        return self._api_config

    def _prepare_config_kwargs(self, api_config) -> Dict[str, Any]:
        """
        Prepare configuration kwargs for APIV2BrowseService.

        This method has been simplified to use shared authentication,
        eliminating duplication with API v1 authentication logic.
        """
        from services.shared_auth import get_shared_auth_config

        # Use shared authentication configuration
        shared_auth = get_shared_auth_config()

        if shared_auth:
            # Use shared authentication - ensures consistency with API v1
            login_token = shared_auth.login_token
            session_cookies = shared_auth.session_cookies
            default_headers = dict(getattr(api_config, "default_headers", {}) or {})

            # Ensure cookie header is present
            if shared_auth.session_cookies:
                cookie_header = "; ".join(f"{k}={v}" for k, v in shared_auth.session_cookies.items() if v)
                default_headers["cookie"] = cookie_header
        else:
            # Fallback to extracting from api_config for backward compatibility
            login_token = ""
            session_cookies = {}
            default_headers = dict(getattr(api_config, "default_headers", {}) or {})
            session_cookies = self._extract_session_cookies(default_headers)

        environment = getattr(api_config, "environment", "production")

        # Debug logging
        import logging
        logger = logging.getLogger(__name__)
        logger.debug(f"API v2 adapter config: login_token={'***' if login_token else 'None'}, "
                    f"cookie_header={bool('cookie' in default_headers)}, "
                    f"shared_auth={'present' if shared_auth else 'None'}")

        return {
            "base_url": getattr(api_config, "base_url", ""),
            "login_token": login_token,
            "session_cookies": session_cookies,
            "environment": environment,
            "default_headers": default_headers or None,
            "inherit_auth_from_api1": False,  # Don't inherit - we use shared auth
        }

    def _prepare_api1_config_for_inheritance(self, api_config) -> Optional[Dict[str, Any]]:
        """Prepare API v1 configuration data for authentication inheritance."""
        try:
            # Extract authentication configuration from API v1 config
            auth_config = getattr(api_config, "auth_config", None)
            authentication = getattr(api_config, "authentication", {})

            # Build the configuration structure expected by the inheritance function
            api1_config = {
                "authentication": {},
                "credentials": {},
                "shared_config": {
                    "authentication": {},
                    "default_headers": getattr(api_config, "default_headers", {}),
                    "session_cookies": {}
                }
            }

            # Extract authentication from auth_config object
            if auth_config is not None:
                try:
                    from shared_api.core.constants import AuthenticationType
                    auth_type = getattr(auth_config, "type", None)

                    if auth_type == AuthenticationType.BEARER_TOKEN:
                        bearer_token = getattr(auth_config, "bearer_token", "")
                        if bearer_token:
                            api1_config["authentication"] = {
                                "type": "bearer_token",
                                "bearer_token": bearer_token
                            }
                            api1_config["credentials"]["login_token"] = bearer_token
                            api1_config["shared_config"]["authentication"]["bearer_token"] = bearer_token

                    elif auth_type == AuthenticationType.API_KEY:
                        api_key = getattr(auth_config, "api_key", "")
                        api_key_header = getattr(auth_config, "api_key_header", "X-API-Key")
                        if api_key:
                            api1_config["authentication"] = {
                                "type": "api_key",
                                "api_key": api_key,
                                "api_key_header": api_key_header
                            }

                    elif auth_type == AuthenticationType.BASIC_AUTH:
                        username = getattr(auth_config, "username", "")
                        password = getattr(auth_config, "password", "")
                        if username and password:
                            api1_config["authentication"] = {
                                "type": "basic_auth",
                                "username": username,
                                "password": password
                            }

                    # Add custom headers
                    custom_headers = getattr(auth_config, "custom_headers", {})
                    if custom_headers:
                        api1_config["authentication"]["custom_headers"] = custom_headers
                        api1_config["shared_config"]["default_headers"].update(custom_headers)

                except Exception as e:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Failed to extract auth_config: {e}")

            # Fallback to legacy authentication dict
            elif isinstance(authentication, dict) and authentication:
                api1_config["authentication"] = authentication.copy()

                # Extract bearer token from Authorization header if present
                if "Authorization" in authentication:
                    auth_header = authentication["Authorization"]
                    if auth_header.startswith("Bearer "):
                        token = auth_header[7:]  # Remove "Bearer " prefix
                        api1_config["credentials"]["login_token"] = token
                        api1_config["shared_config"]["authentication"]["bearer_token"] = token

            # Extract session cookies from various sources
            session_cookies = {}

            # From headers (cookie header)
            headers = getattr(api_config, "default_headers", {})
            if "cookie" in headers:
                cookie_header = headers["cookie"]
                # Parse cookie header into dict
                for cookie in cookie_header.split(";"):
                    if "=" in cookie:
                        key, value = cookie.strip().split("=", 1)
                        session_cookies[key] = value

            # From direct session_cookies attribute
            direct_cookies = getattr(api_config, "session_cookies", {})
            if direct_cookies:
                session_cookies.update(direct_cookies)

            if session_cookies:
                api1_config["credentials"]["session_cookies"] = session_cookies
                api1_config["shared_config"]["session_cookies"] = session_cookies

            # Extract headers
            if headers:
                api1_config["credentials"]["headers"] = headers

            return api1_config if (api1_config["authentication"] or
                                 api1_config["credentials"] or
                                 api1_config["shared_config"]["authentication"]) else None

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Failed to prepare API v1 config for inheritance: {e}")
            return None

    @staticmethod
    def _extract_session_cookies(headers: Dict[str, str]) -> Optional[Dict[str, str]]:
        cookie_header = None
        for key in ("cookie", "Cookie"):
            if key in headers:
                cookie_header = headers.pop(key)
                break

        if not cookie_header:
            return None

        cookies: Dict[str, str] = {}
        for pair in cookie_header.split(";"):
            if "=" not in pair:
                continue
            name, value = pair.strip().split("=", 1)
            if name:
                cookies[name.strip()] = value.strip()
        return cookies or None

    @staticmethod
    def _convert_params(params: Optional[ListItemsParams]) -> APIV2BrowseParams:
        if params is None:
            return APIV2BrowseParams()

        return APIV2BrowseParams(
            page=params.page,
            limit=params.limit,
            base=params.base,
            bank=params.bank,
            bin=params.bin,
            country=params.country,
            state=params.state,
            city=params.city,
            brand=params.brand,
            type=params.type,
            level=getattr(params, "level", ""),
            zip=params.zip,
            price_from=params.price_from,
            price_to=params.price_to,
            zip_check=params.zip_check,
            address=params.address,
            phone=params.phone,
            email=params.email,
            without_cvv=params.without_cvv,
            refundable=params.refundable,
            expire_this_month=params.expire_this_month,
            dob=params.dob,
            ssn=params.ssn,
            mmn=params.mmn,
            ip=params.ip,
            dl=params.dl,
            ua=params.ua,
            discount=params.discount,
        )

    @staticmethod
    def _to_api_response(response: APIV2BrowseResponse, operation: APIOperation) -> APIResponse:
        return APIResponse(
            success=response.success,
            data=response.data,
            error=response.error,
            status_code=response.status_code,
            raw_response=response.raw_response,
            operation=operation,
        )

    async def list_items(
        self,
        params: Optional[ListItemsParams] = None,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        browse_params = self._convert_params(params)
        response = await self._service.list_items(browse_params, user_id=user_id)
        return self._to_api_response(response, APIOperation.LIST_ITEMS)

    async def get_filters(
        self,
        filter_name: str,
        params: Optional[ListItemsParams] = None,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        browse_params = self._convert_params(params)
        response = await self._service.get_filters(filter_name, browse_params, user_id=user_id)
        return self._to_api_response(response, APIOperation.FILTERS)

    async def list_orders(
        self,
        page: int = 1,
        limit: int = 10,
    ) -> APIResponse:
        response = await self._service.list_orders(page=page, limit=limit)
        return self._to_api_response(response, APIOperation.LIST_ORDERS)

    async def close(self) -> None:
        await self._service.close()
