#!/usr/bin/env python3
"""
API v3 Usage Examples

This file demonstrates how to use the API v3 integration for various operations.
"""

import asyncio
import sys
from pathlib import Path

# Add the bot root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.external_api_service import ExternalAPIService, ListItemsParams


async def example_basic_operations():
    """Example of basic API v3 operations"""
    print("🔍 Basic API v3 Operations Example")
    print("=" * 50)
    
    # Initialize the API service
    api_service = ExternalAPIService()
    
    try:
        # Test connection first
        print("1. Testing connection...")
        test_response = await api_service.test_api_v3_connection()
        
        if test_response.success:
            conn_info = test_response.data["connection_info"]
            print(f"   ✅ Connected to: {conn_info['base_url']}")
            print(f"   🧅 Using Tor: {conn_info['use_tor_proxy']}")
        else:
            print(f"   ❌ Connection failed: {test_response.error}")
            return
        
        # List items with basic filters
        print("\n2. Listing items...")
        list_response = await api_service.list_items()
        
        if list_response.success:
            items = list_response.data["items"]
            total = list_response.data.get("totalCount", len(items))
            print(f"   ✅ Found {len(items)} items (total: {total})")
            
            # Show first few items
            for i, item in enumerate(items[:3]):
                print(f"   📋 Item {i+1}: {item.get('bin', 'N/A')} - {item.get('bank', 'N/A')} - ${item.get('price', 'N/A')}")
        else:
            print(f"   ❌ Failed to list items: {list_response.error}")
            return
        
        # Add first item to cart (if available)
        if items:
            print("\n3. Adding item to cart...")
            item_id = items[0]["id"]
            cart_response = await api_service.add_to_cart(item_id)
            
            if cart_response.success:
                print(f"   ✅ Added item {item_id} to cart")
            else:
                print(f"   ❌ Failed to add to cart: {cart_response.error}")
        
        # View cart
        print("\n4. Viewing cart...")
        cart_response = await api_service.view_cart()
        
        if cart_response.success:
            cart_items = cart_response.data.get("items", [])
            total = cart_response.data.get("total", "0.00")
            print(f"   ✅ Cart has {len(cart_items)} items, total: ${total}")
        else:
            print(f"   ❌ Failed to view cart: {cart_response.error}")
        
    except Exception as e:
        print(f"❌ Error during basic operations: {e}")


async def example_filtered_search():
    """Example of filtered item search"""
    print("\n🔍 Filtered Search Example")
    print("=" * 50)
    
    api_service = ExternalAPIService()
    
    try:
        # Search with specific filters
        filters = ListItemsParams(
            bins="405621,424242",  # Specific BIN numbers
            country="US",          # Country filter
            page=1,                # First page
            limit=10               # Limit results
        )
        
        print(f"Searching with filters:")
        print(f"   BINs: {filters.bins}")
        print(f"   Country: {filters.country}")
        print(f"   Page: {filters.page}, Limit: {filters.limit}")
        
        response = await api_service.list_items(filters)
        
        if response.success:
            items = response.data["items"]
            print(f"\n✅ Found {len(items)} filtered items:")
            
            for item in items:
                bin_num = item.get("bin", "N/A")
                bank = item.get("bank", "N/A")
                country = item.get("country", "N/A")
                price = item.get("price", "N/A")
                refundable = "✅" if item.get("refundable") else "❌"
                
                print(f"   💳 {bin_num} | {bank} | {country} | ${price} | Refundable: {refundable}")
        else:
            print(f"❌ Filtered search failed: {response.error}")
            
    except Exception as e:
        print(f"❌ Error during filtered search: {e}")


async def example_order_management():
    """Example of order management operations"""
    print("\n📦 Order Management Example")
    print("=" * 50)
    
    api_service = ExternalAPIService()
    
    try:
        # List existing orders
        print("1. Listing orders...")
        orders_response = await api_service.list_orders()
        
        if orders_response.success:
            orders = orders_response.data.get("orders", [])
            print(f"   ✅ Found {len(orders)} orders")
            
            if orders:
                # Show first order details
                order = orders[0]
                order_id = order.get("id", "N/A")
                status = order.get("status", "N/A")
                total = order.get("total", "N/A")
                print(f"   📋 Order {order_id}: {status} - ${total}")
                
                # Example order check (using dummy IDs for demonstration)
                print(f"\n2. Checking order {order_id}...")
                check_response = await api_service.check_order(123, 456)  # Example IDs
                
                if check_response.success:
                    card_status = check_response.data.get("card_status", "unknown")
                    print(f"   ✅ Card status: {card_status}")
                else:
                    print(f"   ❌ Order check failed: {check_response.error}")
                
                # Example unmask operation (using dummy IDs)
                print(f"\n3. Unmasking cards...")
                unmask_response = await api_service.unmask_order(123, [456, 789])  # Example IDs
                
                if unmask_response.success:
                    cards_revealed = unmask_response.data.get("cards_revealed", 0)
                    print(f"   ✅ Unmasked {cards_revealed} cards")
                else:
                    print(f"   ❌ Unmask failed: {unmask_response.error}")
            else:
                print("   ℹ️  No orders found")
        else:
            print(f"   ❌ Failed to list orders: {orders_response.error}")
            
    except Exception as e:
        print(f"❌ Error during order management: {e}")


async def example_complete_workflow():
    """Example of complete shopping workflow"""
    print("\n🛒 Complete Shopping Workflow Example")
    print("=" * 50)
    
    api_service = ExternalAPIService()
    
    try:
        # Step 1: Browse items
        print("Step 1: Browsing items...")
        list_response = await api_service.list_items({"limit": 5})
        
        if not list_response.success:
            print(f"❌ Failed to browse items: {list_response.error}")
            return
        
        items = list_response.data["items"]
        if not items:
            print("❌ No items available")
            return
        
        print(f"✅ Found {len(items)} items")
        
        # Step 2: Select and add item to cart
        selected_item = items[0]
        item_id = selected_item["id"]
        print(f"\nStep 2: Adding item {item_id} to cart...")
        
        cart_response = await api_service.add_to_cart(item_id)
        if not cart_response.success:
            print(f"❌ Failed to add to cart: {cart_response.error}")
            return
        
        print("✅ Item added to cart")
        
        # Step 3: Review cart
        print("\nStep 3: Reviewing cart...")
        cart_response = await api_service.view_cart()
        
        if not cart_response.success:
            print(f"❌ Failed to view cart: {cart_response.error}")
            return
        
        cart_total = cart_response.data.get("total", "0.00")
        cart_items = cart_response.data.get("items", [])
        print(f"✅ Cart contains {len(cart_items)} items, total: ${cart_total}")
        
        # Step 4: Checkout (demonstration - be careful with real checkouts!)
        print("\nStep 4: Checkout (DEMO - not executing)...")
        print("   ⚠️  Checkout would be executed here in real usage")
        print("   💡 Use: checkout_response = await api_service.checkout()")
        
        # Simulate successful checkout for demonstration
        print("   ✅ Checkout completed (simulated)")
        simulated_order_id = "ORD123456"
        print(f"   📦 Order ID: {simulated_order_id}")
        
        print(f"\n🎉 Workflow completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during complete workflow: {e}")


async def example_error_handling():
    """Example of proper error handling"""
    print("\n⚠️  Error Handling Example")
    print("=" * 50)
    
    api_service = ExternalAPIService()
    
    try:
        # Example of handling various error scenarios
        print("1. Testing with invalid item ID...")
        
        # This should fail gracefully
        response = await api_service.add_to_cart("invalid_item_id")
        
        if response.success:
            print("   ✅ Unexpected success")
        else:
            print(f"   ✅ Handled error gracefully: {response.error}")
            print(f"   📊 Status code: {response.status_code}")
        
        # Example of connection testing
        print("\n2. Testing connection...")
        test_response = await api_service.test_api_v3_connection()
        
        if test_response.success:
            print("   ✅ Connection test passed")
        else:
            print(f"   ❌ Connection test failed: {test_response.error}")
            print("   💡 Check configuration and Tor connectivity")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("💡 This indicates a configuration or setup issue")


async def main():
    """Run all examples"""
    print("🚀 API v3 Usage Examples")
    print("=" * 70)
    
    examples = [
        ("Basic Operations", example_basic_operations),
        ("Filtered Search", example_filtered_search),
        ("Order Management", example_order_management),
        ("Complete Workflow", example_complete_workflow),
        ("Error Handling", example_error_handling),
    ]
    
    for name, example_func in examples:
        try:
            await example_func()
        except Exception as e:
            print(f"\n❌ Example '{name}' failed: {e}")
        
        print("\n" + "─" * 70)
    
    print("\n💡 Tips:")
    print("   • Ensure your .env file is properly configured")
    print("   • Start Tor if using .onion domains")
    print("   • Run validation script first: python scripts/validate_api_v3_setup.py")
    print("   • Check logs for detailed error information")


if __name__ == "__main__":
    asyncio.run(main())
