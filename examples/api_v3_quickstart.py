"""
API v3 Quick Start Example

This example shows how to use API v3 for browsing and filtering cards.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import API v3 components
from api_v3 import get_api_v3_browse_service, APIV3BrowseParams
from api_v3.adapter import get_api_v3_adapter
from api_v3.models.card_model import APIV3CardList


async def example_basic_browse():
    """Example: Basic card browsing"""
    print("=" * 60)
    print("Example 1: Basic Card Browsing")
    print("=" * 60)

    # Get service instance
    service = get_api_v3_browse_service()

    # Create filter parameters
    params = APIV3BrowseParams(
        bins="555426",  # Filter by BIN
        country=["UNITED STATES"],  # Filter by country
        scheme=["MASTERCARD"],  # Filter by card scheme
    )

    # Browse cards
    response = await service.list_items(params, user_id="demo_user")

    if response.success:
        print(f"✓ Found {len(response.data['data'])} cards")

        # Display first 3 cards
        for idx, card in enumerate(response.data["data"][:3], 1):
            print(f"\nCard {idx}:")
            print(f"  BIN: {card.get('bin', 'N/A')}")
            print(f"  Expiry: {card.get('expiry', 'N/A')}")
            print(f"  Country: {card.get('country', 'N/A')}")
            print(f"  Scheme: {card.get('scheme', 'N/A')}")
            print(f"  Type: {card.get('type', 'N/A')}")
            print(f"  Price: ${card.get('price', 'N/A')}")
    else:
        print(f"✗ Error: {response.error}")

    print()


async def example_with_adapter():
    """Example: Using the adapter for standard interface"""
    print("=" * 60)
    print("Example 2: Using Adapter")
    print("=" * 60)

    # Get adapter instance
    adapter = get_api_v3_adapter()

    # Browse with standard filter format
    result = await adapter.browse_cards(
        filters={
            "country": "UNITED STATES",
            "brand": "VISA",  # Adapter converts to 'scheme'
            "type": "CREDIT",
            "phone": True,  # Adapter converts to 'with_phone'
        },
        page=1,
        limit=10,
        user_id="demo_user",
    )

    if result["success"]:
        print(f"✓ Found {result['totalCount']} cards (showing page {result['page']})")

        # Display cards
        for idx, card in enumerate(result["data"][:3], 1):
            print(f"\nCard {idx}:")
            print(f"  ID: {card.get('id', 'N/A')}")
            print(f"  BIN: {card.get('bin', 'N/A')}")
            print(f"  Name: {card.get('name', 'N/A')}")
            print(f"  Scheme: {card.get('scheme', 'N/A')}")
            print(
                f"  Phone: {card.get('phone', 'Available' if card.get('phone') else 'N/A')}"
            )
    else:
        print(f"✗ Error: {result['error']}")

    print()


async def example_get_filters():
    """Example: Getting available filters"""
    print("=" * 60)
    print("Example 3: Getting Available Filters")
    print("=" * 60)

    # Get adapter instance
    adapter = get_api_v3_adapter()

    # Get filters
    result = await adapter.get_filters(user_id="demo_user")

    if result["success"]:
        print(f"✓ Found {len(result['filters'])} filter categories")

        # Display filter options
        for filter_obj in result["filters"][:3]:
            filter_name = filter_obj.get("name", "Unknown")
            filter_label = filter_obj.get("label", filter_name)
            options = filter_obj.get("options", [])

            print(f"\n{filter_label}:")
            print(f"  {len(options)} options available")

            # Show first 5 options
            for opt in options[:5]:
                label = opt.get("label", "N/A")
                print(f"    - {label}")

            if len(options) > 5:
                print(f"    ... and {len(options) - 5} more")
    else:
        print(f"✗ Error: {result['error']}")

    print()


async def example_advanced_filtering():
    """Example: Advanced filtering with multiple criteria"""
    print("=" * 60)
    print("Example 4: Advanced Filtering")
    print("=" * 60)

    # Get service instance
    service = get_api_v3_browse_service()

    # Create advanced filter parameters
    params = APIV3BrowseParams(
        country=["UNITED STATES", "CANADA"],  # Multiple countries
        scheme=["VISA", "MASTERCARD"],  # Multiple schemes
        type=["CREDIT"],  # Only credit cards
        level=["PLATINUM", "GOLD"],  # Premium cards only
        with_phone="true",  # Must have phone
        with_dob="true",  # Must have DOB
        bins="411111,555426",  # Specific BINs
    )

    # Browse cards
    response = await service.list_items(params, user_id="demo_user")

    if response.success:
        cards = response.data["data"]
        print(f"✓ Found {len(cards)} cards matching criteria")

        # Show summary statistics
        countries = {}
        schemes = {}

        for card in cards:
            country = card.get("country", "Unknown")
            scheme = card.get("scheme", "Unknown")

            countries[country] = countries.get(country, 0) + 1
            schemes[scheme] = schemes.get(scheme, 0) + 1

        print("\nCountry Distribution:")
        for country, count in countries.items():
            print(f"  {country}: {count} cards")

        print("\nScheme Distribution:")
        for scheme, count in schemes.items():
            print(f"  {scheme}: {count} cards")
    else:
        print(f"✗ Error: {response.error}")

    print()


async def example_parse_card_data():
    """Example: Parsing card data with models"""
    print("=" * 60)
    print("Example 5: Parsing Card Data")
    print("=" * 60)

    # Sample response from API v3
    sample_response = {
        "headers": [
            "",
            "BIN",
            "Expiry",
            "Base",
            "F. Name",
            "Country/Ethnicity/Continent",
            "Scheme/Type/Level",
            "Address/Phone/DOB",
            "Price",
        ],
        "rows": [
            [
                {
                    "text": "checkbox",
                    "input_value": "demo_card_123",
                    "input_name": "checked[]",
                },
                {"text": "555426"},
                {"text": "10/25"},
                {"text": "DEMO-BASE"},
                {"text": "John D."},
                {"text": "UNITED STATES,North America"},
                {"text": "MASTERCARD DEBIT PREPAID"},
                {"text": "123 Main St, City, 12345Phone : +1234567890DOB: YES"},
                {"text": "$8.50"},
            ]
        ],
    }

    # Parse using card list model
    card_list = APIV3CardList.from_response(sample_response)

    print(f"✓ Parsed {len(card_list.cards)} card(s)")

    if card_list.cards:
        card = card_list.cards[0]
        print(f"\nCard Details:")
        print(f"  ID: {card.id}")
        print(f"  BIN: {card.bin}")
        print(f"  Expiry: {card.expiry}")
        print(f"  Name: {card.f_name}")
        print(f"  Country: {card.country}")
        print(f"  Continent: {card.continent}")
        print(f"  Scheme: {card.scheme}")
        print(f"  Type: {card.card_type}")
        print(f"  Phone: {card.phone}")
        print(f"  DOB Available: {card.dob}")
        print(f"  Price: ${card.price}")

        # Convert to dict
        card_dict = card.to_dict()
        print(f"\nAs Dictionary: {len(card_dict)} fields")

    print()


async def main():
    """Run all examples"""
    print("\n" + "=" * 60)
    print("API v3 Quick Start Examples")
    print("=" * 60 + "\n")

    # Check configuration
    base_url = os.getenv("BASE_URL") or os.getenv("API_V3_BASE_URL")
    username = os.getenv("USERNAME") or os.getenv("API_V3_USERNAME")

    if not base_url or not username:
        print("⚠ Warning: Environment variables not configured")
        print("Please set BASE_URL and USERNAME in .env file")
        print("\nRunning example 5 (parsing) which doesn't require API access...")
        print()
        await example_parse_card_data()
        return

    print(f"Configuration:")
    print(f"  Base URL: {base_url}")
    print(f"  Username: {username}")
    print()

    try:
        # Run examples
        await example_basic_browse()
        await example_with_adapter()
        await example_get_filters()
        await example_advanced_filtering()
        await example_parse_card_data()

        print("=" * 60)
        print("All examples completed!")
        print("=" * 60)

    except Exception as e:
        print(f"\n✗ Error running examples: {e}")
        print("\nNote: Some examples may fail if API credentials are not configured.")
        print(
            "Check your .env file and ensure BASE_URL, USERNAME, and PASSWORD are set."
        )


if __name__ == "__main__":
    # Run examples
    asyncio.run(main())
