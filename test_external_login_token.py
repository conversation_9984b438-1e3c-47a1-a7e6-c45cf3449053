#!/usr/bin/env python3
"""
Test External Login Token Functionality

This script tests the external login token authentication and balance retrieval
to verify that the token is working correctly and balance information is being
retrieved properly.
"""

import asyncio
import sys
import os
import json
import base64
from datetime import datetime, timezone
sys.path.insert(0, '.')

from services.external_api_service import ExternalAPIService
from config.settings import get_settings

async def test_token_configuration():
    """Test that the token is properly configured"""
    print("🔍 Testing Token Configuration")
    print("=" * 60)
    
    # Get settings
    settings = get_settings()
    token = getattr(settings, "EXTERNAL_LOGIN_TOKEN", "")
    api_version = getattr(settings, "EXTERNAL_API_VERSION", "not_set")
    
    print(f"API Version: {api_version}")
    print(f"Token configured: {'Yes' if token else 'No'}")
    
    if not token:
        print("❌ EXTERNAL_LOGIN_TOKEN is not configured!")
        return False
    
    # Try to decode JWT token to check validity (basic check)
    try:
        # JWT tokens have 3 parts separated by dots
        parts = token.split('.')
        if len(parts) == 3:
            print(f"✅ Token has correct JWT format (3 parts)")

            # Try to decode the payload (middle part)
            try:
                # Add padding if needed
                payload_part = parts[1]
                # Add padding to make length multiple of 4
                payload_part += '=' * (4 - len(payload_part) % 4)

                decoded_bytes = base64.urlsafe_b64decode(payload_part)
                decoded = json.loads(decoded_bytes.decode('utf-8'))

                print(f"✅ Token payload decoded successfully")
                print(f"   User ID: {decoded.get('id', 'unknown')}")

                # Check expiration
                exp = decoded.get('exp')
                if exp:
                    exp_date = datetime.fromtimestamp(exp, tz=timezone.utc)
                    now = datetime.now(timezone.utc)

                    if exp_date > now:
                        time_left = exp_date - now
                        print(f"✅ Token is not expired (expires in {time_left.days} days)")
                    else:
                        print(f"❌ Token is EXPIRED (expired {(now - exp_date).days} days ago)")
                        return False
                else:
                    print("⚠️ Token has no expiration field")

            except Exception as decode_e:
                print(f"⚠️ Could not decode token payload: {decode_e}")
                print("   Token format appears correct but payload is not readable")
        else:
            print(f"⚠️ Token does not have JWT format (expected 3 parts, got {len(parts)})")

    except Exception as e:
        print(f"⚠️ Could not analyze token: {e}")
    
    print(f"   Token length: {len(token)} characters")
    print(f"   Token starts with: {token[:20]}...")
    
    return True

async def test_external_api_service_config():
    """Test that ExternalAPIService is configured correctly"""
    print(f"\n🔧 Testing ExternalAPIService Configuration")
    print("=" * 60)
    
    try:
        # Create ExternalAPIService instance
        api_service = ExternalAPIService()
        
        # Get API configuration
        config = await api_service._get_api_config()
        
        if not config:
            print("❌ Failed to get API configuration")
            return False
        
        print(f"✅ API Configuration loaded successfully")
        print(f"   Base URL: {config.base_url}")
        print(f"   Login token configured: {'Yes' if config.login_token else 'No'}")
        print(f"   API version: {getattr(api_service, 'api_version', 'unknown')}")
        
        if config.login_token:
            print(f"   Token length: {len(config.login_token)} characters")
            print(f"   Token starts with: {config.login_token[:20]}...")
        else:
            print("❌ No login token in API configuration!")
            return False
        
        # Test headers and cookies building
        from services.external_api_service import APIOperation
        headers = api_service._build_headers(config, APIOperation.GET_USER_INFO)
        cookies = api_service._build_cookies(config)
        
        print(f"\n📋 Request Configuration:")
        print(f"   Headers count: {len(headers)}")
        print(f"   Cookies count: {len(cookies)}")
        print(f"   LoginToken cookie: {'Yes' if 'loginToken' in cookies else 'No'}")
        
        if 'loginToken' in cookies:
            login_token_cookie = cookies['loginToken']
            print(f"   LoginToken matches config: {'Yes' if login_token_cookie == config.login_token else 'No'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing ExternalAPIService configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_getme_endpoint():
    """Test the /user/getme endpoint to verify authentication"""
    print(f"\n🚀 Testing /user/getme Endpoint")
    print("=" * 60)
    
    try:
        # Create ExternalAPIService instance
        api_service = ExternalAPIService()
        
        print("📡 Making request to /user/getme endpoint...")
        
        # Call get_user_info method
        response = await api_service.get_user_info()
        
        print(f"📥 Response received:")
        print(f"   Success: {response.success}")
        print(f"   Status Code: {getattr(response, 'status_code', 'unknown')}")
        print(f"   Execution Time: {getattr(response, 'execution_time', 'unknown')}s")
        
        if response.success:
            print("✅ Authentication successful!")
            
            # Parse response data
            data = response.data
            if isinstance(data, dict):
                print(f"\n📊 User Information:")
                
                # Look for common user fields
                user_fields = ['id', 'user_id', 'username', 'email', 'balance', 'credits', 'funds']
                for field in user_fields:
                    if field in data:
                        value = data[field]
                        if field in ['balance', 'credits', 'funds']:
                            print(f"   💰 {field.title()}: {value}")
                        else:
                            print(f"   👤 {field.title()}: {value}")
                
                # Show all available fields
                print(f"\n📋 All available fields:")
                for key, value in data.items():
                    if key not in user_fields:
                        # Truncate long values
                        if isinstance(value, str) and len(value) > 50:
                            display_value = f"{value[:47]}..."
                        else:
                            display_value = value
                        print(f"   {key}: {display_value}")
                
                # Check for balance information specifically
                balance_found = False
                balance_fields = ['balance', 'credits', 'funds', 'money', 'wallet_balance', 'account_balance']
                for field in balance_fields:
                    if field in data:
                        balance_value = data[field]
                        print(f"\n💰 Balance Information Found:")
                        print(f"   Field: {field}")
                        print(f"   Value: {balance_value}")
                        print(f"   Type: {type(balance_value).__name__}")
                        
                        # Try to parse as number
                        try:
                            numeric_balance = float(balance_value)
                            print(f"   Numeric Value: {numeric_balance}")
                            
                            if numeric_balance <= 0:
                                print(f"⚠️ Balance is zero or negative: {numeric_balance}")
                                print("   This could explain 'insufficient balance' errors")
                            else:
                                print(f"✅ Balance is positive: {numeric_balance}")
                                
                        except (ValueError, TypeError):
                            print(f"⚠️ Could not parse balance as number: {balance_value}")
                        
                        balance_found = True
                        break
                
                if not balance_found:
                    print(f"\n⚠️ No balance information found in response")
                    print("   This could explain 'insufficient balance' errors")
                    print("   Available fields: " + ", ".join(data.keys()))
                
            else:
                print(f"⚠️ Response data is not a dictionary: {type(data)}")
                print(f"   Raw data: {data}")
            
            return True
            
        else:
            print("❌ Authentication failed!")
            print(f"   Error: {response.error}")
            
            # Check for specific error patterns
            error_msg = response.error or ""
            if "401" in error_msg or "unauthorized" in error_msg.lower():
                print("   🔍 This appears to be an authentication error")
                print("   💡 Possible causes:")
                print("      - Token is invalid or expired")
                print("      - Token format is incorrect")
                print("      - API endpoint requires different authentication")
            elif "403" in error_msg or "forbidden" in error_msg.lower():
                print("   🔍 This appears to be an authorization error")
                print("   💡 Possible causes:")
                print("      - Token is valid but lacks required permissions")
                print("      - Account is suspended or restricted")
            elif "404" in error_msg:
                print("   🔍 Endpoint not found")
                print("   💡 Possible causes:")
                print("      - Wrong API version or endpoint URL")
                print("      - API structure has changed")
            
            return False
            
    except Exception as e:
        print(f"❌ Exception during /user/getme test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_balance_retrieval_methods():
    """Test different methods to retrieve balance information"""
    print(f"\n💰 Testing Balance Retrieval Methods")
    print("=" * 60)
    
    try:
        api_service = ExternalAPIService()
        
        # Test 1: get_user_info (already tested above, but check balance specifically)
        print("📡 Method 1: get_user_info() for balance...")
        user_response = await api_service.get_user_info()
        
        if user_response.success and user_response.data:
            balance_fields = ['balance', 'credits', 'funds', 'money', 'wallet_balance']
            balance_found = False
            
            for field in balance_fields:
                if field in user_response.data:
                    balance = user_response.data[field]
                    print(f"   ✅ Found balance in {field}: {balance}")
                    balance_found = True
                    break
            
            if not balance_found:
                print("   ⚠️ No balance field found in user info")
        else:
            print("   ❌ get_user_info() failed")
        
        # Test 2: view_cart (might contain balance info)
        print("\n📡 Method 2: view_cart() for balance info...")
        cart_response = await api_service.view_cart()
        
        if cart_response.success and cart_response.data:
            print(f"   ✅ Cart response received")
            
            # Look for balance in cart response
            cart_data = cart_response.data
            if isinstance(cart_data, dict):
                balance_fields = ['balance', 'user_balance', 'account_balance', 'available_balance']
                for field in balance_fields:
                    if field in cart_data:
                        balance = cart_data[field]
                        print(f"   💰 Found balance in cart response - {field}: {balance}")
                        break
                else:
                    print("   ⚠️ No balance field found in cart response")
                    print(f"   Available fields: {list(cart_data.keys())}")
            else:
                print(f"   ⚠️ Cart data is not a dictionary: {type(cart_data)}")
        else:
            print("   ❌ view_cart() failed")
            if cart_response.error:
                print(f"   Error: {cart_response.error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception during balance retrieval test: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_insufficient_balance_issue():
    """Analyze potential causes of insufficient balance errors"""
    print(f"\n🔍 Analyzing 'Insufficient Balance' Issue")
    print("=" * 60)
    
    potential_causes = [
        {
            "cause": "Token Authentication Failure",
            "description": "Login token is invalid, expired, or not being sent correctly",
            "symptoms": ["401/403 errors", "Authentication failures", "Empty user info"],
            "solution": "Verify token validity and format"
        },
        {
            "cause": "Balance Field Missing",
            "description": "API response doesn't contain balance information",
            "symptoms": ["No balance field in user info", "Balance parsing errors"],
            "solution": "Check API documentation for correct balance field name"
        },
        {
            "cause": "Balance Parsing Error",
            "description": "Balance value exists but cannot be parsed as number",
            "symptoms": ["String balance values", "Type conversion errors"],
            "solution": "Implement robust balance parsing logic"
        },
        {
            "cause": "Actual Insufficient Balance",
            "description": "Account genuinely has zero or negative balance",
            "symptoms": ["Balance = 0", "Negative balance values"],
            "solution": "Add funds to account or check account status"
        },
        {
            "cause": "API Version Mismatch",
            "description": "Using wrong API version for balance retrieval",
            "symptoms": ["404 errors", "Unexpected response format"],
            "solution": "Verify correct API version and endpoints"
        }
    ]
    
    print("🔍 Potential Causes of 'Insufficient Balance' Errors:")
    print()
    
    for i, cause in enumerate(potential_causes, 1):
        print(f"{i}. {cause['cause']}")
        print(f"   Description: {cause['description']}")
        print(f"   Symptoms: {', '.join(cause['symptoms'])}")
        print(f"   Solution: {cause['solution']}")
        print()
    
    print("💡 Recommended Investigation Steps:")
    print("1. ✅ Verify token authentication with /user/getme")
    print("2. ✅ Check balance field presence and format")
    print("3. ✅ Test balance parsing logic")
    print("4. ✅ Confirm API version compatibility")
    print("5. ✅ Review actual account balance on external platform")

async def main():
    """Run all external login token tests"""
    try:
        print("🔍 EXTERNAL LOGIN TOKEN VERIFICATION")
        print("=" * 60)
        
        # Run all tests
        config_ok = await test_token_configuration()
        service_ok = await test_external_api_service_config()
        getme_ok = await test_getme_endpoint()
        balance_ok = await test_balance_retrieval_methods()
        
        # Analysis
        analyze_insufficient_balance_issue()
        
        # Overall result
        all_tests_passed = config_ok and service_ok and getme_ok and balance_ok
        
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        print(f"✅ Token Configuration: {'PASS' if config_ok else 'FAIL'}")
        print(f"✅ Service Configuration: {'PASS' if service_ok else 'FAIL'}")
        print(f"✅ /user/getme Authentication: {'PASS' if getme_ok else 'FAIL'}")
        print(f"✅ Balance Retrieval: {'PASS' if balance_ok else 'FAIL'}")
        
        if all_tests_passed:
            print(f"\n🎉 ALL TESTS PASSED!")
            print("✅ External login token is working correctly")
            print("✅ Authentication is successful")
            print("🔍 If 'insufficient balance' errors persist, check:")
            print("   - Actual account balance on external platform")
            print("   - Balance parsing logic in checkout process")
            print("   - API response format changes")
        else:
            print(f"\n❌ SOME TESTS FAILED")
            print("🔧 Review the failed tests above for specific issues")
            print("💡 Focus on fixing authentication before investigating balance")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
