"""
Orders-related Telegram handlers (view purchased card details)
"""

from __future__ import annotations

import logging
import asyncio
from datetime import datetime, timezone, timedelta

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from middleware import attach_common_middlewares
from services.user_service import UserService
from services.card_service import CardService
from services.cart_service import CartService
from services.external_api_service import get_external_api_service
from config.settings import get_settings
from database.connection import get_collection
from utils.texts import DEMO_WATERMARK

logger = logging.getLogger(__name__)


class OrdersHandlers:
    def __init__(self):
        from services.external_api_service import ExternalAPIService

        self.user_service = UserService()
        # Note: CardService should be created per-user with proper API selection
        # For now, use explicit API v1 to avoid global setting interference
        self.card_service = CardService(external_api_service=ExternalAPIService(api_version="v1"))
        self.cart_service = CartService()
        self.external_api = get_external_api_service()
        self.purchases = get_collection("purchases")

    async def cb_view_purchased_card(self, callback: CallbackQuery) -> None:
        """Show details for a purchased card by ID"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = parts[2]

            # Fetch order info and show details (sanitized)
            order = await self._fetch_order_for_card(str(db_user.id), card_id)

            # Build details text
            if order:
                details_text = self._format_order_details(order)
            else:
                details_text = f"Could not load details for card #{card_id}."

            # 60s countdown expiry
            expiry = datetime.now(timezone.utc) + timedelta(seconds=60)
            expiry_ts = int(expiry.timestamp())

            # Enhanced header with card information
            card_name = "Unknown Card"
            if order and isinstance(order, dict):
                bank = order.get("bank", "")
                brand = order.get("brand", "")
                if bank and brand:
                    card_name = f"{bank} {brand}"
                elif bank:
                    card_name = bank
                elif brand:
                    card_name = brand

            header = f"💳 <b>{card_name}</b>\n🔍 <i>Card Details & Status</i>\n"
            timer_line = self._format_timer(expiry_ts)
            body = details_text

            # Try to resolve external order id for check API
            order_id_for_check = None
            try:
                if isinstance(order, dict) and isinstance(order.get("_id"), int):
                    order_id_for_check = int(order.get("_id"))
                else:
                    order_id_for_check = await self._resolve_external_order_id(card_id)
            except Exception:
                order_id_for_check = None

            # Enhanced keyboard layout matching browse functionality
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔍 Check Card Status",
                            callback_data=f"orders:check:{order_id_for_check or ''}:{card_id}:{expiry_ts}",
                        )
                    ],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="📋 Order History", callback_data="menu:history"),
                    ],
                    [
                        InlineKeyboardButton(text="🏠 Main Menu", callback_data="menu:main"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )

            msg = await callback.message.edit_text(
                f"{header}{timer_line}\n\n{body}\n" + DEMO_WATERMARK,
                reply_markup=kb,
            )

            # Start countdown updater
            asyncio.create_task(
                self._run_countdown(
                    chat_id=msg.chat.id,
                    message_id=msg.message_id,
                    card_id=str(card_id),
                    expiry_ts=expiry_ts,
                    body_text=body,
                    order_id=str(order_id_for_check or ''),
                )
            )

            await callback.answer()
        except Exception as e:
            logger.error(f"Error showing purchased card: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_check_card(self, callback: CallbackQuery) -> None:
        """Handle Check Card button with enhanced UI and better error handling"""
        try:
            parts = (callback.data or "").split(":")
            # orders:check:<order_id_or_empty>:<card_id>:<expiry>
            if len(parts) not in (4, 5):
                await callback.answer("❌ Invalid request format", show_alert=True)
                return
            if len(parts) == 4:
                order_id_str = ""
                card_id = parts[2]
                expiry_ts = int(parts[3])
            else:
                order_id_str = parts[2]
                card_id = parts[3]
                expiry_ts = int(parts[4])

            # Check expiry
            now_ts = int(datetime.now(timezone.utc).timestamp())
            if now_ts >= expiry_ts:
                await self._disable_check_button(callback.message, card_id)
                await callback.answer("⛔ Check period expired", show_alert=True)
                return

            # Show loading state
            await callback.answer("🔍 Checking card status...", show_alert=False)

            # Resolve external order id
            order_id = int(order_id_str) if order_id_str.isdigit() else None
            if order_id is None:
                order_id = await self._resolve_external_order_id(card_id)

            if order_id is None:
                await callback.answer("❌ Could not resolve order ID", show_alert=True)
                return

            # Call external check API with enhanced error handling
            logger.info(f"Checking card {card_id} for order {order_id}")
            resp = await self.external_api.check_order(order_id, card_id=card_id)

            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                # Parse response data
                data = resp.data.get("data") or {}
                status = data.get("status") or resp.data.get("status") or "Unknown"

                # Enhanced status display with icons
                status_icon = self._get_status_icon(status)
                status_message = self._format_status_message(status, data)

                await callback.answer(
                    f"{status_icon} {status_message}",
                    show_alert=True
                )

                logger.info(f"Card check successful: {status}")
            else:
                # Enhanced error handling
                error_msg = getattr(resp, 'error', 'Unknown error')
                if "timeout" in error_msg.lower():
                    await callback.answer(
                        "⏱️ Check timed out\n\nThe card verification is taking longer than expected. Please try again in a few moments.",
                        show_alert=True
                    )
                elif "not found" in error_msg.lower():
                    await callback.answer(
                        "🔍 Card not found\n\nThis card may not be available for checking at this time.",
                        show_alert=True
                    )
                else:
                    await callback.answer(
                        f"❌ Check failed\n\n{error_msg}",
                        show_alert=True
                    )

                logger.warning(f"Card check failed for {card_id}: {error_msg}")

        except Exception as e:
            logger.error(f"Error checking card {card_id}: {e}")
            await callback.answer(
                "❌ System error\n\nAn unexpected error occurred. Please try again later.",
                show_alert=True
            )

    async def _fetch_order_for_card(self, user_id: str | None, card_id: str) -> dict | None:
        """Fetch latest order entry for the specified product/card id.

        Priority: local DB purchases (non-sensitive) -> external orders API (sanitized).
        """
        try:
            # 1) Try local DB purchases
            if user_id:
                doc = await self.purchases.find_one(
                    {"user_id": user_id, "$or": [{"metadata.card_id": card_id}, {"sku": f"card_{card_id}"}]},
                    sort=[("created_at", -1)],
                )
                if doc:
                    meta = doc.get("metadata", {}) or {}
                    safe = {
                        "_id": str(doc.get("_id")),
                        "product_id": meta.get("card_id") or card_id,
                        "price": doc.get("price"),
                        "status": doc.get("status"),
                        "bank": (meta.get("card_data", {}) or {}).get("bank"),
                        "brand": (meta.get("card_data", {}) or {}).get("brand"),
                        "level": (meta.get("card_data", {}) or {}).get("level"),
                        "type": (meta.get("card_data", {}) or {}).get("type"),
                        "country": (meta.get("card_data", {}) or {}).get("country"),
                        "state": (meta.get("card_data", {}) or {}).get("state"),
                        "city": (meta.get("card_data", {}) or {}).get("city"),
                        "zip": (meta.get("card_data", {}) or {}).get("zip"),
                        "createdAt": doc.get("created_at"),
                    }
                    return safe

            # 2) Fallback to external API
            resp = await self.external_api.list_orders(page=1, limit=10)
            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                for od in (resp.data.get("data") or []):
                    pid = od.get("product_id") or od.get("card_id") or od.get("id")
                    if pid is not None and str(pid) == str(card_id):
                        # Sanitize sensitive fields before showing
                        return {
                            k: v
                            for k, v in od.items()
                            if k
                            not in {
                                "cc",
                                "cvv",
                                "ssn",
                                "dl",
                                "ua",
                                "dob",
                                "expmonth",
                                "expyear",
                                "email",
                                "phone",
                                "address",
                            }
                        }
            return None
        except Exception as e:
            logger.warning(f"Failed to fetch order for card {card_id}: {e}")
            return None

    async def _resolve_external_order_id(self, card_id: str) -> int | None:
        """Find the external order _id for a given product/card id from recent orders."""
        try:
            resp = await self.external_api.list_orders(page=1, limit=10)
            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                data = resp.data.get("data") or []
                for od in data:
                    pid = od.get("product_id") or od.get("card_id") or od.get("id")
                    if pid is not None and str(pid) == str(card_id):
                        oid = od.get("_id")
                        if isinstance(oid, int):
                            return oid
            return None
        except Exception:
            return None

    async def cb_orders_menu(self, callback: CallbackQuery) -> None:
        """Show user's recent orders (from local DB purchases)."""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            cursor = self.purchases.find({"user_id": str(db_user.id)}).sort("created_at", -1)
            docs = await cursor.limit(10).to_list(10)
            if not docs:
                text = (
                    "📦 <b>Your Order History</b>\n"
                    "🔍 <i>Recent Purchases & Cards</i>\n\n"
                    "📋 <b>No orders found</b>\n"
                    "You haven't made any purchases yet.\n\n"
                    "💡 <i>Start browsing our catalog to find cards!</i>"
                ) + DEMO_WATERMARK
                kb = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(text="🛒 Browse Catalog", callback_data="menu:browse")],
                        [InlineKeyboardButton(text="⬅️ Back to Menu", callback_data="menu:main")]
                    ]
                )
                await callback.message.edit_text(text, reply_markup=kb)
                await callback.answer()
                return

            # Enhanced header
            lines = [
                "📦 <b>Your Order History</b>",
                "🔍 <i>Recent Purchases & Cards</i>",
                ""
            ]

            buttons_rows: list[list[InlineKeyboardButton]] = []

            for i, d in enumerate(docs, 1):
                meta = d.get("metadata", {}) or {}
                cd = meta.get("card_data", {}) or {}
                cid = meta.get("card_id")
                if not cid and isinstance(d.get("sku"), str) and d["sku"].startswith("card_"):
                    try:
                        cid = int(d["sku"].split("_", 1)[1])
                    except Exception:
                        cid = None

                price = float(d.get("price", 0.0))
                status = d.get("status", "")
                bank = cd.get("bank", "Unknown Bank")
                brand = cd.get("brand", "")
                level = cd.get("level", "")
                created = d.get("created_at")

                # Enhanced status icon
                status_icon = "✅" if status.lower() in ["completed", "active", "valid"] else "⚠️" if status.lower() in ["pending", "processing"] else "❌"

                # Format card display
                card_name = f"{bank}"
                if brand and brand != bank:
                    card_name += f" {brand}"
                if level:
                    card_name += f" ({level})"

                # Enhanced order line
                lines.append(f"💳 <b>{i}. {card_name}</b>")
                lines.append(f"   {status_icon} Status: {status}")
                lines.append(f"   💰 Price: ${price:.2f}")
                if created:
                    lines.append(f"   📅 Date: {created}")
                lines.append("")

                if cid:
                    # Enhanced button text
                    button_text = f"🔍 View {bank[:15]}..." if len(bank) > 15 else f"🔍 View {bank}"
                    buttons_rows.append(
                        [
                            InlineKeyboardButton(
                                text=button_text,
                                callback_data=f"orders:view_card:{cid}",
                            )
                        ]
                    )

            # Enhanced navigation buttons
            buttons_rows.extend([
                [
                    InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                    InlineKeyboardButton(text="🔄 Refresh", callback_data="menu:history"),
                ],
                [
                    InlineKeyboardButton(text="🏠 Main Menu", callback_data="menu:main"),
                    InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main"),
                ]
            ])

            await callback.message.edit_text(
                "\n".join(lines) + "\n" + DEMO_WATERMARK,
                reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons_rows),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error showing orders: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    def _format_order_details(self, order: dict) -> str:
        """Format order details with enhanced UI matching browse functionality"""
        try:
            # Extract key information
            order_id = order.get("_id", "N/A")
            product_id = order.get("product_id", "N/A")
            status = order.get("status", "Unknown")
            price = order.get("price", "N/A")

            # Card information
            bank = order.get("bank", "Unknown Bank")
            brand = order.get("brand", "N/A")
            card_type = order.get("type", "N/A")
            level = order.get("level", "N/A")

            # Location information
            country = order.get("country", "N/A")
            state = order.get("state", "N/A")
            city = order.get("city", "N/A")
            zip_code = order.get("zip", "N/A")

            # Dates
            created_at = order.get("createdAt", "N/A")
            start_date = order.get("start_Date", "N/A")
            refund_at = order.get("refundAt", "N/A")

            # Build enhanced display
            lines = []

            # Header section with status
            status_icon = "✅" if status.lower() in ["active", "valid", "live"] else "⚠️" if status.lower() in ["pending", "checking"] else "❌"
            lines.append(f"📊 <b>Status:</b> {status_icon} {status}")
            lines.append("")

            # Card details section
            lines.append("💳 <b>Card Information</b>")
            lines.append(f"   🏦 <b>Bank:</b> {bank}")
            if brand != "N/A":
                lines.append(f"   🏷️ <b>Brand:</b> {brand}")
            if card_type != "N/A":
                lines.append(f"   📋 <b>Type:</b> {card_type}")
            if level != "N/A":
                lines.append(f"   ⭐ <b>Level:</b> {level}")
            lines.append("")

            # Financial information
            lines.append("💰 <b>Financial Details</b>")
            if isinstance(price, (int, float)):
                lines.append(f"   💵 <b>Price:</b> ${price:.2f}")
            else:
                lines.append(f"   💵 <b>Price:</b> {price}")
            lines.append("")

            # Location information (if available)
            if any(x != "N/A" for x in [country, state, city, zip_code]):
                lines.append("🌍 <b>Location Details</b>")
                if country != "N/A":
                    lines.append(f"   🏳️ <b>Country:</b> {country}")
                if state != "N/A":
                    lines.append(f"   📍 <b>State:</b> {state}")
                if city != "N/A":
                    lines.append(f"   🏙️ <b>City:</b> {city}")
                if zip_code != "N/A":
                    lines.append(f"   📮 <b>ZIP:</b> {zip_code}")
                lines.append("")

            # Order tracking
            lines.append("📅 <b>Order Timeline</b>")
            if created_at != "N/A":
                lines.append(f"   📝 <b>Created:</b> {created_at}")
            if start_date != "N/A":
                lines.append(f"   🚀 <b>Started:</b> {start_date}")
            if refund_at != "N/A":
                lines.append(f"   🔄 <b>Refund:</b> {refund_at}")

            # Technical details (collapsed)
            lines.append("")
            lines.append("🔧 <b>Technical Info</b>")
            lines.append(f"   🆔 <b>Order ID:</b> {order_id}")
            lines.append(f"   🎯 <b>Product ID:</b> {product_id}")

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Error formatting order details: {e}")
            return "❌ <b>Error loading card details</b>\n\nPlease try again or contact support if the issue persists."

    def _get_status_icon(self, status: str) -> str:
        """Get appropriate icon for card status"""
        status_lower = status.lower()
        if status_lower in ["active", "valid", "live", "working", "good"]:
            return "✅"
        elif status_lower in ["pending", "checking", "processing", "verifying"]:
            return "🔄"
        elif status_lower in ["dead", "invalid", "expired", "blocked", "declined"]:
            return "❌"
        elif status_lower in ["unknown", "unclear", "mixed"]:
            return "❓"
        else:
            return "ℹ️"

    def _format_status_message(self, status: str, data: dict) -> str:
        """Format enhanced status message with additional details"""
        status_lower = status.lower()

        # Base message
        if status_lower in ["active", "valid", "live", "working", "good"]:
            base_msg = f"Card Status: {status.upper()}"
        elif status_lower in ["pending", "checking", "processing", "verifying"]:
            base_msg = f"Card Status: {status.upper()}"
        elif status_lower in ["dead", "invalid", "expired", "blocked", "declined"]:
            base_msg = f"Card Status: {status.upper()}"
        else:
            base_msg = f"Card Status: {status}"

        # Add additional details if available
        details = []
        if "response_time" in data:
            details.append(f"Response: {data['response_time']}ms")
        if "last_checked" in data:
            details.append(f"Last checked: {data['last_checked']}")
        if "confidence" in data:
            details.append(f"Confidence: {data['confidence']}%")

        if details:
            return f"{base_msg}\n\n{' • '.join(details)}"
        else:
            return base_msg

    def _format_timer(self, expiry_ts: int) -> str:
        remaining = max(0, expiry_ts - int(datetime.now(timezone.utc).timestamp()))
        return f"⏳ <b>Time left:</b> {remaining}s"

    async def _run_countdown(
        self, chat_id: int, message_id: int, card_id: str, expiry_ts: int, body_text: str, order_id: str
    ) -> None:
        """Edit the message to update the countdown and disable the button at 0."""
        try:
            settings = get_settings()
            from aiogram import Bot

            bot = Bot(token=settings.BOT_TOKEN)
            # Try to update every second
            last_remaining = None
            while True:
                now_ts = int(datetime.now(timezone.utc).timestamp())
                remaining = max(0, expiry_ts - now_ts)
                if remaining == 0:
                    # Disable the button and update timer to 0
                    try:
                        await self._disable_check_button_by_ids(bot, chat_id, message_id, card_id)
                    finally:
                        break
                # Only edit when value changes to reduce calls
                if remaining != last_remaining:
                    try:
                        await bot.edit_message_text(
                            chat_id=chat_id,
                            message_id=message_id,
                            text=(
                                f"🔎 <b>Card #{card_id} Details</b>\n{self._format_timer(expiry_ts)}\n\n"
                                f"{body_text}\n"
                                + DEMO_WATERMARK
                            ),
                            reply_markup=InlineKeyboardMarkup(
                                inline_keyboard=[
                                    [
                                        InlineKeyboardButton(
                                            text=f"✅ Check Card ({remaining}s)",
                                            callback_data=f"orders:check:{order_id}:{card_id}:{expiry_ts}",
                                        )
                                    ],
                                    [
                                        InlineKeyboardButton(
                                            text="🛒 View Cart", callback_data="local:cart:view"
                                        ),
                                        InlineKeyboardButton(
                                            text="⬅️ Back", callback_data="menu:history"
                                        ),
                                    ],
                                ]
                            ),
                            parse_mode="HTML",
                        )
                    except Exception:
                        # Ignore edit errors (message may have changed)
                        pass
                    last_remaining = remaining
                await asyncio.sleep(1)
            await bot.session.close()
        except Exception as e:
            logger.error(f"Countdown error: {e}")

    async def _disable_check_button(self, message, card_id: str) -> None:
        try:
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⛔ Check Disabled", callback_data="noop")],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )
            await message.edit_reply_markup(reply_markup=kb)
        except Exception:
            pass

    async def _disable_check_button_by_ids(
        self, bot, chat_id: int, message_id: int, card_id: str
    ) -> None:
        try:
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⛔ Check Disabled", callback_data="noop")],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )
            await bot.edit_message_reply_markup(
                chat_id=chat_id, message_id=message_id, reply_markup=kb
            )
        except Exception:
            pass


def get_orders_router() -> Router:
    router = Router()
    attach_common_middlewares(router)
    handlers = OrdersHandlers()

    router.callback_query.register(
        handlers.cb_view_purchased_card, F.data.startswith("orders:view_card:")
    )
    router.callback_query.register(
        handlers.cb_check_card, F.data.startswith("orders:check:")
    )
    router.callback_query.register(
        handlers.cb_orders_menu, F.data == "menu:orders"
    )

    logger.info("Orders handlers registered")
    return router
