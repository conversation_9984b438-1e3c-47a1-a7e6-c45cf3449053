"""
Dump service for handling both dumps (v1) and vdumps (v2) APIs
Based on the demo API structure and responses found in demo/dumps_1 and demo/dumps_2
"""

from __future__ import annotations

import logging
from typing import Dict, Any, Optional, List
import asyncio
import time

from config.settings import get_settings
from utils.performance import monitor_performance

from services.external_api_service import ExternalAPIService
from services.shared_auth import build_default_headers_with_auth

logger = logging.getLogger(__name__)


class DumpService:
    """Service for handling both dumps v1 and vdumps v2 API operations"""

    def __init__(self, external_api_service: Optional[ExternalAPIService] = None):
        self.settings = get_settings()
        self.external_api = external_api_service or ExternalAPIService()

    async def close(self) -> None:
        """Close service sessions"""
        if hasattr(self.external_api, "close"):
            await self.external_api.close()

    # DUMPS V1 API Methods
    async def list_dumps(
        self,
        page: int = 1,
        limit: int = 10,
        base: str = "",
        bank: str = "",
        bin: str = "",
        country: str = "",
        state: str = "",
        city: str = "",
        brand: str = "",
        type: str = "",
        zip: str = "",
        price_from: float = 0,
        price_to: float = 500,
        zip_check: bool = False,
        track1: bool = False,
        track2: bool = False,
        pin: bool = False,
    ) -> Dict[str, Any]:
        """
        List dumps v1 - equivalent to /api/cards/dumps/list
        """
        logger.info(
            "Fetching dumps v1 list",
            extra={
                "page": page,
                "limit": limit,
                "filters": {
                    "base": base,
                    "bank": bank,
                    "bin": bin,
                    "country": country,
                    "state": state,
                    "city": city,
                    "brand": brand,
                    "type": type,
                    "zip": zip,
                    "price_from": price_from,
                    "price_to": price_to,
                    "zip_check": zip_check,
                    "track1": track1,
                    "track2": track2,
                    "pin": pin,
                },
            },
        )

        # Build query parameters
        params = {
            "page": page,
            "limit": limit,
            "base": base,
            "bank": bank,
            "bin": bin,
            "country": country,
            "state": state,
            "city": city,
            "brand": brand,
            "type": type,
            "zip": zip,
            "priceFrom": price_from,
            "priceTo": price_to,
            "zipCheck": str(zip_check).lower(),
            "track1": str(track1).lower(),
            "track2": str(track2).lower(),
            "pin": str(pin).lower(),
        }

        try:
            # Get authentication headers
            headers = build_default_headers_with_auth()

            response = await self.external_api.make_authenticated_request(
                method="POST",
                endpoint="/api/cards/dumps/list",
                params=params,
                headers=headers,
            )

            if response.get("success"):
                logger.info(
                    f"Successfully fetched {len(response.get('data', []))} dumps v1 items"
                )
                return response
            else:
                logger.error(
                    "Failed to fetch dumps v1 list", extra={"response": response}
                )
                return {"success": False, "error": "Failed to fetch dumps list"}

        except Exception as e:
            logger.error(f"Error fetching dumps v1 list: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def get_dump_orders(self, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """
        Get dumps v1 orders - equivalent to /api/cards/dumps/orders
        """
        logger.info("Fetching dumps v1 orders", extra={"page": page, "limit": limit})

        params = {"page": page, "limit": limit}

        try:
            headers = build_default_headers_with_auth()

            response = await self.external_api.make_authenticated_request(
                method="GET",
                endpoint="/api/cards/dumps/orders",
                params=params,
                headers=headers,
            )

            if response.get("success"):
                logger.info(
                    f"Successfully fetched {len(response.get('data', []))} dumps v1 orders"
                )
                return response
            else:
                logger.error(
                    "Failed to fetch dumps v1 orders", extra={"response": response}
                )
                return {"success": False, "error": "Failed to fetch dumps orders"}

        except Exception as e:
            logger.error(f"Error fetching dumps v1 orders: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def download_single_dump(self, order_id: int) -> Dict[str, Any]:
        """
        Download single dump - equivalent to /api/cards/dumps/download/single
        """
        logger.info("Downloading single dump", extra={"order_id": order_id})

        try:
            headers = build_default_headers_with_auth()
            headers["content-type"] = "application/json"

            data = {"_id": order_id}

            response = await self.external_api.make_authenticated_request(
                method="POST",
                endpoint="/api/cards/dumps/download/single",
                data=data,
                headers=headers,
            )

            # The response is typically CSV format or direct dump data
            logger.info("Successfully downloaded dump data")
            return {"success": True, "data": response}

        except Exception as e:
            logger.error(f"Error downloading single dump: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    # VDUMPS V2 API Methods
    async def list_vdumps(
        self,
        page: int = 1,
        limit: int = 10,
        base: str = "",
        bank: str = "",
        bin: str = "",
        country: str = "",
        state: str = "",
        city: str = "",
        brand: str = "",
        type: str = "",
        zip: str = "",
        price_from: float = 0,
        price_to: float = 500,
        zip_check: bool = False,
        track1: bool = False,
        track2: bool = False,
        pin: bool = False,
    ) -> Dict[str, Any]:
        """
        List vdumps v2 - equivalent to /api/cards/vdumps/list
        """
        logger.info(
            "Fetching vdumps v2 list",
            extra={
                "page": page,
                "limit": limit,
                "filters": {
                    "base": base,
                    "bank": bank,
                    "bin": bin,
                    "country": country,
                    "state": state,
                    "city": city,
                    "brand": brand,
                    "type": type,
                    "zip": zip,
                    "price_from": price_from,
                    "price_to": price_to,
                    "zip_check": zip_check,
                    "track1": track1,
                    "track2": track2,
                    "pin": pin,
                },
            },
        )

        # Build query parameters
        params = {
            "page": page,
            "limit": limit,
            "base": base,
            "bank": bank,
            "bin": bin,
            "country": country,
            "state": state,
            "city": city,
            "brand": brand,
            "type": type,
            "zip": zip,
            "priceFrom": price_from,
            "priceTo": price_to,
            "zipCheck": str(zip_check).lower(),
            "track1": str(track1).lower(),
            "track2": str(track2).lower(),
            "pin": str(pin).lower(),
        }

        try:
            headers = build_default_headers_with_auth()

            response = await self.external_api.make_authenticated_request(
                method="POST",
                endpoint="/api/cards/vdumps/list",
                params=params,
                headers=headers,
            )

            if response.get("success"):
                logger.info(
                    f"Successfully fetched {len(response.get('data', []))} vdumps v2 items"
                )
                return response
            else:
                logger.error(
                    "Failed to fetch vdumps v2 list", extra={"response": response}
                )
                return {"success": False, "error": "Failed to fetch vdumps list"}

        except Exception as e:
            logger.error(f"Error fetching vdumps v2 list: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def buy_vdump(self, dump_id: int) -> Dict[str, Any]:
        """
        Buy vdump directly - equivalent to /api/cards/vdumps/buy
        """
        logger.info("Buying vdump v2", extra={"dump_id": dump_id})

        try:
            headers = build_default_headers_with_auth()
            headers["content-type"] = "application/json"

            data = {"id": dump_id}

            response = await self.external_api.make_authenticated_request(
                method="POST",
                endpoint="/api/cards/vdumps/buy",
                data=data,
                headers=headers,
            )

            if response.get("success"):
                logger.info("Successfully bought vdump v2")
                return response
            else:
                logger.error("Failed to buy vdump v2", extra={"response": response})
                return {"success": False, "error": "Failed to buy vdump"}

        except Exception as e:
            logger.error(f"Error buying vdump v2: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    # Cart operations (shared with existing cart service)
    async def add_dump_to_cart(
        self, dump_id: int, product_table_name: str = "dumps"
    ) -> Dict[str, Any]:
        """
        Add dump to cart - uses existing cart API endpoint
        product_table_name can be "dumps" for v1 or "vdumps" for v2
        """
        logger.info(
            "Adding dump to cart",
            extra={"dump_id": dump_id, "product_table_name": product_table_name},
        )

        try:
            headers = build_default_headers_with_auth()
            headers["content-type"] = "application/json"

            data = {"id": dump_id, "product_table_name": product_table_name}

            response = await self.external_api.make_authenticated_request(
                method="POST", endpoint="/api/cart/", data=data, headers=headers
            )

            if response.get("success"):
                logger.info("Successfully added dump to cart")
                return response
            else:
                logger.error("Failed to add dump to cart", extra={"response": response})
                return {"success": False, "error": "Failed to add dump to cart"}

        except Exception as e:
            logger.error(f"Error adding dump to cart: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def get_cart(self) -> Dict[str, Any]:
        """
        Get cart contents - uses existing cart API endpoint
        """
        logger.info("Fetching cart contents")

        try:
            headers = build_default_headers_with_auth()

            response = await self.external_api.make_authenticated_request(
                method="GET", endpoint="/api/cart/", headers=headers
            )

            if response.get("success"):
                logger.info(
                    f"Successfully fetched cart with {len(response.get('data', []))} items"
                )
                return response
            else:
                logger.error("Failed to fetch cart", extra={"response": response})
                return {"success": False, "error": "Failed to fetch cart"}

        except Exception as e:
            logger.error(f"Error fetching cart: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def checkout_cart(self) -> Dict[str, Any]:
        """
        Checkout cart - uses existing cart checkout API endpoint
        """
        logger.info("Checking out cart")

        try:
            headers = build_default_headers_with_auth()

            response = await self.external_api.make_authenticated_request(
                method="GET", endpoint="/api/cart/checkout", headers=headers
            )

            if response.get("success"):
                logger.info("Successfully checked out cart")
                return response
            else:
                logger.error("Failed to checkout cart", extra={"response": response})
                return {"success": False, "error": "Failed to checkout cart"}

        except Exception as e:
            logger.error(f"Error checking out cart: {e}", exc_info=True)
            return {"success": False, "error": str(e)}


# Global service instance
_dump_service_instance: Optional[DumpService] = None


def get_dump_service() -> DumpService:
    """Get global dump service instance"""
    global _dump_service_instance
    if _dump_service_instance is None:
        _dump_service_instance = DumpService()
    return _dump_service_instance


async def close_dump_service() -> None:
    """Close global dump service instance"""
    global _dump_service_instance
    if _dump_service_instance is not None:
        await _dump_service_instance.close()
        _dump_service_instance = None
