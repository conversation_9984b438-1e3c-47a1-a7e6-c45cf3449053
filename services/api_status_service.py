"""
API Status Monitoring Service

Tracks API health, availability, and provides status information to users.
"""

from __future__ import annotations

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)


class APIStatus(Enum):
    """API status states"""
    ONLINE = "online"
    OFFLINE = "offline"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


@dataclass
class APIHealthMetrics:
    """Metrics for API health tracking"""
    last_success_time: Optional[float] = None
    last_failure_time: Optional[float] = None
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    total_requests: int = 0
    total_failures: int = 0
    total_successes: int = 0
    average_response_time: float = 0.0
    last_error_message: Optional[str] = None
    last_status_code: Optional[int] = None
    
    def get_success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 0.0
        return (self.total_successes / self.total_requests) * 100
    
    def get_uptime_percentage(self, window_seconds: int = 3600) -> float:
        """Calculate uptime percentage in the last window"""
        if not self.last_success_time:
            return 0.0
        
        time_since_success = time.time() - self.last_success_time
        if time_since_success > window_seconds:
            return 0.0
        
        return 100.0 - (time_since_success / window_seconds * 100)


class APIStatusService:
    """
    Service for monitoring API status and health.
    
    Tracks API availability, response times, and error rates.
    Provides status information for display to users and admins.
    """
    
    # Singleton instance
    _instance: Optional[APIStatusService] = None
    
    # Status thresholds
    OFFLINE_THRESHOLD_FAILURES = 3  # Consecutive failures before marking offline
    DEGRADED_THRESHOLD_FAILURES = 2  # Consecutive failures before marking degraded
    TIMEOUT_THRESHOLD_SECONDS = 300  # 5 minutes without success = offline
    
    def __init__(self):
        """Initialize API status service"""
        self.metrics: Dict[str, APIHealthMetrics] = {
            "v1": APIHealthMetrics(),
            "v2": APIHealthMetrics(),
            "v3": APIHealthMetrics(),
        }
        self.current_version: str = "v3"  # Default
        self._status_cache: Dict[str, tuple[APIStatus, float]] = {}
        self._cache_ttl: float = 10.0  # Cache status for 10 seconds
        
        logger.info("API Status Service initialized")
    
    @classmethod
    def get_instance(cls) -> APIStatusService:
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def set_current_version(self, version: str) -> None:
        """Set the currently active API version"""
        if version in self.metrics:
            self.current_version = version
            logger.info(f"API version set to: {version}")
    
    def record_success(
        self,
        version: str,
        response_time: float = 0.0,
        status_code: int = 200
    ) -> None:
        """Record a successful API request"""
        if version not in self.metrics:
            logger.warning(f"Unknown API version: {version}")
            return
        
        metrics = self.metrics[version]
        metrics.last_success_time = time.time()
        metrics.consecutive_successes += 1
        metrics.consecutive_failures = 0
        metrics.total_requests += 1
        metrics.total_successes += 1
        metrics.last_status_code = status_code
        
        # Update average response time (exponential moving average)
        if metrics.average_response_time == 0:
            metrics.average_response_time = response_time
        else:
            metrics.average_response_time = (
                0.7 * metrics.average_response_time + 0.3 * response_time
            )
        
        # Clear cache
        self._invalidate_cache(version)
        
        logger.debug(
            f"API {version} success recorded: {response_time:.2f}s, "
            f"consecutive: {metrics.consecutive_successes}"
        )
    
    def record_failure(
        self,
        version: str,
        error_message: str,
        status_code: Optional[int] = None
    ) -> None:
        """Record a failed API request"""
        if version not in self.metrics:
            logger.warning(f"Unknown API version: {version}")
            return
        
        metrics = self.metrics[version]
        metrics.last_failure_time = time.time()
        metrics.consecutive_failures += 1
        metrics.consecutive_successes = 0
        metrics.total_requests += 1
        metrics.total_failures += 1
        metrics.last_error_message = error_message
        if status_code:
            metrics.last_status_code = status_code
        
        # Clear cache
        self._invalidate_cache(version)
        
        logger.warning(
            f"API {version} failure recorded: {error_message}, "
            f"consecutive: {metrics.consecutive_failures}"
        )
    
    def get_status(self, version: str) -> APIStatus:
        """Get current status for an API version"""
        if version not in self.metrics:
            return APIStatus.UNKNOWN
        
        # Check cache
        if version in self._status_cache:
            cached_status, cache_time = self._status_cache[version]
            if time.time() - cache_time < self._cache_ttl:
                return cached_status
        
        metrics = self.metrics[version]
        status = self._calculate_status(metrics)
        
        # Update cache
        self._status_cache[version] = (status, time.time())
        
        return status
    
    def _calculate_status(self, metrics: APIHealthMetrics) -> APIStatus:
        """Calculate API status based on metrics"""
        # Check if we have any data
        if metrics.total_requests == 0:
            return APIStatus.UNKNOWN
        
        # Check for consecutive failures
        if metrics.consecutive_failures >= self.OFFLINE_THRESHOLD_FAILURES:
            return APIStatus.OFFLINE
        
        if metrics.consecutive_failures >= self.DEGRADED_THRESHOLD_FAILURES:
            return APIStatus.DEGRADED
        
        # Check timeout (no success in last N seconds)
        if metrics.last_success_time:
            time_since_success = time.time() - metrics.last_success_time
            if time_since_success > self.TIMEOUT_THRESHOLD_SECONDS:
                return APIStatus.OFFLINE
        elif metrics.last_failure_time:
            # Never had success, only failures
            return APIStatus.OFFLINE
        
        # Check success rate
        success_rate = metrics.get_success_rate()
        if success_rate < 50:
            return APIStatus.DEGRADED
        
        return APIStatus.ONLINE
    
    def get_current_status(self) -> APIStatus:
        """Get status of currently active API version"""
        return self.get_status(self.current_version)
    
    def get_status_emoji(self, status: APIStatus) -> str:
        """Get emoji for status"""
        emoji_map = {
            APIStatus.ONLINE: "🟢",
            APIStatus.OFFLINE: "🔴",
            APIStatus.DEGRADED: "🟡",
            APIStatus.UNKNOWN: "⚪",
        }
        return emoji_map.get(status, "⚪")
    
    def get_status_text(self, status: APIStatus) -> str:
        """Get human-readable status text"""
        text_map = {
            APIStatus.ONLINE: "Online",
            APIStatus.OFFLINE: "Offline",
            APIStatus.DEGRADED: "Degraded",
            APIStatus.UNKNOWN: "Unknown",
        }
        return text_map.get(status, "Unknown")
    
    def get_status_message(self, version: Optional[str] = None) -> str:
        """Get formatted status message for display"""
        if version is None:
            version = self.current_version

        status = self.get_status(version)
        emoji = self.get_status_emoji(status)
        text = self.get_status_text(status)

        return f"{emoji} API {version.upper()}: {text}"

    def get_user_friendly_message(self, version: Optional[str] = None) -> str:
        """Get user-friendly status message without technical details"""
        if version is None:
            version = self.current_version

        status = self.get_status(version)

        if status == APIStatus.ONLINE:
            return "🟢 The API is currently online and working normally."
        elif status == APIStatus.DEGRADED:
            return "🟡 The API is experiencing some issues but may still work. Please try again."
        elif status == APIStatus.OFFLINE:
            return "🔴 The API is currently offline. Please try again later."
        else:  # UNKNOWN
            return "⚪ API status is unknown. Please try your request."
    
    def get_detailed_status(self, version: Optional[str] = None) -> Dict[str, Any]:
        """Get detailed status information"""
        if version is None:
            version = self.current_version
        
        if version not in self.metrics:
            return {"error": "Unknown API version"}
        
        metrics = self.metrics[version]
        status = self.get_status(version)
        
        # Calculate time since last success/failure
        time_since_success = None
        if metrics.last_success_time:
            time_since_success = int(time.time() - metrics.last_success_time)
        
        time_since_failure = None
        if metrics.last_failure_time:
            time_since_failure = int(time.time() - metrics.last_failure_time)
        
        return {
            "version": version,
            "status": status.value,
            "status_emoji": self.get_status_emoji(status),
            "status_text": self.get_status_text(status),
            "metrics": {
                "total_requests": metrics.total_requests,
                "total_successes": metrics.total_successes,
                "total_failures": metrics.total_failures,
                "success_rate": round(metrics.get_success_rate(), 2),
                "consecutive_failures": metrics.consecutive_failures,
                "consecutive_successes": metrics.consecutive_successes,
                "average_response_time": round(metrics.average_response_time, 2),
                "time_since_success": time_since_success,
                "time_since_failure": time_since_failure,
                "last_error": metrics.last_error_message,
                "last_status_code": metrics.last_status_code,
            }
        }
    
    def get_all_statuses(self) -> Dict[str, Dict[str, Any]]:
        """Get status for all API versions"""
        return {
            version: self.get_detailed_status(version)
            for version in self.metrics.keys()
        }
    
    def _invalidate_cache(self, version: str) -> None:
        """Invalidate status cache for a version"""
        if version in self._status_cache:
            del self._status_cache[version]
    
    def reset_metrics(self, version: str) -> None:
        """Reset metrics for an API version"""
        if version in self.metrics:
            self.metrics[version] = APIHealthMetrics()
            self._invalidate_cache(version)
            logger.info(f"Metrics reset for API {version}")


# Global instance getter
def get_api_status_service() -> APIStatusService:
    """Get the global API status service instance"""
    return APIStatusService.get_instance()

