"""
Static Filter Options Provider

Provides fallback filter options when dynamic API endpoints are unavailable.
This ensures the filter interface remains functional even when external APIs
have issues or missing endpoints.
"""

from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)


class StaticFilterOptionsProvider:
    """Provides static filter options as fallback when API endpoints fail"""
    
    # Country options with flag emojis
    COUNTRY_OPTIONS = [
        {"value": "US", "label": "🇺🇸 United States", "count": 1250},
        {"value": "CA", "label": "🇨🇦 Canada", "count": 890},
        {"value": "GB", "label": "🇬🇧 United Kingdom", "count": 750},
        {"value": "AU", "label": "🇦🇺 Australia", "count": 620},
        {"value": "DE", "label": "🇩🇪 Germany", "count": 580},
        {"value": "FR", "label": "🇫🇷 France", "count": 540},
        {"value": "IT", "label": "🇮🇹 Italy", "count": 480},
        {"value": "ES", "label": "🇪🇸 Spain", "count": 450},
        {"value": "NL", "label": "🇳🇱 Netherlands", "count": 420},
        {"value": "SE", "label": "🇸🇪 Sweden", "count": 380},
        {"value": "NO", "label": "🇳🇴 Norway", "count": 350},
        {"value": "DK", "label": "🇩🇰 Denmark", "count": 320},
        {"value": "FI", "label": "🇫🇮 Finland", "count": 290},
        {"value": "CH", "label": "🇨🇭 Switzerland", "count": 280},
        {"value": "AT", "label": "🇦🇹 Austria", "count": 260},
        {"value": "BE", "label": "🇧🇪 Belgium", "count": 240},
        {"value": "IE", "label": "🇮🇪 Ireland", "count": 220},
        {"value": "PT", "label": "🇵🇹 Portugal", "count": 200},
        {"value": "PL", "label": "🇵🇱 Poland", "count": 180},
        {"value": "CZ", "label": "🇨🇿 Czech Republic", "count": 160},
    ]
    
    # US State options
    STATE_OPTIONS = [
        {"value": "CA", "label": "California", "count": 320},
        {"value": "NY", "label": "New York", "count": 280},
        {"value": "TX", "label": "Texas", "count": 250},
        {"value": "FL", "label": "Florida", "count": 220},
        {"value": "IL", "label": "Illinois", "count": 180},
        {"value": "PA", "label": "Pennsylvania", "count": 160},
        {"value": "OH", "label": "Ohio", "count": 140},
        {"value": "GA", "label": "Georgia", "count": 130},
        {"value": "NC", "label": "North Carolina", "count": 120},
        {"value": "MI", "label": "Michigan", "count": 110},
        {"value": "NJ", "label": "New Jersey", "count": 100},
        {"value": "VA", "label": "Virginia", "count": 95},
        {"value": "WA", "label": "Washington", "count": 90},
        {"value": "AZ", "label": "Arizona", "count": 85},
        {"value": "MA", "label": "Massachusetts", "count": 80},
    ]
    
    # Card brand options
    BRAND_OPTIONS = [
        {"value": "VISA", "label": "Visa", "count": 2500},
        {"value": "MASTERCARD", "label": "Mastercard", "count": 2200},
        {"value": "AMEX", "label": "American Express", "count": 800},
        {"value": "DISCOVER", "label": "Discover", "count": 600},
        {"value": "DINERS", "label": "Diners Club", "count": 200},
        {"value": "JCB", "label": "JCB", "count": 150},
        {"value": "UNIONPAY", "label": "UnionPay", "count": 100},
    ]
    
    # Card type options
    TYPE_OPTIONS = [
        {"value": "CREDIT", "label": "Credit Card", "count": 4500},
        {"value": "DEBIT", "label": "Debit Card", "count": 2800},
        {"value": "PREPAID", "label": "Prepaid Card", "count": 1200},
        {"value": "GIFT", "label": "Gift Card", "count": 300},
    ]
    
    # Card level options
    LEVEL_OPTIONS = [
        {"value": "STANDARD", "label": "Standard", "count": 3200},
        {"value": "GOLD", "label": "Gold", "count": 2100},
        {"value": "PLATINUM", "label": "Platinum", "count": 1500},
        {"value": "SIGNATURE", "label": "Signature", "count": 800},
        {"value": "INFINITE", "label": "Infinite", "count": 400},
        {"value": "BLACK", "label": "Black", "count": 200},
    ]
    
    # Bank options (sample)
    BANK_OPTIONS = [
        {"value": "CHASE", "label": "Chase Bank", "count": 1200},
        {"value": "BOFA", "label": "Bank of America", "count": 1100},
        {"value": "WELLS", "label": "Wells Fargo", "count": 950},
        {"value": "CITI", "label": "Citibank", "count": 800},
        {"value": "CAPITAL_ONE", "label": "Capital One", "count": 750},
        {"value": "AMEX_BANK", "label": "American Express Bank", "count": 600},
        {"value": "DISCOVER_BANK", "label": "Discover Bank", "count": 500},
        {"value": "USBANK", "label": "US Bank", "count": 450},
        {"value": "PNC", "label": "PNC Bank", "count": 400},
        {"value": "TD", "label": "TD Bank", "count": 350},
    ]
    
    # City options (sample for major cities)
    CITY_OPTIONS = [
        {"value": "NEW_YORK", "label": "New York", "count": 450},
        {"value": "LOS_ANGELES", "label": "Los Angeles", "count": 380},
        {"value": "CHICAGO", "label": "Chicago", "count": 320},
        {"value": "HOUSTON", "label": "Houston", "count": 280},
        {"value": "PHOENIX", "label": "Phoenix", "count": 250},
        {"value": "PHILADELPHIA", "label": "Philadelphia", "count": 220},
        {"value": "SAN_ANTONIO", "label": "San Antonio", "count": 200},
        {"value": "SAN_DIEGO", "label": "San Diego", "count": 180},
        {"value": "DALLAS", "label": "Dallas", "count": 170},
        {"value": "SAN_JOSE", "label": "San Jose", "count": 160},
    ]
    
    # ZIP code options (sample)
    ZIP_OPTIONS = [
        {"value": "10001", "label": "10001 (New York, NY)", "count": 25},
        {"value": "90210", "label": "90210 (Beverly Hills, CA)", "count": 20},
        {"value": "60601", "label": "60601 (Chicago, IL)", "count": 18},
        {"value": "77001", "label": "77001 (Houston, TX)", "count": 15},
        {"value": "33101", "label": "33101 (Miami, FL)", "count": 12},
        {"value": "85001", "label": "85001 (Phoenix, AZ)", "count": 10},
        {"value": "19101", "label": "19101 (Philadelphia, PA)", "count": 8},
        {"value": "78201", "label": "78201 (San Antonio, TX)", "count": 7},
        {"value": "92101", "label": "92101 (San Diego, CA)", "count": 6},
        {"value": "75201", "label": "75201 (Dallas, TX)", "count": 5},
    ]
    
    @classmethod
    def get_filter_options(cls, filter_name: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Get static filter options for a given filter name
        
        Args:
            filter_name: Name of the filter (country, state, brand, etc.)
            filters: Current filter context (for dependent filters like state->city)
            
        Returns:
            List of filter options with value, label, and count
        """
        filters = filters or {}
        
        # Map filter names to static options
        filter_map = {
            "country": cls.COUNTRY_OPTIONS,
            "state": cls._get_state_options(filters),
            "city": cls._get_city_options(filters),
            "zip": cls._get_zip_options(filters),
            "brand": cls.BRAND_OPTIONS,
            "type": cls.TYPE_OPTIONS,
            "level": cls.LEVEL_OPTIONS,
            "bank": cls.BANK_OPTIONS,
        }
        
        options = filter_map.get(filter_name, [])
        logger.info(f"Providing {len(options)} static options for filter '{filter_name}'")
        return options
    
    @classmethod
    def _get_state_options(cls, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get state options based on selected country"""
        country = filters.get("country")
        
        if country == "US":
            return cls.STATE_OPTIONS
        elif country == "CA":
            return [
                {"value": "ON", "label": "Ontario", "count": 180},
                {"value": "QC", "label": "Quebec", "count": 150},
                {"value": "BC", "label": "British Columbia", "count": 120},
                {"value": "AB", "label": "Alberta", "count": 100},
                {"value": "MB", "label": "Manitoba", "count": 60},
                {"value": "SK", "label": "Saskatchewan", "count": 40},
                {"value": "NS", "label": "Nova Scotia", "count": 35},
                {"value": "NB", "label": "New Brunswick", "count": 30},
            ]
        else:
            # For other countries, return empty or generic regions
            return []
    
    @classmethod
    def _get_city_options(cls, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get city options based on selected state/country"""
        state = filters.get("state")
        country = filters.get("country")
        
        if country == "US" and state:
            # Return cities for specific states
            state_cities = {
                "CA": [
                    {"value": "LOS_ANGELES", "label": "Los Angeles", "count": 120},
                    {"value": "SAN_FRANCISCO", "label": "San Francisco", "count": 80},
                    {"value": "SAN_DIEGO", "label": "San Diego", "count": 60},
                    {"value": "SACRAMENTO", "label": "Sacramento", "count": 40},
                ],
                "NY": [
                    {"value": "NEW_YORK", "label": "New York City", "count": 200},
                    {"value": "BUFFALO", "label": "Buffalo", "count": 30},
                    {"value": "ROCHESTER", "label": "Rochester", "count": 25},
                    {"value": "ALBANY", "label": "Albany", "count": 20},
                ],
                "TX": [
                    {"value": "HOUSTON", "label": "Houston", "count": 90},
                    {"value": "DALLAS", "label": "Dallas", "count": 80},
                    {"value": "SAN_ANTONIO", "label": "San Antonio", "count": 70},
                    {"value": "AUSTIN", "label": "Austin", "count": 60},
                ],
            }
            return state_cities.get(state, cls.CITY_OPTIONS[:5])
        else:
            return cls.CITY_OPTIONS[:10]
    
    @classmethod
    def _get_zip_options(cls, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get ZIP code options based on selected city/state"""
        city = filters.get("city")
        state = filters.get("state")
        
        # For demo purposes, return sample ZIP codes
        # In a real implementation, this would be more sophisticated
        return cls.ZIP_OPTIONS[:10]
