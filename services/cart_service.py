"""
Cart service for managing shopping cart operations
"""

from __future__ import annotations

import html
import logging
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta, timezone

from database.connection import get_collection, database_transaction
from models import Cart, CartItem, CartStatus
from services.card_service import CardService
from services.external_api_service import get_external_api_service
from config.settings import get_settings
from utils.performance import monitor_performance

logger = logging.getLogger(__name__)


class CartService:
    """Service for managing shopping cart operations"""

    def __init__(self):
        self.settings = get_settings()
        self.carts_collection = get_collection("carts")
        self.cart_items_collection = get_collection("cart_items")
        self.users_collection = get_collection("users")
        self.card_service = CardService()
        self.external_api_service = get_external_api_service()

        # Cart configuration
        self.cart_expiry_hours = 24  # Cart expires after 24 hours
        self.max_cart_items = 50  # Maximum items per cart

        # No direct HTTP session; external_api_service handles HTTP/auth
        # Card data caching removed - cards should be provided from catalog handlers cache

    async def close(self) -> None:
        """Close child services if needed"""
        await self.card_service.close()

    @monitor_performance("get_or_create_cart")
    async def get_or_create_cart(self, user_id: str) -> Cart:
        """
        Get user's active cart or create a new one

        Args:
            user_id: User document ID

        Returns:
            User's active cart
        """
        try:
            # Look for existing active cart
            cart_doc = await self.carts_collection.find_one(
                {"user_id": user_id, "status": CartStatus.ACTIVE}
            )

            if cart_doc:
                cart = Cart.from_mongo(cart_doc)

                # Check if cart has expired
                if cart.is_expired():
                    await self._expire_cart(cart)
                    # Create new cart
                    return await self._create_new_cart(user_id)

                return cart

            # Create new cart
            return await self._create_new_cart(user_id)

        except Exception as e:
            logger.error(f"Error getting/creating cart for user {user_id}: {e}")
            raise

    async def _create_new_cart(self, user_id: str) -> Cart:
        """Create a new cart for user"""
        from datetime import timezone

        expiry_time = datetime.now(timezone.utc) + timedelta(
            hours=self.cart_expiry_hours
        )

        cart = Cart(user_id=user_id, status=CartStatus.ACTIVE, expires_at=expiry_time)

        result = await self.carts_collection.insert_one(cart.to_mongo())
        cart.id = result.inserted_id

        logger.info(f"Created new cart {cart.id} for user {user_id}")
        return cart

    async def _expire_cart(self, cart: Cart) -> None:
        """Mark cart as expired"""
        await self.carts_collection.update_one(
            {"_id": cart.id}, {"$set": {"status": CartStatus.EXPIRED}}
        )
        logger.info(f"Expired cart {cart.id}")

    @monitor_performance("add_to_cart")
    async def add_to_cart(
        self,
        user_id: str,
        card_id: int,
        quantity: int = 1,
        card_data: Optional[Dict[str, Any]] = None,
    ) -> Tuple[bool, str]:
        """
        Add a card to user's cart with improved performance and error handling

        Args:
            user_id: User document ID
            card_id: External card ID from API
            quantity: Number of items to add
            card_data: Optional card data to avoid re-fetching from API

        Returns:
            Tuple of (success, message)
        """
        start_time = datetime.now(timezone.utc)
        logger.info(
            f"Starting add_to_cart for user {user_id}, card {card_id}, quantity {quantity}"
        )

        try:
            async with database_transaction():
                # Get or create cart
                cart = await self.get_or_create_cart(user_id)

                # Check cart item limit using current count from collection
                try:
                    current_count = await self.cart_items_collection.count_documents(
                        {"user_id": user_id}
                    )
                except Exception:
                    current_count = len(cart.items or [])
                if current_count >= self.max_cart_items:
                    return False, f"Cart is full (maximum {self.max_cart_items} items)"

                # Check if item already exists in cart
                existing_item = await self.cart_items_collection.find_one(
                    {"user_id": user_id, "card_id": card_id}
                )

                if existing_item:
                    # Update quantity
                    new_quantity = existing_item.get("quantity", 0) + quantity
                    await self.cart_items_collection.update_one(
                        {"_id": existing_item["_id"]},
                        {
                            "$set": {"quantity": new_quantity},
                            "$currentDate": {"updated_at": True},
                        },
                    )

                    # Ensure the cart.items includes this item id
                    existing_item_id_str = str(existing_item.get("_id"))
                    if existing_item_id_str not in (cart.items or []):
                        await self.carts_collection.update_one(
                            {"_id": cart.id},
                            {"$addToSet": {"items": existing_item_id_str}},
                        )

                    message = f"Updated quantity to {new_quantity}"
                else:
                    # Use provided card_data or create fallback data if not provided
                    # OPTIMIZATION: Avoid API calls during add-to-cart operations
                    if card_data is None:
                        logger.info(f"No card_data provided for card {card_id}, using fallback data")
                        # Create fallback data instead of making API calls
                        # This keeps add-to-cart operations fast and local-only
                        card_data = {
                            "_id": card_id,
                            "bank": f"Card #{card_id}",  # More informative fallback
                            "bin": "N/A",
                            "type": "CARD",
                            "price": 1.00,  # Default price for fallback cards
                            "country": "N/A",
                            "state": "N/A",
                            "city": "N/A",
                            "brand": "N/A",
                            "level": "N/A",
                            "zip": "N/A",
                            "_fallback": True,  # Mark as fallback data
                            "_note": "Card details will be updated at checkout",
                        }

                    # Ensure card_data has required fields
                    if not card_data.get("_id"):
                        card_data["_id"] = card_id

                    # Create new cart item
                    cart_item = CartItem(
                        user_id=user_id,
                        card_id=card_id,
                        card_data=card_data,
                        quantity=quantity,
                        price_at_add=self._extract_price_from_card_data(card_data),
                    )

                    result = await self.cart_items_collection.insert_one(
                        cart_item.to_mongo()
                    )
                    cart_item.id = result.inserted_id

                    # Add item ID to cart
                    await self.carts_collection.update_one(
                        {"_id": cart.id}, {"$push": {"items": str(cart_item.id)}}
                    )

                    message = f"Added {quantity} item(s) to cart"

                # Update cart total
                await self._update_cart_total(cart.id)

                # NOTE: We no longer add to external cart during browsing
                # External cart operations are now handled only during checkout queue processing

                # Log performance metrics
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()
                logger.info(
                    f"Successfully added card {card_id} to cart for user {user_id} in {duration:.2f}s"
                )

                if duration > 3.0:  # Warn if operation takes more than 3 seconds
                    logger.warning(
                        f"Slow add_to_cart operation: {duration:.2f}s for card {card_id}"
                    )

                return True, message

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()
            logger.error(
                f"Error adding card {card_id} to cart for user {user_id} after {duration:.2f}s: {e}"
            )
            return False, f"Failed to add item to cart: {str(e)}"

    # DISABLED: This method was causing excessive API calls during add-to-cart
    # Card data should be provided from catalog handlers cache instead
    async def _fetch_card_data_DISABLED(self, card_id: int | str) -> Optional[Dict[str, Any]]:
        """
        Fetch card data from the cards API with improved search strategy
        Uses intelligent pagination and caching for better performance
        """
        try:
            card_key = str(card_id)

            try:
                numeric_card_id = int(card_key)
            except (TypeError, ValueError):
                numeric_card_id = None

            # Check cache first
            if card_key in self._card_cache:
                cache_time = self._card_cache_timestamps.get(card_key)
                if (
                    cache_time
                    and datetime.now(timezone.utc) - cache_time < self._card_cache_ttl
                ):
                    logger.debug(f"Returning cached data for card ID {card_id}")
                    return self._card_cache[card_key]
                else:
                    # Remove expired cache entry
                    self._card_cache.pop(card_key, None)
                    self._card_cache_timestamps.pop(card_key, None)

            logger.debug(f"Searching for card ID {card_id}")

            # Improved search strategy: Try recent pages first, then broader search
            # Most cards are likely to be on recent pages (higher page numbers)
            search_strategies = [
                # Try recent pages first with larger limits for better coverage
                {"page": 1, "limit": 100},  # Most recent 100 cards
                {"page": 2, "limit": 100},  # Next 100 cards
                {"page": 3, "limit": 100},  # Next 100 cards
                {"page": 4, "limit": 100},  # Next 100 cards
                {"page": 5, "limit": 100},  # Next 100 cards
            ]

            # For very high card IDs, also try estimated pages
            if numeric_card_id is not None and numeric_card_id > 1_000_000:
                estimated_page = max(1, numeric_card_id // 50000)  # More conservative estimate
                search_strategies.extend(
                    [
                        {"page": estimated_page, "limit": 100},
                        {"page": estimated_page - 1, "limit": 100},
                        {"page": estimated_page + 1, "limit": 100},
                    ]
                )
                logger.debug(
                    f"Very high card ID detected, estimated page: {estimated_page}"
                )

            for strategy in search_strategies:
                try:
                    logger.debug(
                        f"Searching page {strategy['page']} with limit {strategy['limit']}"
                    )
                    cards_data = await self.card_service.fetch_cards(
                        page=strategy["page"], limit=strategy["limit"], filters={}
                    )

                    if cards_data.get("success"):
                        cards = cards_data.get("data", [])
                        logger.debug(
                            f"Retrieved {len(cards)} cards from page {strategy['page']}"
                        )

                        # Check if we found the card
                        for card in cards:
                            try:
                                if str(card.get("_id")) == str(card_id):
                                    logger.info(
                                        f"Found card {card_id} on page {strategy['page']}"
                                    )
                                    # Cache the found card
                                    self._card_cache[card_key] = card
                                    self._card_cache_timestamps[card_key] = datetime.now(
                                        timezone.utc
                                    )

                                    # Manage cache size periodically
                                    if (
                                        len(self._card_cache) % 100 == 0
                                    ):  # Cleanup every 100 entries
                                        self._manage_card_cache()

                                    return card
                            except Exception:
                                continue

                        # Log the range of IDs we searched for debugging
                        if cards:
                            try:
                                card_ids: List[int] = []
                                for result_card in cards:
                                    raw_id = result_card.get("_id")
                                    try:
                                        card_ids.append(int(str(raw_id)))
                                    except (TypeError, ValueError):
                                        continue

                                if card_ids:
                                    min_id = min(card_ids)
                                    max_id = max(card_ids)
                                    logger.debug(
                                        f"Page {strategy['page']}: ID range {min_id}-{max_id}"
                                    )
                            except Exception:
                                pass

                except Exception as e:
                    logger.warning(f"Failed to search page {strategy['page']}: {e}")
                    continue

            logger.error(f"Card {card_id} not found after comprehensive search")
            return None

        except Exception as e:
            logger.error(f"Error fetching card data for ID {card_id}: {e}")
            return None

    # REMOVED: _manage_card_cache method (no longer needed since _fetch_card_data is disabled)

    def _extract_price_from_card_data(self, card_data: Dict[str, Any]) -> float:
        """
        Safely extract price from card data, handling various formats

        Args:
            card_data: Card data dictionary

        Returns:
            Price as float, defaults to 1.00 if not found or invalid
        """
        try:
            price = card_data.get("price", 1.00)

            # Handle string prices
            if isinstance(price, str):
                # Remove any currency symbols and whitespace
                price_str = price.replace("$", "").replace("€", "").replace("£", "").strip()
                if price_str:
                    price = float(price_str)
                else:
                    price = 1.00

            # Handle numeric prices
            elif isinstance(price, (int, float)):
                price = float(price)

            # Ensure price is positive
            if price <= 0:
                logger.warning(f"Invalid price {price} in card data, using default 1.00")
                price = 1.00

            return round(price, 2)

        except (ValueError, TypeError) as e:
            logger.warning(f"Error extracting price from card data: {e}, using default 1.00")
            return 1.00

    async def _add_to_external_cart(self, card_id: int) -> bool:
        """Add item to external API cart using the consolidated external API service"""
        try:
            response = await self.external_api_service.add_to_cart(card_id, "Cards")

            if response.success:
                logger.info(f"Successfully added card {card_id} to external cart")
                return True
            else:
                logger.warning(
                    f"Failed to add card {card_id} to external cart: {response.error}"
                )
                return False

        except Exception as e:
            logger.error(f"Error adding to external cart: {e}")
            return False

    async def _update_cart_total(self, cart_id) -> None:
        """Update cart total amount"""
        try:
            # Get cart to find user_id
            cart_doc = await self.carts_collection.find_one({"_id": cart_id})
            if not cart_doc:
                # Try to coerce string to ObjectId if possible
                try:
                    from bson import ObjectId

                    if isinstance(cart_id, str) and ObjectId.is_valid(cart_id):
                        cart_doc = await self.carts_collection.find_one(
                            {"_id": ObjectId(cart_id)}
                        )
                except Exception:
                    pass
            if not cart_doc:
                return

            user_id = cart_doc["user_id"]

            # Get all cart items for this user
            cart_items = await self.cart_items_collection.find(
                {"user_id": user_id}
            ).to_list(None)

            # Calculate total
            total = sum(item["price_at_add"] * item["quantity"] for item in cart_items)

            # Update cart
            await self.carts_collection.update_one(
                {"_id": cart_id},
                {
                    "$set": {"total_amount": round(total, 2)},
                    "$currentDate": {"updated_at": True},
                },
            )

        except Exception as e:
            logger.error(f"Error updating cart total for cart {cart_id}: {e}")

    @monitor_performance("remove_from_cart")
    async def remove_from_cart(self, user_id: str, card_id: int) -> Tuple[bool, str]:
        """
        Remove a card from user's cart

        Args:
            user_id: User document ID
            card_id: External card ID to remove

        Returns:
            Tuple of (success, message)
        """
        try:
            async with database_transaction():
                # Find cart item
                cart_item = await self.cart_items_collection.find_one(
                    {"user_id": user_id, "card_id": card_id}
                )

                if not cart_item:
                    return False, "Item not found in cart"

                # Remove cart item
                await self.cart_items_collection.delete_one({"_id": cart_item["_id"]})

                # Remove from cart items list
                await self.carts_collection.update_one(
                    {"user_id": user_id, "status": CartStatus.ACTIVE},
                    {"$pull": {"items": str(cart_item["_id"])}},
                )

                # Update cart total
                cart = await self.get_or_create_cart(user_id)
                await self._update_cart_total(cart.id)

                logger.info(f"Removed card {card_id} from cart for user {user_id}")
                return True, "Item removed from cart"

        except Exception as e:
            logger.error(
                f"Error removing card {card_id} from cart for user {user_id}: {e}"
            )
            return False, f"Failed to remove item: {str(e)}"

    @monitor_performance("get_cart_contents")
    async def get_cart_contents(self, user_id: str) -> Dict[str, Any]:
        """
        Get user's cart contents with detailed information

        Args:
            user_id: User document ID

        Returns:
            Dictionary with cart information and items
        """
        try:
            cart = await self.get_or_create_cart(user_id)

            # Get cart items
            cart_items_docs = await self.cart_items_collection.find(
                {"user_id": user_id}
            ).to_list(None)

            cart_items = [CartItem.from_mongo(doc) for doc in cart_items_docs]

            # Calculate totals
            total_items = sum(item.quantity for item in cart_items)
            total_amount = sum(item.get_total_price() for item in cart_items)

            return {
                "cart": cart,
                "items": cart_items,
                "total_items": total_items,
                "total_amount": round(total_amount, 2),
                "is_empty": len(cart_items) == 0,
            }

        except Exception as e:
            logger.error(f"Error getting cart contents for user {user_id}: {e}")
            return {
                "cart": None,
                "items": [],
                "total_items": 0,
                "total_amount": 0.0,
                "is_empty": True,
                "error": str(e),
            }

    @monitor_performance("clear_cart")
    async def clear_cart(self, user_id: str) -> Tuple[bool, str]:
        """
        Clear all items from user's cart

        Args:
            user_id: User document ID

        Returns:
            Tuple of (success, message)
        """
        try:
            async with database_transaction():
                # Remove all cart items
                result = await self.cart_items_collection.delete_many(
                    {"user_id": user_id}
                )

                # Update cart
                await self.carts_collection.update_one(
                    {"user_id": user_id, "status": CartStatus.ACTIVE},
                    {
                        "$set": {"items": [], "total_amount": 0.0},
                        "$currentDate": {"updated_at": True},
                    },
                )

                items_removed = result.deleted_count
                logger.info(
                    f"Cleared cart for user {user_id}, removed {items_removed} items"
                )

                if items_removed > 0:
                    return True, f"Removed {items_removed} item(s) from cart"
                else:
                    return True, "Cart was already empty"

        except Exception as e:
            logger.error(f"Error clearing cart for user {user_id}: {e}")
            return False, f"Failed to clear cart: {str(e)}"

    @monitor_performance("update_cart_item_quantity")
    async def update_cart_item_quantity(
        self, user_id: str, card_id: int, new_quantity: int
    ) -> Tuple[bool, str]:
        """
        Update quantity of a cart item

        Args:
            user_id: User document ID
            card_id: External card ID
            new_quantity: New quantity (0 to remove)

        Returns:
            Tuple of (success, message)
        """
        try:
            if new_quantity < 0:
                return False, "Quantity cannot be negative"

            if new_quantity == 0:
                return await self.remove_from_cart(user_id, card_id)

            async with database_transaction():
                # Update cart item quantity
                result = await self.cart_items_collection.update_one(
                    {"user_id": user_id, "card_id": card_id},
                    {
                        "$set": {"quantity": new_quantity},
                        "$currentDate": {"updated_at": True},
                    },
                )

                if result.matched_count == 0:
                    return False, "Item not found in cart"

                # Update cart total
                cart = await self.get_or_create_cart(user_id)
                await self._update_cart_total(cart.id)

                logger.info(
                    f"Updated cart item {card_id} quantity to {new_quantity} for user {user_id}"
                )
                return True, f"Updated quantity to {new_quantity}"

        except Exception as e:
            logger.error(f"Error updating cart item quantity for user {user_id}: {e}")
            return False, f"Failed to update quantity: {str(e)}"

    def format_cart_for_display(self, cart_contents: Dict[str, Any]) -> str:
        """
        Format cart contents for display in Telegram with enhanced formatting

        Args:
            cart_contents: Result from get_cart_contents()

        Returns:
            Formatted string for display
        """
        try:
            if cart_contents.get("is_empty", True):
                return (
                    "🛒 <b>Your Cart</b>\n\n"
                    "📭 Your cart is empty.\n"
                    "🔍 Browse the catalog to add items!\n\n"
                    "💡 <i>Tip: Use /catalog to start shopping</i>"
                )

            items = cart_contents.get("items", [])
            total_items = cart_contents.get("total_items", 0)
            total_amount = cart_contents.get("total_amount", 0.0)
            cart = cart_contents.get("cart")

            # Header with cart summary
            cart_text = f"🛒 <b>Your Cart</b>\n"
            cart_text += (
                f"📦 <b>{total_items}</b> items • 💵 <b>${total_amount:.2f}</b>\n"
            )
            cart_text += "─" * 30 + "\n\n"

            def _safe_text(value: Any, limit: int | None = None) -> str:
                if value is None:
                    return ""
                text = html.escape(str(value).strip())
                if not text:
                    return ""
                if limit and len(text) > limit:
                    return text[: limit - 1] + "…"
                return text

            def _truthy_flag(value: Any) -> bool:
                if value is None:
                    return False
                if isinstance(value, bool):
                    return value
                if isinstance(value, (int, float)):
                    return value == 1
                if isinstance(value, str):
                    return value.strip().lower() in {
                        "1",
                        "yes",
                        "true",
                        "y",
                        "present",
                        "included",
                        "available",
                        "✅",
                        "✔",
                    }
                return False

            # Display each item with enhanced formatting
            for i, item in enumerate(items, 1):
                card_data = item.card_data
                bin_number = _safe_text(card_data.get("bin", "N/A")) or "N/A"
                card_type_raw = card_data.get("type")
                card_type = _safe_text(card_type_raw)
                country_raw = card_data.get("country")
                country = _safe_text(country_raw)
                price = item.price_at_add
                quantity = item.quantity
                item_total = item.get_total_price()

                # Check if this is fallback data
                is_fallback = card_data.get("_fallback", False)
                fallback_indicator = " ⚠️" if is_fallback else ""

                primary_label = next(
                    (
                        card_data.get(field)
                        for field in ["bank", "name", "brand", "base"]
                        if card_data.get(field)
                    ),
                    "Card",
                )
                bank_display = _safe_text(primary_label, 40) or "Card"

                holder_text = _safe_text(
                    next(
                        (
                            card_data.get(field)
                            for field in [
                                "name",
                                "cardholder",
                                "holder",
                                "full_name",
                            ]
                            if card_data.get(field)
                        ),
                        None,
                    ),
                    40,
                )

                base_value = _safe_text(card_data.get("base"), 80)
                brand_value = _safe_text(card_data.get("brand"))
                level_value = _safe_text(card_data.get("level"))
                quality_value = _safe_text(card_data.get("quality"))
                phone_value = _safe_text(card_data.get("phone"))
                price_text_value = _safe_text(card_data.get("price_text"), 80)

                city_value = _safe_text(card_data.get("city"))
                state_value = _safe_text(card_data.get("state"))
                zip_value = _safe_text(card_data.get("zip"))

                location_parts = [
                    part
                    for part in [city_value, state_value, zip_value, country]
                    if part
                ]

                # Format item display (hide internal card ID from user)
                primary_label = next(
                    (
                        card_data.get(field)
                        for field in ["bank", "name", "brand", "base"]
                        if card_data.get(field)
                    ),
                    "Card",
                )
                cart_text += f"<b>{i}.</b> 💳 <b>{primary_label}</b>{fallback_indicator}\n"

                cart_text += f"🏦 <b>{bank_display}</b>\n"

                if holder_text:
                    cart_text += f"👤 {holder_text}\n"

                if base_value:
                    cart_text += f"🧬 {base_value}\n"

                line_parts = [f"🔢 BIN: <code>{bin_number}</code>"]
                if card_type:
                    line_parts.append(f"📊 {card_type}")
                if brand_value:
                    brand_line = brand_value
                    if level_value:
                        brand_line = f"{brand_line} • {level_value}"
                    line_parts.append(f"💳 {brand_line}")
                elif level_value:
                    line_parts.append(f"⭐ {level_value}")

                cart_text += " • ".join(line_parts) + "\n"

                if location_parts:
                    cart_text += f"📍 {' • '.join(location_parts)}\n"

                if quality_value:
                    cart_text += f"💎 Quality: <b>{quality_value}</b>\n"

                if phone_value:
                    cart_text += f"📞 <code>{phone_value}</code>\n"

                if price_text_value and price_text_value != f"${price:.2f}":
                    cart_text += f"🏷️ {price_text_value}\n"

                if _truthy_flag(card_data.get("dob")):
                    cart_text += "🎂 DOB Included\n"

                if _truthy_flag(card_data.get("refundable")):
                    cart_text += "♻️ Refundable: ✓\n"
                elif card_data.get("refundable") is not None:
                    cart_text += "♻️ Refundable: ✗\n"

                # Pricing and quantity
                cart_text += f"💰 ${price:.2f} × {quantity} = <b>${item_total:.2f}</b>\n"

                # Add fallback note if applicable
                if is_fallback:
                    cart_text += f"<i>⚠️ Card data will be updated at checkout</i>\n"

                cart_text += "\n"

            # Footer with total and actions
            cart_text += "─" * 30 + "\n"
            cart_text += f"💵 <b>Total: ${total_amount:.2f}</b>\n\n"

            # Add helpful information
            if cart and hasattr(cart, "expires_at"):
                from datetime import datetime, timezone

                time_left = cart.expires_at - datetime.now(timezone.utc)
                hours_left = int(time_left.total_seconds() // 3600)
                if hours_left > 0:
                    cart_text += f"⏰ <i>Cart expires in {hours_left} hours</i>\n"
                else:
                    cart_text += f"⏰ <i>Cart expires soon</i>\n"

            cart_text += f"🛍️ <i>Ready to checkout? Use the checkout button below!</i>"

            return cart_text

        except Exception as e:
            logger.error(f"Error formatting cart for display: {e}")
            return "❌ Error displaying cart contents"

    @monitor_performance("queue_checkout")
    async def queue_checkout(
        self, user_id: str, telegram_user_id: int
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Queue cart checkout for processing

        Args:
            user_id: User document ID
            telegram_user_id: Telegram user ID for notifications

        Returns:
            Tuple of (success, message, job_id)
        """
        try:
            # Import here to avoid circular imports
            from services.checkout_queue_service import CheckoutQueueService

            # Best-effort: ensure worker is running in environments where
            # startup services might not have been initialized.
            queue_service = CheckoutQueueService()
            try:
                await queue_service.start_worker()
            except Exception:
                # Continue even if starting the worker fails; queuing still works
                pass

            return await queue_service.queue_checkout(user_id, telegram_user_id)

        except Exception as e:
            logger.error(f"Error queuing checkout for user {user_id}: {e}")
            return False, f"Failed to queue checkout: {str(e)}", None

    # REMOVED: Deprecated checkout_cart method
    # Use queue_checkout instead for proper isolation and better error handling

    async def get_cart_item_count(self, user_id: str) -> int:
        """
        Get total number of items in user's cart (optimized for performance)

        Args:
            user_id: User document ID

        Returns:
            Total number of items in cart
        """
        try:
            # Use aggregation for better performance instead of fetching all items
            pipeline = [
                {"$match": {"user_id": user_id}},
                {"$group": {"_id": None, "total_quantity": {"$sum": "$quantity"}}},
            ]

            result = await self.cart_items_collection.aggregate(pipeline).to_list(1)
            if result:
                return result[0].get("total_quantity", 0)
            return 0

        except Exception as e:
            logger.error(f"Error getting cart item count for user {user_id}: {e}")
            # Fallback to simple count
            try:
                return await self.cart_items_collection.count_documents(
                    {"user_id": user_id}
                )
            except Exception:
                return 0

    async def get_cart_summary(self, user_id: str) -> Dict[str, Any]:
        """
        Get cart summary with minimal data for performance (count and total only)

        Args:
            user_id: User document ID

        Returns:
            Dictionary with cart summary
        """
        try:
            # Use aggregation to get both count and total in one query
            pipeline = [
                {"$match": {"user_id": user_id}},
                {
                    "$group": {
                        "_id": None,
                        "total_items": {"$sum": "$quantity"},
                        "total_amount": {
                            "$sum": {"$multiply": ["$price_at_add", "$quantity"]}
                        },
                        "unique_items": {"$sum": 1},
                    }
                },
            ]

            result = await self.cart_items_collection.aggregate(pipeline).to_list(1)
            if result:
                summary = result[0]
                return {
                    "total_items": summary.get("total_items", 0),
                    "total_amount": round(summary.get("total_amount", 0.0), 2),
                    "unique_items": summary.get("unique_items", 0),
                    "is_empty": summary.get("total_items", 0) == 0,
                }

            return {
                "total_items": 0,
                "total_amount": 0.0,
                "unique_items": 0,
                "is_empty": True,
            }

        except Exception as e:
            logger.error(f"Error getting cart summary for user {user_id}: {e}")
            return {
                "total_items": 0,
                "total_amount": 0.0,
                "unique_items": 0,
                "is_empty": True,
            }

    async def cleanup_expired_carts(self) -> int:
        """
        Clean up expired carts (background task)

        Returns:
            Number of carts cleaned up
        """
        try:
            current_time = datetime.now(timezone.utc)

            # Find expired carts - compare with both timezone-aware and naive times
            # to handle cases where database stores naive datetimes
            current_time_naive = current_time.replace(tzinfo=None)

            expired_carts = await self.carts_collection.find(
                {
                    "status": CartStatus.ACTIVE,
                    "$or": [
                        {"expires_at": {"$lt": current_time}},
                        {"expires_at": {"$lt": current_time_naive}},
                    ],
                }
            ).to_list(None)

            cleaned_count = 0
            for cart_doc in expired_carts:
                cart = Cart.from_mongo(cart_doc)
                await self._expire_cart(cart)
                cleaned_count += 1

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired carts")

            return cleaned_count

        except Exception as e:
            logger.error(f"Error cleaning up expired carts: {e}")
            return 0
