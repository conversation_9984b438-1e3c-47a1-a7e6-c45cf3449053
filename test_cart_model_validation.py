#!/usr/bin/env python3
"""
Test script to verify that the CartItem model validation is working correctly
with string card IDs and minimal required fields.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.catalog import CartItem
from pydantic import ValidationError


def test_cart_item_with_string_id():
    """Test that CartItem accepts string card IDs."""
    print("🧪 Testing CartItem with String Card ID")
    print("=" * 50)
    
    # Test card ID (the problematic one from the error)
    test_card_id = "03faed8f5a9b80fd0c074c423138ee110b1143a3"
    
    # Minimal card data (only _id is required now)
    test_card_data = {
        "_id": test_card_id,
        "price": 12.0
    }
    
    try:
        # This should work now
        cart_item = CartItem(
            user_id="test_user",
            card_id=test_card_id,  # String card ID
            card_data=test_card_data,
            quantity=1,
            price_at_add=12.0,
            currency="USD"
        )
        
        print(f"✅ CartItem created successfully!")
        print(f"   Card ID: {cart_item.card_id}")
        print(f"   Card ID type: {type(cart_item.card_id).__name__}")
        print(f"   Card data: {cart_item.card_data}")
        print(f"   Total price: ${cart_item.get_total_price()}")
        
        return True
        
    except ValidationError as e:
        print(f"❌ Validation error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_cart_item_with_integer_id():
    """Test that CartItem coerces integer card IDs to strings."""
    print("\n🧪 Testing CartItem with Integer Card ID (should coerce)")
    print("=" * 50)
    
    # Integer card ID (should coerce)
    test_card_id = 123456789
    
    # Minimal card data
    test_card_data = {
        "_id": str(test_card_id),
        "price": 12.0
    }
    
    try:
        cart_item = CartItem(
            user_id="test_user",
            card_id=test_card_id,  # Integer card ID (should coerce)
            card_data=test_card_data,
            quantity=1,
            price_at_add=12.0,
            currency="USD"
        )

        print(f"✅ CartItem accepted integer ID and coerced to string")
        print(f"   Card ID: {cart_item.card_id}")
        print(f"   Card ID type: {type(cart_item.card_id).__name__}")

        if cart_item.card_id != str(test_card_id):
            print("❌ Card ID not coerced correctly")
            return False

        return True

    except ValidationError as e:
        print(f"❌ Unexpected validation error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_cart_item_missing_required_fields():
    """Test that CartItem validates required fields correctly."""
    print("\n🧪 Testing CartItem with Missing Required Fields")
    print("=" * 50)
    
    test_card_id = "03faed8f5a9b80fd0c074c423138ee110b1143a3"
    
    # Card data missing _id (should fail)
    test_card_data = {
        "price": 12.0
        # Missing "_id"
    }
    
    try:
        cart_item = CartItem(
            user_id="test_user",
            card_id=test_card_id,
            card_data=test_card_data,  # Missing required _id field
            quantity=1,
            price_at_add=12.0,
            currency="USD"
        )
        
        print(f"❌ Unexpected success: CartItem should require _id in card_data")
        return False
        
    except ValidationError as e:
        print(f"✅ Expected validation error for missing _id: {e}")
        return True
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_cart_item_with_optional_fields():
    """Test that CartItem works with optional fields."""
    print("\n🧪 Testing CartItem with Optional Fields")
    print("=" * 50)
    
    test_card_id = "03faed8f5a9b80fd0c074c423138ee110b1143a3"
    
    # Card data with optional fields (bank is no longer required)
    test_card_data = {
        "_id": test_card_id,
        "bin": "123456",
        "name": "Test Card",
        "country": "Test Country",
        "price": 12.0,
        "brand": "Test Brand",
        "type": "Test Type",
        "level": "Test Level",
        "quality": "Test Quality",
        "expiry": "12/25"
        # Note: "bank" is not included (should be OK now)
    }
    
    try:
        cart_item = CartItem(
            user_id="test_user",
            card_id=test_card_id,
            card_data=test_card_data,
            quantity=2,
            price_at_add=12.0,
            currency="USD"
        )
        
        print(f"✅ CartItem created successfully with optional fields!")
        print(f"   Card ID: {cart_item.card_id}")
        print(f"   Quantity: {cart_item.quantity}")
        print(f"   Total price: ${cart_item.get_total_price()}")
        print(f"   Card data keys: {list(cart_item.card_data.keys())}")
        
        return True
        
    except ValidationError as e:
        print(f"❌ Validation error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Run all validation tests."""
    print("🚀 Starting CartItem Model Validation Tests")
    print("=" * 60)
    
    test1_result = test_cart_item_with_string_id()
    test2_result = test_cart_item_with_integer_id()
    test3_result = test_cart_item_missing_required_fields()
    test4_result = test_cart_item_with_optional_fields()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"String Card ID Test: {'✅ PASSED' if test1_result else '❌ FAILED'}")
    print(f"Integer Card ID Test: {'✅ PASSED' if test2_result else '❌ FAILED'}")
    print(f"Missing Required Fields Test: {'✅ PASSED' if test3_result else '❌ FAILED'}")
    print(f"Optional Fields Test: {'✅ PASSED' if test4_result else '❌ FAILED'}")
    
    overall_success = test1_result and test2_result and test3_result and test4_result
    print(f"\nOverall Result: {'🎉 ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n✅ CartItem model validation is working correctly!")
        print("   ✓ Accepts string card IDs")
        print("   ✓ Rejects integer card IDs")
        print("   ✓ Validates required fields (_id)")
        print("   ✓ Works with optional fields (bank no longer required)")
        print("\n🎯 The Pydantic validation errors should now be resolved!")
    else:
        print("\n❌ Some validation tests failed. Please review the errors above.")
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
