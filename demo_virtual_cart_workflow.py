#!/usr/bin/env python3
"""
Virtual Cart Workflow Demonstration

This script demonstrates the complete virtual cart workflow:
1. Browse available items
2. Add items to virtual cart
3. Manage cart (view, modify quantities)
4. Checkout (transfer to API cart with validation)
5. Display detailed comparison results
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv
load_dotenv()

from api_v3.services.virtual_cart import get_virtual_cart
from api_v3.services.checkout_service import APIV3CheckoutService
from api_v3.services.browse_service import APIV3BrowseService, APIV3BrowseParams
from api_v3.config import get_api_v3_config_from_env

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VirtualCartDemo:
    """Demonstration of virtual cart workflow."""
    
    def __init__(self):
        self.config = get_api_v3_config_from_env()
        if not self.config:
            raise ValueError("API v3 configuration not found")
        
        self.browse_service = None
        self.checkout_service = None
        self.demo_user_id = "demo_user"
    
    async def setup(self):
        """Setup demo services."""
        logger.info("🔧 Setting up demo services")
        
        self.browse_service = APIV3BrowseService(
            base_url=self.config.base_url,
            username=self.config.username,
            password=self.config.password,
            use_socks_proxy=self.config.use_socks_proxy,
            socks_url=self.config.socks_url,
        )
        
        self.checkout_service = APIV3CheckoutService(
            base_url=self.config.base_url,
            username=self.config.username,
            password=self.config.password,
            use_socks_proxy=self.config.use_socks_proxy,
            socks_url=self.config.socks_url,
        )
        
        logger.info("✅ Demo services initialized")
    
    async def cleanup(self):
        """Cleanup demo services."""
        if self.browse_service:
            await self.browse_service.close()
        if self.checkout_service:
            await self.checkout_service.close()
        logger.info("🧹 Demo cleanup completed")
    
    def display_items(self, items, title="Items"):
        """Display items in a formatted table."""
        logger.info(f"\n📋 {title}")
        logger.info("-" * 80)
        logger.info(f"{'#':<3} {'ID':<10} {'BIN':<8} {'Country':<15} {'Price':<8} {'Brand':<12}")
        logger.info("-" * 80)
        
        for i, item in enumerate(items[:10], 1):  # Show first 10
            item_id = item.get("_id", "")[:10]
            bin_val = item.get("bin", "")
            country = item.get("country", "")[:15]
            price = f"${item.get('price', 0):.2f}"
            brand = item.get("brand", "")[:12]
            
            logger.info(f"{i:<3} {item_id:<10} {bin_val:<8} {country:<15} {price:<8} {brand:<12}")
        
        if len(items) > 10:
            logger.info(f"... and {len(items) - 10} more items")
    
    def display_virtual_cart(self, virtual_cart):
        """Display virtual cart contents."""
        items = virtual_cart.get_items()
        summary = virtual_cart.get_summary()
        
        logger.info(f"\n🛒 Virtual Cart Contents")
        logger.info("-" * 80)
        
        if not items:
            logger.info("   (Empty)")
            return
        
        logger.info(f"{'Item':<15} {'BIN':<8} {'Qty':<5} {'Price':<8} {'Total':<8}")
        logger.info("-" * 80)
        
        for item in items:
            item_name = item.name[:15] if item.name else item.item_id[:15]
            logger.info(f"{item_name:<15} {item.bin:<8} {item.quantity:<5} ${item.price:<7.2f} ${item.total_price:<7.2f}")
        
        logger.info("-" * 80)
        logger.info(f"Total Items: {summary.total_items} | Unique: {summary.unique_items} | Total: ${summary.total_price:.2f}")
    
    def display_checkout_results(self, checkout_result):
        """Display detailed checkout results."""
        logger.info(f"\n🏁 Checkout Results")
        logger.info("=" * 80)
        
        # Overall status
        status = "✅ SUCCESS" if checkout_result.success else "❌ FAILED"
        logger.info(f"Status: {status}")
        
        if checkout_result.error:
            logger.info(f"Error: {checkout_result.error}")
        
        # Virtual cart summary
        virtual_summary = checkout_result.virtual_cart_summary
        logger.info(f"\n📦 Virtual Cart Summary:")
        logger.info(f"   Items: {virtual_summary.get('total_items', 0)}")
        logger.info(f"   Total: ${virtual_summary.get('total_price', 0):.2f}")
        
        # API cart result
        api_result = checkout_result.api_cart_result
        logger.info(f"\n🔗 API Cart Transfer:")
        logger.info(f"   Success: {'✅' if api_result.get('success') else '❌'}")
        if api_result.get('message'):
            logger.info(f"   Message: {api_result['message']}")
        
        # Validation results
        validation = checkout_result.validation_result
        logger.info(f"\n🔍 Validation Results:")
        logger.info(f"   Overall: {'✅ PASSED' if validation.success else '❌ FAILED'}")
        logger.info(f"   Item Count Match: {'✅' if validation.item_count_match else '❌'}")
        logger.info(f"   Item IDs Match: {'✅' if validation.item_ids_match else '❌'}")
        logger.info(f"   Price Match: {'✅' if validation.price_match else '❌'}")
        
        logger.info(f"\n📊 Detailed Comparison:")
        logger.info(f"   Virtual Cart: {len(validation.virtual_items)} items, ${validation.total_virtual_price:.2f}")
        logger.info(f"   API Cart: {len(validation.api_items)} items, ${validation.total_api_price:.2f}")
        
        if validation.discrepancies:
            logger.info(f"\n⚠️  Discrepancies Found:")
            for discrepancy in validation.discrepancies:
                logger.info(f"   • {discrepancy}")
        
        # Order result (if created)
        if checkout_result.order_result:
            order_result = checkout_result.order_result
            logger.info(f"\n📦 Order Creation:")
            logger.info(f"   Success: {'✅' if order_result.get('success') else '❌'}")
            if order_result.get('order_id'):
                logger.info(f"   Order ID: {order_result['order_id']}")
    
    async def run_demo(self):
        """Run the complete virtual cart demonstration."""
        logger.info("🎬 Starting Virtual Cart Workflow Demonstration")
        logger.info("=" * 80)
        
        await self.setup()
        
        try:
            # Step 1: Browse available items
            logger.info("\n🔍 STEP 1: Browse Available Items")
            logger.info("=" * 50)
            
            browse_result = await self.browse_service.list_items(
                APIV3BrowseParams(limit=20), self.demo_user_id
            )
            
            if not browse_result.success:
                logger.error(f"❌ Failed to browse items: {browse_result.error}")
                return
            
            available_items = browse_result.data.get("data", [])
            logger.info(f"✅ Found {len(available_items)} available items")
            self.display_items(available_items, "Available Items")
            
            # Step 2: Setup virtual cart
            logger.info("\n🛒 STEP 2: Virtual Cart Management")
            logger.info("=" * 50)
            
            virtual_cart = get_virtual_cart(self.demo_user_id)
            virtual_cart.clear()  # Start fresh
            
            # Add some items to virtual cart
            selected_items = available_items[:5]  # Select first 5 items
            
            logger.info(f"📝 Adding {len(selected_items)} items to virtual cart...")
            
            for i, item in enumerate(selected_items):
                quantity = (i % 3) + 1  # Vary quantities: 1, 2, 3, 1, 2
                success = virtual_cart.add_item(item, quantity=quantity)
                
                if success:
                    item_name = item.get("name", item.get("_id", "Unknown"))[:20]
                    logger.info(f"   ✅ Added {quantity}x {item_name}")
                else:
                    logger.warning(f"   ⚠️  Failed to add item {i+1}")
            
            # Display virtual cart
            self.display_virtual_cart(virtual_cart)
            
            # Step 3: Modify cart (demonstrate quantity changes)
            logger.info("\n📝 STEP 3: Cart Modifications")
            logger.info("=" * 50)
            
            if len(selected_items) >= 2:
                # Remove one item completely
                first_item_id = selected_items[0].get("_id")
                virtual_cart.remove_item(first_item_id)
                logger.info(f"🗑️  Removed first item completely")
                
                # Reduce quantity of second item
                second_item_id = selected_items[1].get("_id")
                virtual_cart.remove_item(second_item_id, quantity=1)
                logger.info(f"📉 Reduced quantity of second item by 1")
                
                # Display updated cart
                self.display_virtual_cart(virtual_cart)
            
            # Step 4: Checkout process
            logger.info("\n🏁 STEP 4: Checkout Process")
            logger.info("=" * 50)
            
            logger.info("🚀 Starting checkout (transferring virtual cart to API cart)...")
            
            checkout_result = await self.checkout_service.checkout(
                user_id=self.demo_user_id,
                create_order=False,  # Don't create order in demo
                clear_virtual_cart=False,  # Keep for comparison
            )
            
            # Display detailed results
            self.display_checkout_results(checkout_result)
            
            # Step 5: Final verification
            logger.info("\n✅ STEP 5: Final Verification")
            logger.info("=" * 50)
            
            if checkout_result.success:
                logger.info("🎉 Virtual cart workflow completed successfully!")
                logger.info("📋 Summary of what was accomplished:")
                logger.info("   • Browsed available items from API")
                logger.info("   • Added items to virtual cart (local storage)")
                logger.info("   • Modified cart contents (add/remove)")
                logger.info("   • Transferred virtual cart to actual API cart")
                logger.info("   • Validated data integrity between virtual and API carts")
                logger.info("   • Provided detailed comparison results")
            else:
                logger.error("❌ Virtual cart workflow encountered issues")
                logger.error("🔍 Please review the validation results above")
            
        except Exception as e:
            logger.error(f"❌ Demo failed with exception: {e}", exc_info=True)
        
        finally:
            await self.cleanup()


async def main():
    """Main demo execution."""
    demo = VirtualCartDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
